import Endpoint from "@/common/endpoint";
import { APICall } from "@/services";
import { useMutation } from "react-query";

export type SignOutError = {
  message: string;
};

export type SignOutBodyType = {
  access_token: string;
};

/**
 * TODO
 */
export function useSignOut(
  onError: (error: SignOutError) => void,
  onSuccess: (data: any) => void,
) {
  const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.SIGN_OUT;

  return useMutation(
    (accessToken: string) => {
      // Get ID token from localStorage for Authorization header
      const idToken = typeof window !== "undefined" 
        ? localStorage.getItem("IDToken") || localStorage.getItem("IdToken")
        : null;
      const authToken = idToken || accessToken; // Fallback to access token if ID token not found
      
      console.log("Sign out API call:");
      console.log("- Access token exists:", !!accessToken);
      console.log("- ID token exists:", !!idToken);
      console.log("- Using auth token:", authToken ? authToken.substring(0, 20) + "..." : "null");
      
      return APICall({
        url,
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `${authToken}`,
        },
      })({
        body: { access_token: accessToken },
      });
    },
    { onError, onSuccess }
  );
}


