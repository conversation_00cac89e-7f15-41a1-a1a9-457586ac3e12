'use client'
import React from 'react'
import SummaryCardGroup from '../commonmodule/page'
import ResizableTable from '@/components/mf/ReportingToolTable'
import {useState} from 'react' 
import FullFunnelJourney from '@/components/ui/Funnel'
import { Card } from '@/components/ui/card'
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {useQuartileProgression,useFullFunnelJourney} from './apicallPage';
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from  "../../Filters/buildFilters";
import { useWastageFilters } from '../../Filters/useFilters';
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';


const Quartile_progression=()=> {
    const[sercterm,setSearchTerm] = useState('');
        const { selectedPackage } = usePackage();
        const { startDate, endDate } = useDateRange();
        const [isExporting, setIsExporting] = useState(false);
        const [exportType, setExportType] = useState<string | null>(null);
        
        
       const params = {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    };
    const [query, setQuery] = useState({
      publisher: ["all"],
      campaign: ["all"],
      channel: ["all"],
      fraud_category:["all"],
      fraud_sub_category:["all"],
      creative_id:["all"],
      sub_publisher:["all"],
      campaign_id:["all"],
    });
  
   
    const quartileparams={
        ...params,
        ...query,
    }
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
            const isReady =
  !!selectedPackage && !!startDate && !!endDate;
           const {data:quartileprogression ,isLoading:quartileLoading,error:quartileError} = useQuartileProgression(quartileparams,isReady || exportType !== 'quartile');
           const {data:fullfunneljourney ,isLoading:fullfunnelLoading,error:fullfunnelError} = useFullFunnelJourney(quartileparams,isReady);
    const quartileHeader =[     
    { title: "Quartile", key: "Quartile" },
        { title: "Unique Counts", key: "Unique Counts" },
        { title: "Unique Counts Distribution", key: "Unique Counts Distribution" },
        { title: "Impressions", key: "Impressions" },
        { title: "Impression Distribution ", key: "Impression Distribution" },
    ]
    const quartilereportData = quartileprogression?.data.map((item: any) => ({
        "Quartile": item.quartile,
        "Unique Counts": item.unique_counts,
        "Unique Counts Distribution":`${item.unique_counts_percentage}%`,
        "Impressions": item.impressions,
        "Impression Distribution":`${item.impressions_percentage}%`,
    })) ?? [];

const funnelSteps = fullfunneljourney?.map((item: any) => ({
  label: item.label,
  countLabel: item.countLabel,
  ivt: `${parseFloat(item.ivt).toFixed(2)}%`,
  arrowshadowcolor: item.arrowshadowcolor,
  circlebgcolor: item.circlebgcolor,
  cardquartilename: item.cardquartilename,
  cardquartilesubtitle: item.cardquartilesubtitle,

})) ?? [];



const filter = useWastageFilters(params, query);

const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
const handleExportClick = async (type: string) => {
  setExportType(type as any);
  setIsExporting(true);
};
useExportCsv({
  exportParams: params,
  queryParams: query,
  exportType,
  setExportType,
  isExporting,
  setIsExporting,
  endpointMap: {
    quartile: Endpoint.WebBrand.QUARTILE_PROGRESSION,
  },
  baseUrlMap: {
    quartile: process.env.NEXT_PUBLIC_WEB_BRAND!,
  },
});
  return (
      <div className=" w-full grid grid-col p-2 gap-2">
         <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                      <Filter filter={filter} onChange={handleFilterChange} />
                      </div>
        <SummaryCardGroup params={params} query={query} />
          <div className="gap-1 w-full"> 
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Quartile Progression</div>
  <div className="grid grid-cols-1 w-full gap-2">
    <ResizableTable
     isPaginated={true}
     columns={quartileHeader}
     data={quartilereportData}
     isSearchable={true}
     isTableDownload={true}
     onDownload={() => handleExportClick("quartile")}
     isUserTable={false}
     height={410}
      row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
     isLoading={quartileLoading}
     marginTop='0'
     row_height={20}
    />
    </div>
    </div>
     <div className="w-full">
  <div className="bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2">
    Full Funnel Journey
  </div>

<div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4 w-full p-2">    {funnelSteps.map((step, index) => (
     
        <FullFunnelJourney
          key={index} // Ensure unique keys for each FunnelStep component
          title={step.label}
              subTitleData={[
                   { label: "Count", value: step.countLabel },
                   { label: "IVT", value: step.ivt },
                 ]}
          circleBgColor={step.circlebgcolor}
          arrowShadowColor={step.arrowshadowcolor}
          cardquartilename={step.cardquartilename}
          cardquartilesubtitle={step.cardquartilesubtitle}
          isLoading={fullfunnelLoading}
          circleClassName='w-[120px] h-[120px]  lg:w-[100px] lg:h-[80px] xl:h-[120px] xl:w-[120px]'
          leftClassName="relative left-8 md:left-4 lg:left-1 xl:left-3"
          // Pass more props here if needed
        />
    ))}
  </div>
</div>

    </div>
  );
}

export default Quartile_progression;