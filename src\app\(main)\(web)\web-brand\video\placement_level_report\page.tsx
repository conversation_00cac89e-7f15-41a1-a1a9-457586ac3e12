'use client'
import React from 'react'
import SummaryCardGroup from '../commonmodule/page'
import ResizableTable from '@/components/mf/ReportingToolTable'
import {useState} from 'react' ;
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { usePlacementReport } from './apicallPage';
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from  "../../Filters/buildFilters";
import { useWastageFilters } from '../../Filters/useFilters';
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';
import { useDebounce } from '@/hooks/useDebounce';


const PlacementReport=()=> {
    const[sercterm,setSearchTerm] = useState('');
    const { selectedPackage } = usePackage();
    const { startDate, endDate } = useDateRange();
    const [isExporting, setIsExporting] = useState(false);
    const [exportType, setExportType] = useState<string | null>(null);
    const [limit, setLimit] = useState(10);
    const [pageNo, setPageNo] = useState(1);
    const debouncedSearchTerm = useDebounce(sercterm, 500); // 300ms delay
        
    const params = {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    };
    const [query, setQuery] = useState({
      publisher: ["all"],
      campaign: ["all"],
      fraud_category:["all"],
      fraud_sub_category:["all"],
      creative_id:["all"],
      sub_publisher:["all"],
      campaign_id:["all"],
      channel:["all"],
    });


    const placementparam={
        ...params,
        ...query,
        search_term:debouncedSearchTerm,
        limit:limit,
        page:pageNo,
    }
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
          
        const isReady =
  !!selectedPackage && !!startDate && !!endDate;
           const {data:placementreport ,isLoading:placementLoading,error:placementError} = usePlacementReport(placementparam,isReady ||  exportType !== 'placement');
           
    

    const palcementHeader =[     
    { title: "Platform", key: "Platform" },
    { title: "Video ID / Placement ID", key: "Video ID" },
    { title: "Impression %", key:"Impression %" },

    ]
   const PlacementreportData = Array.isArray(placementreport?.data)
  ? placementreport?.data?.map((item: any) => ({
      "Platform": item.publisher,
     "Video ID": item.videoid || "-", // fallback for empty title
      "Impression %": `${item.impression_percentage}%`,
    }))
  : [];
  

const filter = useWastageFilters(params, query);

const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
const handleExportClick = async (type: string) => {
  setExportType(type as any);
  setIsExporting(true);
};
useExportCsv({
  exportParams: params,
  queryParams: query,
  exportType,
  setExportType,
  isExporting,
  setIsExporting,
  endpointMap: {
    placement: Endpoint.WebBrand.SHOW_WISE_IMPRESSION,
  },
  baseUrlMap: {
    placement: process.env.NEXT_PUBLIC_WEB_BRAND!,
  },
});

  return (
      <div className=" w-full grid grid-col p-2 gap-2">
         <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                      <Filter filter={filter} onChange={handleFilterChange} />
                      </div>
        <SummaryCardGroup params={params} query={query} />
  <div className="grid grid-cols-1 w-full gap-2">
    <ResizableTable
     isPaginated={true}
     columns={palcementHeader}
     data={PlacementreportData}
     isSearchable={true}
     isTableDownload={true}
     onDownload={() => handleExportClick("placement")}
     isUserTable={false}
     height={410}
     onLimitChange={(limit: number) => setLimit(limit)}
    onPageChangeP={(page: number) => setPageNo(page)}
     isLoading={placementLoading}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
     marginTop='0'
     row_height={10}
     row_count={5}
     totalPages={placementreport?.pagination?.total_pages}
     pageNo={placementreport?.pagination?.page_number}
    />
    </div>
    </div>
  );
}

export default PlacementReport;