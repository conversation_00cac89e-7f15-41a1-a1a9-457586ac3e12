import Endpoint from "@/common/endpoint";
import { APICall } from "@/services";
import { useMutation } from "react-query";

export type ErrorResponse = {
  message: string;
};

export type SetMFAPreferenceUpdateBodyType = {
  access_token: string;
  enable_software_token_mfa: boolean;
};

export function useSetMFAPreferenceUpdate(
  onError: (err: ErrorResponse) => void,
  onSuccess: (data: unknown) => void,
) {
  const authDomain = process.env.NEXT_PUBLIC_UAM_DOMAIN|| "https://auth.mfilterit.com/";
  const url = authDomain + Endpoint.SET_MFA_PREFERENCE_UPDATE;
  return useMutation(
    APICall({
      url,
      method: "POST",
      headers: {
        Authorization: typeof window !== "undefined" ? localStorage.getItem("IDToken") || "" : "",
      },
    }),
    { onSuccess, onError },
  );
}