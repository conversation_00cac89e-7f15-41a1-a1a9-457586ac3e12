'use client';

import React, { useEffect, useState } from 'react';
import ResizableTable from '@/components/mf/Realtimetable';
import { Switch } from '@/components/ui/switch';
import { useApiCall } from '../../../queries/api_base';
import Endpoint from '../../../common/endpoint';
import { usePackage } from '@/components/mf/PackageContext';
import { ChannelSwitchList } from '@/components/ui/ChannelListswitch';
import DeleteDialog from '@/components/ui/deletedialog';
import { useToast } from "@/hooks/use-toast"
import ToastContent from "@/components/mf/ToastContent"
import { Card } from '@/components/ui/card';


interface ChannelStatus {
  [key: string]: boolean;
}

// Add mapping for blacklist types
const BLACKLIST_TYPE_MAP: Record<string, string> = {
  'IP': 'ip_blacklisting',
  'Placement': 'placement_blacklisting',
  'Audience Exclusion': 'audience_exclusion_blacklisting'
};

// Add mapping for action toggle payload
const ACTION_TOGGLE_MAP: Record<string, string> = {
  'IP': 'ip',
  'Placement': 'placement',
  'Audience Exclusion': 'audience_exclusion'
};

interface ToastData {
  type: "success" | "error";
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
}
export interface RealTimeApiItem {
  Blacklisting: 'ip' | 'placement' | 'audience_exclusion';
  action: boolean;
  status: 'Active' | 'InActive';
  channels: ChannelStatus;
  inserted_date: string;
}

interface ColumnGRD {
  title: any;
  key: keyof userDataRD;
}

interface userDataRD {
  BlackListing: string;
  Status: string;
  'Date on Enabled': string;
  'Checkbox-data': string;
  Action: string;
}

const RealTimeColumn: ColumnGRD[] = [
  { title: 'BlackListing', key: 'BlackListing' },
  { title: 'Status', key: 'Status' },
  { title: 'Date on Enabled', key: 'Date on Enabled' },
  { title: 'Channels', key: 'Checkbox-data' },
  { title: 'Active / Inactive', key: 'Action' }
];

interface BlacklistPayload {
  package_name: string;
  ip_blacklisting: {
    channels: string[];
    status: boolean | null;
  };
  placement_blacklisting: {
    channels: string[];
    status: boolean | null;
  };
  audience_exclusion_blacklisting: {
    channels: string[];
    status: boolean | null;
  };
  [key: string]: {
    channels: string[];
    status: boolean | null;
  } | string;
}

function RealTimeProtection() {
  const { toast } = useToast()
  const { selectedPackage } = usePackage();
  const [realTimeData, setRealTimeData] = useState<any[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionStatusMap, setActionStatusMap] = useState<Record<string, boolean>>({});
  const [isTogglingAction, setIsTogglingAction] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [toastData, setToastData] = useState<ToastData | null>(null)
  

  const [pendingToggle, setPendingToggle] = useState<{
    type: "action" | "channel";
    blacklistType: string;
    newStatus: boolean;
    channel?: string;
  } | null>(null);

  const [blacklistState, setBlacklistState] = useState<
    Record<string, { channels: string[]; status: boolean }>
  >({});

  // const getActiveChannels = (channels: ChannelStatus) =>
  //   Object.fromEntries(Object.entries(channels).filter(([_, v]) => v));


  const handleChannelToggle = ({
    blacklistType,
    channel,
    newStatus,
  }: {
    blacklistType: string;
    channel: string;
    newStatus: boolean;
  }) => {
    setPendingToggle({
      type: "channel",
      blacklistType,
      channel,
      newStatus,
    });
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (!pendingToggle || !selectedPackage) return;

    const { type, blacklistType, newStatus, channel } = pendingToggle;

    if (type === "action") {
      const actionType = ACTION_TOGGLE_MAP[blacklistType];
      if (!actionType) {
        console.error('Invalid blacklist type for action:', blacklistType);
        return;
      }

      const payload = {
        package_name: selectedPackage,
        blacklisting: actionType,
        action: newStatus
      };

      setIsTogglingAction(true);
      setIsSubmitting(true);

      triggerToggleAction(payload, {
        onSuccess: (res) => {
          setActionStatusMap((prev) => ({
            ...prev,
            [blacklistType]: newStatus,
          }));

          setToastData({
            type: "success",
            title: "Success",
            description: res.message,
            variant: "default"
          });

          triggerFetchRealTime({ package_name: selectedPackage });
          setDeleteDialogOpen(false);
          setPendingToggle(null);
          setIsTogglingAction(false);
          setIsSubmitting(false);
        },
        onError: (err:any) => {
            const errorMessage =
    err?.response?.data?.error?.message || // For Axios-style errors
    err?.error?.message ||                 // For directly structured errors
    "An unexpected error occurred";
          setToastData({
            type: "error",
            title: "Error",
            description: errorMessage,
            variant: "default"
          });
        }
      });
    } else if (type === "channel") {
      // Get current state for all blacklisting types
      const currentState = { ...blacklistState };
      
      // Get the API key for the blacklist type
      const apiKey = BLACKLIST_TYPE_MAP[blacklistType];
      if (!apiKey) {
        console.error('Invalid blacklist type:', blacklistType);
        return;
      }

      // Get current channels for this blacklist type and ensure they are strings
      const currentChannels = (currentState[apiKey]?.channels || []).filter((c): c is string => typeof c === 'string');
      
      // Update channels based on the toggle action
      const updatedChannels: string[] = newStatus 
        ? Array.from(new Set([...currentChannels, channel]))
        : currentChannels.filter((c: string) => c !== channel);

      // Create the payload with the correct structure
      const payload: BlacklistPayload = {
        package_name: selectedPackage,
        ip_blacklisting: {
          channels: (blacklistState['ip_blacklisting']?.channels || []).filter((c): c is string => typeof c === 'string'),
          status: blacklistState['ip_blacklisting']?.status || false
        },
        placement_blacklisting: {
          channels: ["google"],
          status: blacklistState['placement_blacklisting']?.status || false
        },
        audience_exclusion_blacklisting: {
          channels: (blacklistState['audience_exclusion_blacklisting']?.channels || []).filter((c): c is string => typeof c === 'string'),
          status: blacklistState['audience_exclusion_blacklisting']?.status || false
        }
      };

      // Update the specific blacklist type with the new channel status
      if (apiKey && channel) {
        payload[apiKey] = {
          channels: updatedChannels,
          status: newStatus
        };
      }

      console.log('Payload:', payload);
      console.log('Current State:', currentState);
      console.log('API Key:', apiKey);
      console.log('New Status:', newStatus);
      console.log('Updated Channels:', updatedChannels);

      // Update local state first to ensure UI reflects the change
      setBlacklistState(prev => ({
        ...prev,
        [apiKey]: {
          channels: updatedChannels.filter((c): c is string => c !== undefined),
          status: newStatus
        }
      }));

      setIsSubmitting(true);

      triggerFetchChannelStatus(payload, {
        onSuccess: (data) => {
          setToastData({
            type: "success",
            title: "Success",
            description: data.message || "Channel updated",
            variant: "default"
          });

          triggerFetchRealTime({ package_name: selectedPackage });
          setDeleteDialogOpen(false);
          setPendingToggle(null);
          setIsSubmitting(false);
        },
        onError: (err) => {
          // Revert the local state on error
          setBlacklistState(prev => ({
            ...prev,
            [apiKey]: {
              channels: currentChannels,
              status: !newStatus
            }
          }));

          setToastData({
            type: "error",
            title: "Error",
            description: err.message,
            variant: "default"
          });
          setIsSubmitting(false);
        }
      });
    }
  };
    const {
      trigger: triggerFetchRealTime,
      loading: isLoadinggetReal
    } = useApiCall<RealTimeApiItem[]>({
      url: process.env.NEXT_PUBLIC_CONFIGURATION + Endpoint.GET_REAL_TIME_TABLE,
      method: 'POST',
      manual: true,
      onSuccess: (data) => {
      //  console.log("Realtime API response", data);
        const dynamicState: Record<string, { channels: string[]; status: boolean }> = {};
        const actionMap: Record<string, boolean> = {};

        const transformed = data.map((item) => {
          const typeKey = `${item.Blacklisting}_blacklisting`;

          dynamicState[typeKey] = {
            channels: Object.keys(item.channels),
            status: item.status === 'Active',
          };

          actionMap[item.Blacklisting] = item.action;

          return {
            BlackListing: item.Blacklisting,
            Status: item.status,
            "Date on Enabled": item.inserted_date,
            "Checkbox-data": (
              <ChannelSwitchList
                key={item.Blacklisting + JSON.stringify(item.channels)} // 👈 force remount
                channels={item.channels}
                blacklistType={item.Blacklisting}
                onToggle={handleChannelToggle}
              />
            ),

            Action: null, // placeholder
          };
        });

        setActionStatusMap(actionMap);
        setBlacklistState(dynamicState);

        const finalTransformed = transformed.map((row) => ({
          ...row,
          Action: (
            <Switch
              checked={actionMap[row.BlackListing]}
              disabled={isTogglingAction}
              onCheckedChange={(checked) => {
                setPendingToggle({ type: "action", blacklistType: row.BlackListing, newStatus: checked });
                setDeleteDialogOpen(true);
              }}
            />
          )
        }));

        setRealTimeData(finalTransformed);
        setIsTogglingAction(false);
      },
      onError: (err) => {
        console.error('Error fetching real-time data:', err);
      }
    });


    const { trigger: triggerFetchChannelStatus } = useApiCall<any>({
      url: process.env.NEXT_PUBLIC_CONFIGURATION + Endpoint.TOGGLE_CHANNEL_STATUS,
      method: 'POST',
      manual: true,
      onSuccess: (data) => {
        setToastData({
          type: "success",
          title: "Success",
          description: data.message,
          variant: "default"
        });
       // console.log("Realtime API response", data);
        //triggerFetchRealTime({ package_name: selectedPackage });
      },
      onError: (error) => {
        console.error('Error toggling channel status:', error);
      }
    });

    const { trigger: triggerToggleAction } = useApiCall<any>({
      url: process.env.NEXT_PUBLIC_CONFIGURATION + Endpoint.TOGGLE_ACTION_STATUS,
      method: 'POST',
      manual: true,
      onSuccess: (res) => {
       // console.log("✅ Action toggle success:", res);
        setToastData({
          type: "success",
          title: "Success",
          description: res.message,
          variant: "default"
        });
        // Refresh the table after successful toggle
        // triggerFetchRealTime({ package_name: selectedPackage });
      },
      onError: (err) => {
      //  console.error("❌ Error toggling action:", err);
        setToastData({
          type: "error",
          title: "Error",
          description: err.message,
          variant: "default"
        });
      }
    });
    useEffect(() => {
      if (selectedPackage) {
        triggerFetchRealTime({ package_name: selectedPackage });
      }
    }, [selectedPackage]);

    return (
      <div className="min-h-[500px] p-8">
        <div className="max-w-6xl mx-auto">
          <Card className='p-8 min-h-[500px]'>
          {toastData && (
            <ToastContent
              type={toastData.type}
              title={toastData.title}
              description={toastData.description}
              variant={toastData.variant}
            />
          )}
          <ResizableTable
            isPaginated={false}
            data={realTimeData}
            columns={RealTimeColumn}
            isSearchable={false}
            isSelectable={false}
            isLoading={isLoadinggetReal}
            isColumns={false}
          />
          <DeleteDialog
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
            onConfirm={confirmDelete}
            title={`Confirm ${pendingToggle?.newStatus ? "Activate" : "Deactivate"} ${pendingToggle?.type === "channel" ? pendingToggle?.channel : pendingToggle?.blacklistType}?`}
            description={
              pendingToggle?.type === "channel"
                ? `Are you sure you want to ${pendingToggle?.newStatus ? "activate" : "deactivate"} the "${pendingToggle?.channel}" channel?`
                : `Are you sure you want to ${pendingToggle?.newStatus ? "activate" : "deactivate"} the "${pendingToggle?.blacklistType}" blacklisting rule?`
            }
            button_no="No"
            button_yes="Yes"
            isSubmitting={isSubmitting}
          />
          </Card>
        </div>
      </div>
    );
  }

  export default RealTimeProtection;
