'use client';
import React, { useEffect, useState } from 'react';

interface CircularProps {
  value?: number;          // Value in percentage or seconds
  target?: number;
  color?: string;
  size?: number;
  showTime?: boolean; 
   maxValue?: number;      // New prop to switch between percentage and time
}

const Circular_progress: React.FC<CircularProps> = ({
  value = 0,
  target = 100,
  color = "#ff00ff",
  size = 120,
  showTime = false, 
  maxValue = 100,        // Default to percentage
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);
  const radius = (size - 20) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
   const strokeDashoffset = circumference - (animatedValue / maxValue) * circumference;
  const targetOffset = circumference - (target / maxValue) * circumference;

  useEffect(() => {
    const duration = 1000; // in ms
    const startTime = performance.now();

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      setAnimatedValue(Math.round(progress * value));

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [value]);

  
  // Time formatting: seconds to mm:ss
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="relative" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle cx={size / 2} cy={size / 2} r={radius} stroke="#E5E7EB" strokeWidth="8" fill="transparent" />
        {/* Target circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth="2"
          strokeOpacity="0.3"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={targetOffset}
          fill="transparent"
          strokeLinecap="round"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth="8"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          fill="transparent"
          strokeLinecap="round"
          className="transition-all duration-1000 ease-out"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {showTime ? formatTime(animatedValue) : `${animatedValue}%`}
          </div>
         {showTime ? (
              <div className="text-xs text-gray-500 mt-1">Min:Sec</div>
            ) : (
              <div className="text-xs text-gray-500">of {target}%</div>
            )}
        
        </div>
      </div>
    </div>
  );
};

export default Circular_progress;
