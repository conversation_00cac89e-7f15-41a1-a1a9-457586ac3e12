"use client";
import React from "react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";
import { useSignOut } from "@/queries";
import ToastContent from "@/components/mf/ToastContent";
import { Power } from "lucide-react";

const SignOutButton = () => {
  const router = useRouter();
  const [toastData, setToastData] = React.useState<any>(null);
  
  const { mutate } = useSignOut(
    // onError callback
    (error) => {
      console.log("Sign out error:", error);
      setToastData({
        type: "error",
        title: "Sign Out Failed",
        description: error.message || "Failed to sign out",
        variant: "default"
      });
      // Clear session and redirect even on error
      setTimeout(() => {
        localStorage.clear();
             window.location.href = `${process.env.NEXT_PUBLIC_REDIRECT_URL}`;
        router.refresh();
      }, 2000);
    },
    // onSuccess callback
    (data) => {
      //console.log("Sign out success:", data.data.message);
      setToastData({
        type: "success",
        title: "Signed Out", 
        description:data.data.message,
        variant: "default"
      });
      // Clear session and redirect on success
      setTimeout(() => {
        localStorage.clear();
           window.location.href = `${process.env.NEXT_PUBLIC_REDIRECT_URL}`;
       // window.location.href = 'https://uat-dashboard.mfilterit.net/';
      //  router.replace("/");
        router.refresh();
      }, 2000);
    }
  );
  
  const onClick = () => {
    const accessToken = localStorage.getItem("AccessToken");
    const idToken = localStorage.getItem("IDToken");
    
    if (!accessToken || !idToken) {
      console.log("Debug - No access token or id token, clearing session and redirecting");
      // Clear all session storage items
      localStorage.clear();
      // Redirect to login page immediately
            window.location.href = `${process.env.NEXT_PUBLIC_REDIRECT_URL}`;
      router.refresh();
      return;
    }

    // Make the API call
    mutate(accessToken);
  };

  return (
    <>
      {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}
      <Button
        title="Log out"
        variant="ghost"
        size="icon"
        onClick={onClick}
        className="hover:bg-destructive hover:text-destructive-foreground"
      >
        <Power />
      </Button>
    </>
  );
};

export default SignOutButton;