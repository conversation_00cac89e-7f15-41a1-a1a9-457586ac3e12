import React, { useEffect, useState, useCallback } from "react";
import { useApiCall } from "../../../app/(main)/(web)/queries/api_base";
import Endpoint from "../../../app/(main)/(web)/common/endpoint";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Loader2, Minus, Plus, X, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MFDateRangePicker } from "@/components/mf/MFDateRangePicker";
import { DateRangeProvider, useDateRange } from "@/components/mf/DateRangeContext";

interface DeliveryOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "schedule" | "download";
  onSubmit: (payload: any) => void;
  defaultData?: any;
  mode?: any;
  frequency?: string;
  onFrequencyChange?: (value: string) => void;
  category?: string;
}

interface MailingList {
  id: string;
  mailing_list_name: string;
}

const occurrenceOptions = [
  // { value: "last-hour", label: "Last Full Available Hours" },
  { value: "last-day", label: "Last Full Available Day" },
  { value: "last-7-days", label: "Last 7 Days" },
  // { value: "last-one-day", label: "Last One Day" },
  { value: "last-week", label: "Last Week" },
  { value: "last-15-days", label: "Last 15 Days" },
  { value: "last-30-days", label: "Last 30 Days" },
  // { value: "month-to-date", label: "Month to Date" },
  { value: "last-month", label: "Last Month" },
  { value: "quarter-to-date", label: "Quarter to Date" },
  { value: "last-quarter", label: "Last Quarter" },
  { value: "custom-range", label: "Custom Date Range" },
  // { value: "DAILY", label: "Daily" },
];

const scheduleOptions = [
  { value: "DAILY", label: "Daily" },
  { value: "last-month", label: "Last Month" },
  { value: "month-to-date", label: "Month to Date" },
  { value: "last-week", label: "Last Week" },
];

const DeliveryOptionsModal = ({
  isOpen,
  onClose,
  type,
  onSubmit,
  defaultData,
  mode,
  frequency,
  onFrequencyChange,
  category,
}: DeliveryOptionsModalProps) => {
  const [sendViaEmail, setSendViaEmail] = useState(false);
  const [saveToCloud, setSaveToCloud] = useState(true);
  const [selectedCloudProvider, setSelectedCloudProvider] = useState<"AWS" | "GCP" | "Azure">("AWS");
  const [emails, setEmails] = useState<string[]>([""]);
  const [selectedMailingLists, setSelectedMailingLists] = useState<string[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [cloudConfigs, setCloudConfigs] = useState({
    AWS: { accessKey: "", secretKey: "", bucketName: "" },
    GCP: { accessKey: "", secretKey: "", bucketName: "" },
    Azure: { accessKey: "", secretKey: "", bucketName: "" },
  });

  const [mailinglist, setMailinglist] = useState<MailingList[]>([]);
  const [customRange, setCustomRange] = useState<{ from: string; to: string } | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

 
  const { result: mailingListApi, loading: mailingListLoading } = useApiCall({
    url: process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.MAILING_LIST_GET_API,
    method: "POST",
    manual: true,
    onSuccess: (data) => {
      if (Array.isArray(data.mailing_lists)) {
        setMailinglist(data.mailing_lists);
      }
    },
    onError: (error) => {
      console.error("Error fetching mailing list:", error);
    },
  });

  const refreshMailingList = useCallback(() => {
    mailingListApi.mutate({
      page: "1",
      page_size: "200",
      mailing_list_name: "",
    });
  }, [mailingListApi]);

  useEffect(() => {
    if (isOpen) {
      refreshMailingList();
    }
  }, [isOpen]);

  useEffect(() => {
    if (defaultData) {
      const { email, aws, gcp, azure } = defaultData;

      if (email?.status) {
        setSendViaEmail(true);
        const selectedLists = email.mail_id_list?.map((listName: string) =>
          mailinglist.find((ml) => ml.mailing_list_name === listName)?.id
        ).filter(Boolean) || [];
        setSelectedMailingLists(selectedLists);
        setEmails(email.to || [""]);
      }

      if (aws?.status) {
        setSelectedCloudProvider("AWS");
        setSaveToCloud(true);
        setCloudConfigs((prev) => ({
          ...prev,
          AWS: {
            accessKey: aws.aws_access_key_id || "",
            secretKey: aws.aws_secret_access_key || "",
            bucketName: aws.bucket_name || "",
          },
        }));
      } else if (gcp?.status) {
        setSelectedCloudProvider("GCP");
        setSaveToCloud(true);
        setCloudConfigs((prev) => ({
          ...prev,
          GCP: {
            accessKey: gcp.gcp_access_key_id || "",
            secretKey: gcp.gcp_secret_access_key || "",
            bucketName: gcp.storage_name || "",
          },
        }));
      } else if (azure?.status) {
        setSelectedCloudProvider("Azure");
        setSaveToCloud(true);
        setCloudConfigs((prev) => ({
          ...prev,
          Azure: {
            accessKey: azure.azure_access_key_id || "",
            secretKey: azure.azure_secret_access_key || "",
            bucketName: azure.container_name || "",
          },
        }));
      }
    }
  }, [defaultData, mailinglist]);

  useEffect(() => {
    if (isOpen) {
      if (type === "download") {
        setSaveToCloud(false);
      } else {
        setSaveToCloud(true);
      }
    }
  }, [isOpen, type]);

  const handleMailingListToggle = (listId: string) => {
    if (mode === "view") return;
    setSelectedMailingLists((prev) =>
      prev.includes(listId) ? prev.filter((id) => id !== listId) : [...prev, listId]
    );
  };

  const removeMailingList = (listId: string) => {
    if (mode === "view") return;
    setSelectedMailingLists((prev) => prev.filter((id) => id !== listId));
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isDropdownOpen && !target.closest(".relative")) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isDropdownOpen]);

  const getSelectedMailingListNames = () => {
    return selectedMailingLists
      .map((id) => mailinglist.find((ml) => ml.id === id)?.mailing_list_name)
      .filter(Boolean);
  };

  const handleConfirm = (extra?: { customRange?: { from: string; to: string } }) => {
    const cloudOptions = ["AWS", "GCP", "Azure"].reduce((acc, provider) => {
      const key = provider.toLowerCase();
      const isSelected = selectedCloudProvider === provider && saveToCloud;
      const config = cloudConfigs[provider as "AWS" | "GCP" | "Azure"];

      acc[key] = {
        status: isSelected,
        [`${key}_access_key_id`]: isSelected ? config.accessKey : "",
        [`${key}_secret_access_key`]: isSelected ? config.secretKey : "",
        [provider === "AWS"
          ? "bucket_name"
          : provider === "GCP"
          ? "storage_name"
          : "container_name"]: isSelected ? config.bucketName : "",
        key: isSelected ? "reports/monthly_sales_report.pdf" : "",
      };

      return acc;
    }, {} as Record<string, any>);

    const mailingListNames = getSelectedMailingListNames();
    const filteredEmails = emails.filter((email) => email.trim() !== "");
    const hasMailingLists = mailingListNames.length > 0;
    const hasEmails = filteredEmails.length > 0;

    const emailSection = {
      status: sendViaEmail,
      mail_type: hasMailingLists ? "group" : "individual",
      mail_id_list: mailingListNames,
      email_group: mailingListNames,
      to: filteredEmails,
    };

    const payload = {
      ...cloudOptions,
      email: emailSection,
      ...(extra?.customRange ? { customRange: extra.customRange } : {}),
    };

    onSubmit(payload);
    onClose();
  };

  let dateRangeContext: { startDate: string; endDate: string; setDateRange: (start: string, end: string) => void } | undefined;
  try {
    dateRangeContext = useDateRange();
  } catch {}

  return (
    <DateRangeProvider>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[600px] h-[600px] flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {type === "schedule" ? "Schedule Report" : "Download Report"}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto py-4">
            {/* Occurrence Dropdown */}
            <div className="space-y-2  p-1 mb-4">
              <Label>Occurrence<span className="text-red-500">*</span></Label>
              <Select
                value={frequency}
                onValueChange={(val) => {
                  onFrequencyChange?.(val);
                  setShowDatePicker(val === "custom-range");
                }}
                disabled={mode === "view"}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Occurrence" />
                </SelectTrigger>
                <SelectContent>
                  {(type === "schedule"
                    ? scheduleOptions
                    : (category === "Visit"
                        ? occurrenceOptions.filter(option => ["last-7-days", "last-one-day", "custom-range"].includes(option.value))
                        : occurrenceOptions)
                  ).map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showDatePicker && (
                <div className="mt-2">
                  <MFDateRangePicker
                    onDateChange={(range) => {
                      if (range?.from && range?.to) {
                        setCustomRange({
                          from: range.from.toISOString().slice(0, 10),
                          to: range.to.toISOString().slice(0, 10),
                        });
                      }
                    }}
                  />
                </div>
              )}
            </div>

            <div className="border-b">
              <div className="flex">
                <div className="flex items-center gap-2 min-w-[150px] px-4 py-2">
                  <Checkbox
                    id="save-cloud"
                    checked={saveToCloud}
                    onCheckedChange={(checked) =>
                      setSaveToCloud(checked as boolean)
                    }
                  />
                  <Label htmlFor="save-cloud" className="text-sm font-medium">
                    Save to Cloud
                  </Label>
                </div>
                <div className="flex items-center gap-2 min-w-[150px] px-4 py-2">
                  <Checkbox
                    id="send-email"
                    checked={sendViaEmail}
                    onCheckedChange={(checked) =>
                      setSendViaEmail(checked as boolean)
                    }
                  />
                  <Label htmlFor="send-email" className="text-sm font-medium">
                    Send via Email
                  </Label>
                </div>
              </div>
            </div>

            {sendViaEmail && (
              <div className="space-y-4 rounded-lg border p-4 mt-4">
                <div className="space-y-2">
                  <Label>Select Mailing Lists</Label>
                  
                  {/* Selected mailing lists display */}
                  {selectedMailingLists.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-2">
                      {selectedMailingLists.map(listId => {
                        const listName = mailinglist.find(ml => ml.id === listId)?.mailing_list_name;
                        return (
                          <div key={listId} className="flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-md text-sm">
                            <span>{listName}</span>
                            {mode !== "view" && (
                              <X 
                                className="h-3 w-3 cursor-pointer hover:text-destructive" 
                                onClick={() => removeMailingList(listId)}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {/* Custom dropdown for selecting mailing lists */}
                  <div className="relative">
                    <div 
                      className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer ${mode === "view" ? "opacity-50 cursor-not-allowed" : ""}`}
                      onClick={() => mode !== "view" && setIsDropdownOpen(!isDropdownOpen)}
                    >
                      <span className="flex-1">
                        {selectedMailingLists.length === 0 
                          ? "Choose Mailing Lists" 
                          : `${selectedMailingLists.length} selected`
                        }
                      </span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                    </div>
                    
                    {isDropdownOpen && (
                      <div className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-md max-h-60 overflow-auto">
                        {mailingListLoading ? (
                          <div className="flex justify-center items-center p-4">
                            <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          </div>
                        ) : mailinglist.length === 0 ? (
                          <div className="p-4 text-center text-muted-foreground">
                            No mailing lists available
                          </div>
                        ) : (
                          mailinglist.map((list) => (
                            <div 
                              key={list.id}
                              className="flex items-center gap-2 p-2 hover:bg-accent cursor-pointer"
                              onClick={() => handleMailingListToggle(list.id)}
                            >
                              <Checkbox 
                                checked={selectedMailingLists.includes(list.id)}
                              />
                              <span className="flex-1">{list.mailing_list_name}</span>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-2">
                  {emails.map((email, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        type="email"
                        placeholder="Enter Email"
                        value={email}
                        onChange={(e) =>
                          setEmails((prev) => {
                            const copy = [...prev];
                            copy[index] = e.target.value;
                            return copy;
                          })
                        }
                        className="flex-1"
                        disabled={mode === "view"}
                      />
                      {index === emails.length - 1 ? (
                        <Plus
                          onClick={() => setEmails([...emails, ""])}
                          className="mt-2 h-4 w-4 cursor-pointer text-primary"
                        />
                      ) : (
                        <Minus
                          onClick={() =>
                            setEmails(emails.filter((_, i) => i !== index))
                          }
                          className="mt-2 h-4 w-4 cursor-pointer text-primary"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {saveToCloud && (
              <div className="space-y-4 rounded-lg border p-4 mt-4">
                <div className="border-b">
                  <div className="flex">
                    {["AWS", "GCP", "Azure"].map((provider) => (
                      <button
                        disabled={mode === "view"}
                        key={provider}
                        onClick={() =>
                          setSelectedCloudProvider(
                            provider as "AWS" | "GCP" | "Azure"
                          )
                        }
                        className={`relative min-w-[100px] border-b-2 px-4 py-2 text-sm font-medium transition-colors hover:text-primary ${
                          selectedCloudProvider === provider
                            ? "border-primary text-primary"
                            : "border-transparent text-muted-foreground"
                        }`}
                      >
                        {provider}
                      </button>
                    ))}
                  </div>
                </div>
                {(() => {
                  const config = cloudConfigs[selectedCloudProvider];
                  return (
                    <>
                      <div className="space-y-2">
                        <Label>Secret Key</Label>
                        <Input
                          value={config.secretKey}
                          onChange={(e) =>
                            setCloudConfigs((prev) => ({
                              ...prev,
                              [selectedCloudProvider]: {
                                ...prev[selectedCloudProvider],
                                secretKey: e.target.value,
                              },
                            }))
                          }
                          placeholder={`Enter ${selectedCloudProvider} Secret Key`}
                          disabled={mode === "view"}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Access Key</Label>
                        <Input
                          value={config.accessKey}
                          onChange={(e) =>
                            setCloudConfigs((prev) => ({
                              ...prev,
                              [selectedCloudProvider]: {
                                ...prev[selectedCloudProvider],
                                accessKey: e.target.value,
                              },
                            }))
                          }
                          placeholder={`Enter ${selectedCloudProvider} Access Key`}
                          disabled={mode === "view"}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>
                          {selectedCloudProvider === "AWS"
                            ? "Bucket Name"
                            : selectedCloudProvider === "GCP"
                              ? "Storage Name"
                              : "Container Name"}
                        </Label>
                        <Input
                          value={config.bucketName}
                          onChange={(e) =>
                            setCloudConfigs((prev) => ({
                              ...prev,
                              [selectedCloudProvider]: {
                                ...prev[selectedCloudProvider],
                                bucketName: e.target.value,
                              },
                            }))
                          }
                          placeholder={`Enter ${
                            selectedCloudProvider === "AWS"
                              ? "Bucket Name"
                              : selectedCloudProvider === "GCP"
                                ? "Storage Name"
                                : "Container Name"
                          }`}
                          disabled={mode === "view"}
                        />
                      </div>
                    </>
                  );
                })()}
              </div>
            )}
          </div>

          <DialogFooter className="flex-shrink-0">
            <Button
              onClick={onClose}
              className="text-white bg-primary"
              disabled={mode === "view"}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                 //if (!handleValidation()) return; 
                if (frequency === "custom-range" && customRange) {
                  handleConfirm({ customRange });
                } else {
                  handleConfirm();
                }
              }}
              className="text-white bg-primary"
              disabled={mode === "view"}
            >
              {type === "schedule" ? "Schedule" : "Download"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DateRangeProvider>
  );
};

export default DeliveryOptionsModal;
