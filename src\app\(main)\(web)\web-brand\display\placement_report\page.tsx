'use client';
import React from 'react';
import { onExpand, downloadURI } from "@/lib/utils";
import { useCallback, useRef, useState } from 'react';
import domToImage from "dom-to-image";
import SummaryCardGroup from '../common_module/page';
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {
    buildFilter,
    useFilterChangeHandler,
    FilterState,
} from '../Filters/buildFilters';
import HeaderRow from '@/components/mf/HeaderRow';
import ResizableTable from '@/components/mf/ReportingToolTable';
import { Card } from '@/components/ui/card';
import { useWastageFilters } from '../../Filters/useFilters';
import { useTopPlacementsAsc, useTopPlacementsDesc } from './apicallPage';
import { useExportCsv } from '@/lib/Exportdata';
import { useDebounce } from '@/hooks/useDebounce';
import Endpoint from '../../../common/endpoint';




const Summary = () => {
    const cardRefs = useRef<Record<string, HTMLElement | null>>({});
    const [expandedCard, setExpandedCard] = useState<string | null>(null);
    const { selectedPackage } = usePackage();
    const { startDate, endDate } = useDateRange();
    const [serchterm, setSearchTerm] = useState('');
     const [isExporting, setIsExporting] = useState(false);
        const [exportType, setExportType] = useState<string | null>(null);

  const debouncedSearchTerm = useDebounce(serchterm, 500); // 300ms delay

    const params = {
        package_name: selectedPackage,
        start_date: startDate,
        end_date: endDate,
    };
    const [query, setQuery] = useState({
         publisher: ["all"],
         campaign: ["all"],
         channel: ["all"],
         fraud_category:["all"],
         fraud_sub_category:["all"],
         creative_id:["all"],
         sub_publisher:["all"],
         campaign_id:["all"],
       });
    const topwiseparams={
        ...params,
        ...query,
        search_term:debouncedSearchTerm,
    }
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
const isReady =
  !!selectedPackage && !!startDate && !!endDate;
      const { data: topplacementsasc, isLoading: topplacementsascLoading, error: topplacementsascError } = useTopPlacementsAsc(topwiseparams,isReady||exportType !== 'topplacementsasc');
        const { data: topplacementsdesc, isLoading: topplacementsdescLoading, error: topplacementsdescError } = useTopPlacementsDesc(topwiseparams,isReady||exportType !== 'topplacementsdesc');
     
    const onExport = useCallback(
        async (s: string, title: string, key: string) => {
            const ref = cardRefs.current[key];
            if (!ref) return;

            switch (s) {
                case "png":
                    const screenshot = await domToImage.toPng(ref);
                    downloadURI(screenshot, title + ".png");
                    break;
                default:
            }
        },
        []
    );
    const handleExpand = (key: string) => {
        onExpand(key, cardRefs, expandedCard, setExpandedCard);
    };
    const xAxisConfigS = {
        dataKey: "label",
    };

    const xAxisConfigD = {
        dataKey: "label",
    };

    const xAxisConfigstack = {
        dataKey: "value",

    };



   const filter = useWastageFilters(params, query);
   
    const handleFilterChange = useFilterChangeHandler(
        loadedFilter,
        setQuery,
        setLoadedFilter
    );

    const imprHeader = [
        { title: "Placement", key: "Placement" },
        { title: "Impression", key: "Impression" },
        { title: "IVT%", key: "IVT" },
    ]

    const ImprData = topplacementsdesc?.map((item: any) => ({
        "Placement": item.imp_placement_id,
        "Impression": item.total_count,
        "IVT": item.ivt_percentage,
    })) ?? [];
    const AscdHeader = [
        { title: "Placement", key: "Placement" },
        { title: "Impression", key: "Impression" },
        { title: "IVT%", key: "IVT" },
    ]
    const AscdData = topplacementsasc?.map((item: any) => ({
        "Placement": item.imp_placement_id,
        "Impression": item.total_count,
        "IVT": item.ivt_percentage,
    })) ?? [];
    const mfaHeader = [
        { title: "Placement", key: "Placement" },
        { title: "Impression", key: "Impression" },
        { title: "IVT%", key: "IVT" },
    ]
    const mfaData = [
        {
            Placement: "Placement1",
            Impression: 1000,
            IVT: 10,
        },
        {
            Placement: "Placement2",
            Impression: 2000,
            IVT: 20,
        },
        {
            Placement: "Placement3",
            Impression: 3000,
            IVT: 30,
        },
        {
            Placement: "Placement4",
            Impression: 4000,
            IVT: 40,
        },
        {
            Placement: "Placement5",
            Impression: 5000,
            IVT: 50,
        },
        {
            Placement: "Placement6",
            Impression: 6000,
            IVT: 60,
        },
        {
            Placement: "Placement7",
            Impression: 7000,
            IVT: 70,
        },
    ]
    const UnsafeHeader = [
        { title: "Placement", key: "Placement" },
        { title: "Impression", key: "Impression" },
        { title: "IVT%", key: "IVT" },
    ];
    const safeHeader = [
        { title: "Placement", key: "Placement" },
        { title: "Impression", key: "Impression" },
        { title: "IVT%", key: "IVT" },
    ];

    const handleExportClick = async (type: string) => {
        setExportType(type as any);
        setIsExporting(true);
    };
    useExportCsv({
        exportParams: params,
        queryParams: query,
        exportType,
        setExportType,
        isExporting,
        setIsExporting,
        endpointMap: {
            topplacementsasc: Endpoint.WebBrand.DISPLAY_TOP_PLACEMENTS_ASC,
            topplacementsdesc: Endpoint.WebBrand.DISPLAY_TOP_PLACEMENTS_DESC,
        },
        baseUrlMap: {
            topplacementsasc: process.env.NEXT_PUBLIC_WEB_BRAND!,
            topplacementsdesc: process.env.NEXT_PUBLIC_WEB_BRAND!,
        },
    });
    return (
        <div className="p-2 w-full grid grid-col  gap-2">
            <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 sm:overflow-x-auto  scrollbar w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                <Filter filter={filter} onChange={handleFilterChange} />
            </div>
            <SummaryCardGroup params={params} query={query}/>
            <div className="gap-1 w-full">
                <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Web Placements</div>
                <div className="grid grid-rows-3 w-full gap-2">
                    <Card ref={(el) => (cardRefs.current["impression_placements"] = el!)} className='p-2'>
                        <HeaderRow
                            title='Top 20 Placements Impression-Descending'
                            isellipsis={false}
                        />

                        <ResizableTable
                            isPaginated={true}
                            columns={imprHeader}
                            data={ImprData}
                            isSearchable={true}
                            isUserTable={false}
                            height={410}
                            isTableDownload={true}
                            onDownload={() => handleExportClick("topplacementsdesc")}
                             row_count={5}
                            row_height={10}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={serchterm}
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["ascending_placements"] = el!)} className='p-2'>
                        <HeaderRow
                            title='Top 20 Placements - Ascending'
                            isellipsis={false}
                            
                        />

                        <ResizableTable
                            isPaginated={true}
                            columns={AscdHeader}
                            data={AscdData}
                            isSearchable={true}
                            isUserTable={false}
                            height={410}
                            isTableDownload={true}
                            onDownload={() => handleExportClick("topplacementsasc")}
                             row_count={5}
                            row_height={10}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={serchterm}
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["mfa_placements"] = el!)} className='p-2'>
                        <HeaderRow
                            title='Sample MFA Placements'
                            isellipsis={false}
                        />

                        <ResizableTable
                            isPaginated={true}
                            columns={mfaHeader}
                            data={mfaData}
                            isSearchable={true}
                            isTableDownload={true}
                           // onDownload={() => handleExportClick("mfa_placements")}
                            isUserTable={false}
                            height={410}
                             row_count={5}
                            row_height={10}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={serchterm}
                        />
                    </Card>
                </div>
                </div>
                <div className="gap-1 w-full">
                <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>App Placements</div>
                <div className="grid grid-rows-2 w-full gap-2">
                    <Card ref={(el) => (cardRefs.current["unsafe_placements"] = el!)} className=' p-2'>
                        <HeaderRow
                            title='Top 20 Placements - Unsafe'
                       
                        />

                        <ResizableTable
                            isPaginated={true}
                            columns={imprHeader}
                            data={ImprData}
                            isTableDownload={true}
                           // onDownload={() => handleExportClick("topplacementsdesc")}
                            isSearchable={true}
                            isUserTable={false}
                            height={410}
                             row_count={5}
                              row_height={10}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={serchterm}
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["safe_placements"] = el!)} className='p-2'>
                        <HeaderRow
                            title='Top 20 Placements - Safe'
                            isellipsis={false}
                        />
                        <ResizableTable
                            isPaginated={true}
                            columns={AscdHeader}
                            data={AscdData}
                            isSearchable={true}
                            isTableDownload={true}
                            //onDownload={() => handleExportClick("topplacementsasc")}
                            isUserTable={false}
                            height={410}
                             row_count={1}
                             row_height={10}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={serchterm}
                        />
                    </Card>
                </div>
            </div>
        </div>

    );
};

export default Summary;
