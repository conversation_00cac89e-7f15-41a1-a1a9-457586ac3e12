const Endpoint ={
    TRAFFIC_COUNT : "api/v1/web/performance/traffic_counts",
    EMBEDDED_MENU : "access_control/embedded_menus",
    EVENT_TRAFFIC : "api/v1/web/performance/event_traffic",
    VISIT_TRAFFIC : "api/v1/web/performance/visit_traffic",
    TOP_CAMPAIGNS : "api/v1/web/performance/top_campaigns",
    TOP_PUBLISHERS : "api/v1/web/performance/top_publishers",
    VISIT_TRAFFIC_PUBLISHER :"api/v1/web/performance/visit_traffic_publisher",
    EVENT_TRAFFIC_PUBLISHER :"api/v1/web/performance/event_traffic_publisher",
    TRAFFIC_TRENDS : "api/v1/web/performance/traffic_trends",
    COLOR_API :"web/performance/colors",       
    SUB_PUBLISHERS: "api/v1/web/performance/filters/sub_publishers",
    CAMPAIGNS: "api/v1/web/performance/filters/campaigns",
    CHANNELS: "api/v1/web/performance/filters/channels",
    PUBLISHERS: "api/v1/web/performance/filters/publishers",
    REPEAT_USERS_TABLE: "api/v1/web/performance/repeat_users_table",
    BOT_BEHAVIOUR :"api/v1/web/performance/bot_behaviour",
    REPEAT_IP :"api/v1/web/performance/repeat_ip",
    HARDWARE_CONCURRENCY :"api/v1/web/performance/hardware_concurrency",
    INVALID_GEO :"api/v1/web/performance/invalid_geo",
    IFRAME_SIZE :"api/v1/web/performance/iframe_size",
    POP_UNDER :"api/v1/web/performance/pop_under",
    VPN_PROXIES :"api/v1/web/performance/vpn_proxies",
    TRAFFIC_CONTRIBUTION :"api/v1/web/performance/traffic_contribution",
    TOP_CONTRIBUTING_OS_VERSIONS:"api/v1/web/performance/top_contributing_os_versions",
    STATE_VISIT:"api/v1/web/performance/state_visit",
    STATE_EVENT:"api/v1/web/performance/state_event",
    GOOGLE:"api/v1/web/performance/google",
    META:"api/v1/web/performance/meta",
    BLOCKED_REPORT_GOOGLE:"api/v1/web/performance/blocked_report_google",
    BLOCKED_REPORT_META:"api/v1/web/performance/blocked_report_meta",
    OVERALL_PLACEMENT:"api/v1/web/performance/overall_placements",
    OVERALL_PLACEMENT_TABLE:"api/v1/web/performance/overall_placements_table",
    UNSAFE_PLACEMENT:"api/v1/web/performance/unsafe_placements",
    UNSAFE_PLACEMENT_TABLE:"api/v1/web/performance/unsafe_placements_table",
    USER_INTENT:"api/v1/web/performance/user_intent",
    AUDIENCE_OPTIMIZATION_FACEBOOK:"api/v1/web/performance/audience_optimization_facebook",
    OPTIMIZATION_FACEBOOK_TABLE:"api/v1/web/performance/audience_optimization_facebook_table",
    AUDIENCE_OPTIMIZATION_GOOGLE_TABLE:"api/v1/web/performance/audience_optimization_google_table",
    DEVICE_CONCENTRATION:"api/v1/web/performance/device_concentration",
    DEVICE_CONCENTRATION_DESKTOP:"api/v1/web/performance/device_concentration_desktop",
    AUDIENCE_OPTIMIZATION_GOOGLE:"api/v1/web/performance/audience_optimization_google",
    EVENT_TYPE:"api/v1/web/performance/filters/event_type",
    REDIS_GET_API:"api/v1/web/performance/redis_get_api",
    REDIS_API:"api/v1/web/performance/redis_api",
    GOOGLE_ADS_LOGIN:"api/v1/web/performance/google_ads_login",
    GOOGLE_ADS_AUTH_API:"api/v1/web/performance/google_ads_token_fetch",
    COUNTRY:"api/v1/web/performance/filters/country",
    LIST_USER:"auth/list_users",
    USER_INFORMATION:"access_control/getUser?product_name=Web%20Performance",
    PRODUCT:"access_control/products",
    ROLE_LIST:"access_control/rolesList?product_name=Web%20Performance",
    PACKAGE:"access_control/user_packages?product_name=Web%20Performance",
    CREATE_PACKAGE:"access_control/createPackage?product_name=Web%20Performance",
    REPORT_TEMPLATE:"common/reporting_tool/get_templates",
    REPORT_TEMPLATE_FIELDS:"common/reporting_tool/get_template_fields",
    REPORT_CUSTOM_TEMPLATE:"common/reporting_tool/custome_template",
    REPORT_GET_API :"common/reporting_tool/get_all_reports",
    REPORT_FILTERS:"common/reporting_tool/filters",
    MAILING_LIST_GET_API:"common/reporting_tool/list_all_mailing_lists",
    CREATE_MAILING_LIST_API:"common/reporting_tool/create_mailing_list",
    UPDATE_MAILING_LIST_API:"common/reporting_tool/edit_mailing_list",
    REPORT_CREATE_API:"common/reporting_tool/create_report",
    REPORT_THRESHOLD_API:"common/reporting_tool/threshold",
    REPORT_DELETE_API:"common/reporting_tool/delete_report",
    REPORT_CLONE_API:"common/reporting_tool/copy_report",
    VIEW_REPORT_API:"common/reporting_tool/view_report",
    REPORT_EDIT_API:"common/reporting_tool/edit_report",
    REPORT_CATEGORY:"common/reporting_tool/categories",
    REPORT_STATUS_UPDATE_API:"common/reporting_tool/update_status",
    REPORT_STATUS_MAIL_UPDATE_API:"common/reporting_tool/status_change_mailing_list",
    GET_MENU_API:"access_control/menus?product_name=Web%20Performance",
    USER_LIST:"access_control/userList?product_name=Web%20Performance",
    ADD_USER:"access_control/addToUser?product_name=Web%20Performance",
    PACKAGE_MAPPING:"access_control/packageMapping?product_name=Web%20Performance",
   EDIT_USERROLES: "access_control/editUserRoles?product_name=Web%20Performance",
   CREATE_USER:"access_control/createUser?product_name=Web%20Performance",
   DELETE_PACKAGE:"access_control/deletePackage",
   DELETE_PRODUCTUSER:"access_control/deleteRoleForProduct",
  DELETE_USER :"access_control/deleteUser",
   EDIT_USER:"access_control/editUser?product_name=Web%20Performance",
   EDIT_PACKAGE:"access_control/editPackage?product_name=Web%20Performance",
   CALL_RECOMMENDATION_GET:"api/v1/web/performance/get_call_recommendation",
   CALL_RECOMMENDATION_STATUS_UPDATE:"api/v1/web/performance/call_recommendation_status_update",
  CALL_RECOMMENDATION_UPDATE :"api/v1/web/performance/call_recommendation_update",
 GET_REAL_TIME_TABLE :"api/v1/web/performance/get_real_time",
 TOGGLE_CHANNEL_STATUS:"api/v1/web/performance/toggle_channel_status",
 TOGGLE_ACTION_STATUS:"api/v1/web/performance/toggle_action_status",

 WebBrand:{
   PUBLISHERS:"filters/publishers",
   PUBLISHER_ID:"filters/sub_publishers",
   CAMPAIGNS:"filters/campaigns",
   CHANNELS:"filters/channel",
   SUB_PUBLISHERS:"filters/sub_publishers",
   WASTAGE_CATEGORY:"filters/fraud_category",
   CAMPAIGNS_ID:"filters/campaign_id",
   CREATIVE_ID:"filters/creative_id",
   WASTAGE_SUB_CATEGORY:"filters/fraud_sub_category",
   FRAUD_CATEGORY_WISE_TRAFFIC :"fraud-category-wise-traffic",
   DEVICE_TYPE_WISE_TRAFFIC:"device-type-wise-traffic",
   SUMMARY:"summary",
   SOURCE_WISE_TRAFFIC:"publisher-wise-traffic",
   IMPRESSION_DISTRIBUTION_BY_TIME:"impression-distribution-by-time",
   LOCATION_WISE_TRAFFIC:"location-wise-traffic",
   RNF_TABLE:"rnf-table",
   RNF_UNIQUE_COUNTS:"rnf-unique-counts-dispersion",
   QUARTILE_PROGRESSION:"quartile-progression",
   FULL_FUNNEL_JOURNEY:"full-funnel-journey",
   SHOW_WISE_IMPRESSION:"show-wise-impression", 
  RNF_IMPRESSION_COUNTS :"rnf-impression-counts-distribution",
  DISPLAY_DAY_WISE_TREND:"display/day_wise_trend",
  DISPLAY_GEO_DISTRIBUTION:"display/geo_distribution",
  DISPLAY_TOP_PLACEMENTS_ASC:"display/top_placements_asc",
  DISPLAY_TOP_PLACEMENTS_DESC:"display/top_placements_desc",
  DISPLAY_CREATIVE_WISE_TRAFFIC:"display/creative-wise-traffic",
  DISPLAY_FRAUD_CATEGORY_WISE_TRAFFIC:"display/fraud-category-wise-traffic",
  FRAUD_DISTRIBUTION_BY_DEVICE_MODEL:"display/fraud-distribution-by-device-model",
  FRAUD_DISTRIBUTION_BY_DEVICE_MAKE:"display/fraud-distribution-by-device-make",
  DISPLAY_FRAUD_DISTRIBUTION_BY_PUBLISHER:"display/fraud-distribution-by-publisher",
  IMPRESSION_DISTRIBUTION_BY_CAMPAIGN:"display/impression-distribution-by-campaign",
  IMPRESSION_DISTRIBUTION_BY_CREATIVE:"display/impression-distribution-by-creative",
  DISPLAY_SUMMARY:"display/summary",
  DISPLAY_DEVICE_TYPE_WISE_TRAFFIC:"display/device-type-wise-traffic",
  DISPLAY_CAMPAIGNS:"display/filters/campaigns",
  DISPLAY_PUBLISHERS:"display/filters/publishers",
  DISPLAY_SUB_PUBLISHERS:"display/filters/sub_publishers",
  DISPLAY_CREATIVE_ID:"display/filters/creative_id",
  DISPLAY_CAMPAIGN_ID:"display/filters/campaign_id",
  DISPLAY_FRAUD_CATEGORY:"display/filters/fraud_category",
  DISPLAY_FRAUD_SUB_CATEGORY:"display/filters/fraud_sub_category",
  DISPLAY_CHANNELS:"display/filters/channel",
  REPORT_CREATE_MAILING_LIST:"create_mailing_list",
  REPORT_LIST_MAILING_LIST:"list_all_mailing_lists",
  REPORT_GET_MAILING_LIST_BY_NAME:"get_mailing_list_by_name",
 REPORT_EDIT_MAILING_LIST:"edit_mailing_list",
 REPORT_STATUS_MAIL_UPDATE_API:"status_change_mailing_list",
 REPORT_TEMPLATE:"get_templates",
 REPORT_TEMPLATE_FIELDS:"get_template_fields",
 REPORT_CUSTOM_TEMPLATE:"custome_template",
 REPORT_EDIT_API:"edit_report",
 REPORT_CREATE_API:"create_report",
 REPORT_DOWNLOAD_API:"create_report",
 REPORT_THRESHOLD_API:"threshold",
 REPORT_CATEGORY:"categories" ,
 REPORT_CLONE_API:"copy_report",
 REPORT_DELETE_API:"delete_report",
 REPORT_GET_API :"get_all_reports",
 REPORT_STATUS_UPDATE_API:"update_status",
 VIEW_REPORT_API:"view_report",
 
 }
}
export  default Endpoint;
