import Endpoint from "@/common/endpoint";
import { APICall } from "@/services";
import { useRouter } from "next/navigation";
import { useMutation } from "react-query";

type ErrorResponse = {
  message: string;
};

export type VerifySoftwareTokenBodyType = {
  access_token: string;
  user_code: string;
};

/**
 * Hook to verify software token
 * 
 * Usage with ToastContent (similar to SignOut component):
 * 
 * const [toastData, setToastData] = React.useState<any>(null);
 * 
 * const { mutate } = useVerifySoftwareToken(
 *   // onError callback
 *   (error) => {
 *     setToastData({
 *       type: "error",
 *       title: "Verification Failed",
 *       description: error.message || "Failed to verify token",
 *       variant: "default"
 *     });
 *   },
 *   // onSuccess callback
 *   (data) => {
 *     if (data?.message === "Access token is valid." && data?.status_code === 200) {
 *       setToastData({
 *         type: "success",
 *         title: "Success",
 *         description: "Access token is valid.",
 *         variant: "default"
 *       });
 *     }
 *   }
 * );
 * 
 * // In your JSX:
 * {toastData && (
 *   <ToastContent
 *     type={toastData.type}
 *     title={toastData.title}
 *     description={toastData.description}
 *     variant={toastData.variant}
 *   />
 * )}
 */
export function useVerifySoftwareToken(onSuccess: (data: unknown) => void) {
  const authDomain = process.env.NEXT_PUBLIC_AUTH_DOMAIN || "https://auth.mfilterit.com/";
  const url = authDomain + Endpoint.VERIFY_SOFTWARE_TOKEN;
  
  return useMutation(
    APICall({
      url,
      method: "POST",
      headers: {
        Authorization: typeof window !== "undefined" ? localStorage.getItem("IDToken") || "" : "",
      },
    }),
    { 
      onSuccess: (data: any) => {
        // Call the original onSuccess callback
        onSuccess(data);
      },
    },
  );
}
