"use client";

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, <PERSON><PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, LabelList } from "recharts";
import { formatValue, cn } from "@/lib/utils";
import { ChartContainer, ChartLegend,ChartLegendContent, ChartTooltip, ChartTooltipContent, CustomTick } from "@/components/ui/chart";
import HeaderRow from "../HeaderRow";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { useFullscreen } from "@/hooks/use-fullscreen";
import CustomLegendContent from "@/components/ui/CustomLegends";

interface data {
  label: string;
  [key: string]: string | number;
}

interface config {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface CustomTickProps {
  x: number;
  y: number;
  payload: {
    value: string;
  };
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  isFullscreen: boolean;
  axisType: string;
}
interface yaxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
  title?: string;

}
interface DynamicBarChartProps {
  data: data[];
  config: config;
  xAxisTitle?: string;
  yAxisTitle?: string;
  isHorizontal?: boolean;
  multiple?:string;
  title?: string;
  handleExport?: () => void;
  onExpand?: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  isRadioButton?: boolean;
  isSelect?: boolean;
  dynamicTitle?: string;
  formatterType?: "number" | "percentage"
  position?: string
  isLoading?: boolean
  AxisLabel?:string;
  selectoptions?:string[] ;
   placeholder?: string;
   handleFrequencyChange?: (value: string) => void; 
   width?:string;
   selectedFrequency?:string;
   isPercentage?:boolean;
   isMultiSelect?: boolean;
  multiSelectOptions?: { label: string; value: string }[];
  selectedMultiValues?: string[];
  handleMultiSelectChange?: (values: string[]) => void;
  multiSelectPlaceholder?: string;
  yAxisXOffset?: number;
  yAxisXOffsetFullscreen?: number;
   marginTop?:number;
  marginRight?:number;
  marginLeft?:number;
  marginBottom?:number;
  barsize?:number;
  height?:string;
  yaxis1?:yaxis1;
  yAxisPercentage?:boolean;
   rightAxisKeys?: string[];
   isexportcsv?:boolean;
   yaxisright?:boolean;
   isCustomLegendContent?:boolean;
   isLegend?:boolean;
   
}

export function DynamicBarChart({
  data,
  config,
  xAxisTitle,
  isLoading,
  yAxisTitle,
  isHorizontal = false,
  handleExport,
  onExport,
  onExpand,
  multiple,
   placeholder,
   handleFrequencyChange,
  isSelect,
  selectedFrequency,
  selectoptions=[],
  AxisLabel,
  isRadioButton,
  title,
  isPercentage,
  dynamicTitle,
  formatterType = "number",
  position = "right",
  width,
  // ... existing props ...
  isMultiSelect = false,
  multiSelectOptions = [],
  selectedMultiValues = [],
  handleMultiSelectChange,
  multiSelectPlaceholder = "Select options",
  yAxisXOffset,
   marginTop=10,
  marginRight=40,
  marginLeft=40,
  marginBottom=20,
  barsize=20,
    yAxisXOffsetFullscreen,
    height="250px",
    yAxisPercentage=false,
    rightAxisKeys = [],
    isexportcsv=false,
    yaxisright=false,
    isCustomLegendContent=false,
    isLegend=true,
}: DynamicBarChartProps) {

  const isFullscreen = useFullscreen();

  // Calculate responsive chart height based on fullscreen state
  const getChartHeight = () => {
    if (isFullscreen) {
      return 550; // Fixed height for fullscreen to prevent overflow
    }
    return Math.min((data?.length || 0) * 20, 500); // Normal height with null check
  };

  const chartHeight = getChartHeight();
  const labels = Object.values(config || {}).map(item => item.label);
  const colors = Object.values(config || {}).map(item => item.color);

  // const CustomSquareLegend = ({ payload }: { payload?: any[] }) => {
  //   if (!payload) return null;
  //   // Calculate dynamic height based on data length
  //   const baseHeight = 50; // Base height for each data entry
  //   const dynamicHeight = Math.max((data?.length || 0) * baseHeight, 200); // Minimum height of 200px with null check
  //   return (
  //     <div className="flex flex-wrap justify-center items-center gap-4 mt-2">
  //       {payload.map((entry, index) => (
  //         <div key={`legend-${index}`} className="flex items-center gap-2">
  //           <div
  //             style={{
  //               width: 8,
  //               height: 8,
  //               backgroundColor: entry.color,
  //               borderRadius: 2,
  //             }}
  //           ></div>
  //           <span className="text-small-font">{entry.value}</span>
  //         </div>
  //       ))}
  //     </div>
  //   );
  // };

  const chartWidth = (data ? data.length : 0) * 70;
  const dataKeys = Object.keys(config || {});

  
  const formatLabel = (value: number) => {
    if (formatterType === "percentage") {
      return `${value}%`;
    }else{
    return `${value}`;
    }
  };
  return (

    <Card className="flex flex-col border-none">
      <HeaderRow
        title={title}
        onExpand={onExpand}
        handleExport={handleExport}
        isRadioButton={isRadioButton}
        isSelect={isSelect}
        selectoptions={selectoptions}
        onExport={onExport}
        placeholder={placeholder}
        handleFrequencyChange={handleFrequencyChange}
        isMultiSelect={isMultiSelect}
        multiSelectOptions={multiSelectOptions}
        selectedMultiValues={selectedMultiValues}
        handleMultiSelectChange={handleMultiSelectChange}
        multiSelectPlaceholder={multiSelectPlaceholder}
        width={width}
        selectedFrequency={selectedFrequency}
        isexportcsv={isexportcsv}
      />
      <CardHeader className="items-center pb-0">
        <CardTitle className="text-body font-semibold">{dynamicTitle}</CardTitle>
      </CardHeader>
      <CardContent className={`flex-1 pb-0 chart-card-content ${isFullscreen ? 'h-[600px]' : 'max-h-[270px]'} ${isFullscreen ? '' : ''}`}>
        {isLoading ? (
          <div className="flex items-center justify-center  w-full h-[200px]">
            <Loader2 className=" h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (

          <ChartContainer config={config} style={{ height: isFullscreen ? "550px" : height, width: "100%" }}>
            <ResponsiveContainer height={isFullscreen ? 550 : 240}>
              {data?.length > 0 ? (
                <BarChart
                  data={data}
                  layout={isHorizontal ? "vertical" : "horizontal"}
                  width={chartWidth}
                  margin={{ 
                    left: isFullscreen ? (isHorizontal ? 80 : 40) : 4, 
                    right: marginRight, 
                    top: marginTop, 
                    bottom: marginBottom 
                  }}
                  barSize={isFullscreen ? 30 : barsize}
                  barGap={isFullscreen ? 40 : 30}
                  height={chartHeight}
                >

                  <CartesianGrid strokeDasharray="3 3" />
                  {isHorizontal ? (
                    <XAxis className="text-body"
                      type="number"
                      label={{ value: xAxisTitle || "", position: "insideBottom", offset: -5 }}
                      tickFormatter={(value) => formatValue(value as number, AxisLabel || "")}
                      style={{fontSize: isFullscreen ? '16px' : '12px'}}
                      interval={0}
                    />
                  ) : (
                    <XAxis className="text-body"
                      dataKey="label"
                      label={{ value: xAxisTitle || "", position: "insideBottom", offset: -5, }}
                      style={{fontSize: isFullscreen ? '16px' : '12px'}}
                      interval={0}
                    />
                  )}
                  {isHorizontal ? (
                    <YAxis className="mr-10 text-body"
                    yAxisId="left"
                      type="category"
                      dataKey="label"
                      label={{
                        value: yAxisTitle || "",
                        angle: -90,
                        position: "insideLeft",
                        offset: 0
                      }}
                      tickFormatter={(value) => {
                        const displayValue = value.length > 3 ? value.slice(0, 5) + "..." : value;
                        return displayValue; // Return string, not an element
                      }}
                      interval={0}
                      style={{fontSize: isFullscreen ? '16px' : '12px'}}
                      tick={(props) => <CustomTick {...props} chartConfig={config} isFullscreen={isFullscreen} axisType="y" yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />}
                    />
                  ) : (
                    <YAxis className="text-body"
                    yAxisId="left"
                      label={{
                        value: yAxisTitle || "",
                        angle: -90,
                        position: "insideLeft",
                      }}
                      tickFormatter={(value) => {
                        const displayValue = value.length > 3 ? value.slice(0, 5) + "..." : value;
                        return displayValue; // Return string, not an element
                      }}
                      interval={0}
                      style={{fontSize: isFullscreen ? '16px' : '12px'}}
                      tick={(props) => <CustomTick {...props} chartConfig={config} isFullscreen={isFullscreen} axisType="y" yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />}
                    />
                  )}
                  {yaxisright &&(
                    <YAxis
                        yAxisId="right"
                        orientation="right"
                        //tickFormatter={(value) => `${value}%`}
                        domain={[0, 100]}
                        axisLine={true}
                        rightAxisKeys={[rightAxisKeys]}
                        tickLine={false}
                        style={{fontSize: isFullscreen ? '16px' : '12px'}}
                        tick={(props) => <CustomTick {...props} chartConfig={config} isFullscreen={isFullscreen} axisType="y" yAxisPercentage={yAxisPercentage} Fullscreen={yAxisXOffsetFullscreen} />} />
                        
                  )}
                  <ChartTooltip content={<ChartTooltipContent isPercentage={isPercentage}/>} />
               {isLegend && (
                    <ChartLegend
                      content={<ChartLegendContent isFullscreen={isFullscreen} />}
                    />
                  )}
                  {/* <Legend content={(props) => <CustomSquareLegend {...props} />}/> */}
                  {dataKeys.map((key) => (
                    <Bar key={key} dataKey={key} fill={config[key].color} radius={0} 
                    yAxisId={rightAxisKeys?.includes(key) ? "right" : "left"}>
                      
                      {/* Add LabelList here to show labels on the bars */}
                      <div className="dark:text-white">
                        <LabelList
                          dataKey={key}
                          position={position as any}
                          style={{ fontSize: isFullscreen ? "12px" : "8px", fill: "#000" }}
                          formatter={formatLabel}
                        />
                      </div>
                    </Bar>
                  ))}
                </BarChart>
              ) : (<div className="flex items-center justify-center w-full h-full">
                <span className="text-small-font">  No Data Found.!</span>
                
              </div>)}
            </ResponsiveContainer>
          </ChartContainer>
        )}
         {isCustomLegendContent && (
            <div className={` bottom-0 z-10  pb-4 sm:overflow-visible md:overflow-visible ${isFullscreen ? 'pt-4' : 'pt-0'}`}>
            <CustomLegendContent labels={labels} colors={colors} />
          </div>
        )}
      </CardContent>

    </Card>
  );
}
export default DynamicBarChart;
