"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { z } from "zod"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Plus, Trash2 } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Footer, <PERSON>alogClose } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import EllipsisTooltip from "@/components/mf/EllipsisTooltip"
import { MultiSelect } from "@/components/ui/multi-select"
import {useEffect} from "react";

// Generic types for dynamic configuration
interface FieldConfig {
  name: string
  label: string
  placeholder?: string
  required: boolean
  disabled?: boolean
  dependsOn?: string
  width?: string
  showOnlyForFirstRow?: boolean
  isMultiple?: boolean
  type?: "input" | "select" | "multiselect"
}

interface OptionData {
  value: string
  label: string
  disabled?: boolean
}

interface DynamicSelectData {
  [fieldName: string]: {
    [rowIndex: number]: OptionData[]
  }
}

interface LoadingStates {
  [rowIndex: number]: boolean
}

interface ValidationConfig {
  userField: {
    name: string
    message: string
  }
  dynamicFields: {
    name: string
    minItems: number
    message: string
    fields: {
      [key: string]: {
        message: string
      }
    }
  }
}

interface DynamicFieldValues {
  [key: string]: string | string[];
  name: string;
  email: string;
  Products: string;
  Role: string;
  Packages: string[];
}

interface FormValues {
  user: string;
  fields: DynamicFieldValues[];
}

interface EditFormValues extends DynamicFieldValues {
  name: string;
  email: string;
  Products: string;
  Role: string;
  Packages: string[];
}

interface FieldArrayItem {
  id: string;
  [key: string]: any;
}

interface DynamicFormDialogProps {
  // Dialog configuration
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  mode?: "create" | "edit" | "view"

  // Form configuration
  userFieldConfig: FieldConfig
  dynamicFieldsConfig: FieldConfig[]
  validationConfig: ValidationConfig

  // Data
  staticData: {
    [fieldName: string]: OptionData[]
  }
  dynamicData: DynamicSelectData
  loadingStates: LoadingStates
  globalLoading?: boolean

  // Callbacks
  onSubmit: (data: any) => void
  onFieldChange: (fieldName: string, value: string | string[], rowIndex: number, formSetValue: any) => void
  onUserSelect?: (value: string) => void
  handleAddField?: () => void

  // UI Configuration
  labels: {
    cancel: string
    submit: string
    loading: string
    noDataFound: string
    actions: string
  }

  // Button configuration
  addButtonConfig?: {
    icon?: React.ReactNode
    className?: string
  }
  removeButtonConfig?: {
    icon?: React.ReactNode
    className?: string
  }

  // Add a prop to receive initial values for edit mode
  initialValues?: {
    [key: string]: any;
  };
}

const DynamicFormDialog: React.FC<DynamicFormDialogProps> = ({
  open,
  onOpenChange,
  title,
  mode = "create",
  userFieldConfig,
  dynamicFieldsConfig,
  validationConfig,
  staticData,
  dynamicData,
  loadingStates,
  globalLoading = false,
  onSubmit,
  onFieldChange,
  onUserSelect,
  handleAddField,
  labels,
  addButtonConfig,
  removeButtonConfig,
  initialValues,
}) => {
  // Add search state and ref
  const [searchQuery, setSearchQuery] = useState("");
  const searchInputRef = React.useRef<HTMLInputElement>(null);

  // Reset search query when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setSearchQuery("");
    }
  }, [open]);

  // Add function to clear search query
  const clearSearchQuery = () => {
    setSearchQuery("");
    if (searchInputRef.current) {
      searchInputRef.current.value = "";
    }
  };

  // Create dynamic validation schema
  // const createValidationSchema = () => {
  //   const fieldsSchema: { [key: string]: z.ZodType<any> } = {};
  //   dynamicFieldsConfig.forEach((field) => {
  //     if (field.isMultiple) {
  //       fieldsSchema[field.name] = field.required
  //         ? z.array(z.string()).min(1, validationConfig.dynamicFields.fields[field.name]?.message || `${field.label} is required`)
  //         : z.array(z.string()).optional();
  //     } else {
  //       fieldsSchema[field.name] = field.required
  //         ? z.string().min(1, validationConfig.dynamicFields.fields[field.name]?.message || `${field.label} is required`)
  //         : z.string().optional();
  //     }
  //   });
  //   return z.object({
  //     user: z.string().min(1, validationConfig.userField.message),
  //     fields: z
  //       .array(z.object({
  //         name: z.string().min(1, "Name is required"),
  //         email: z.string().email("Invalid email address"),
  //         Products: z.string().min(1, "Product is required"),
  //         Role: z.string().min(1, "Role is required"),
  //         Packages: z.array(z.string()).min(1, "At least one package is required"),
  //       }))
  //       .min(validationConfig.dynamicFields.minItems, validationConfig.dynamicFields.message),
  //   });
  // };

  // const formSchema = createValidationSchema()

  // Create default values
  const createDefaultValues = (): FormValues => {
    if (mode === "edit" && initialValues) {
      return {
        user: initialValues.name || "",
        fields: [
          {
            name: initialValues.name || "",
            email: initialValues.email || "",
            Products: initialValues.Products || "",
            Role: initialValues.Role || "",
            Packages: initialValues.Packages || [],
          },
        ],
      };
    }
    return {
      user: "",
      fields: [
        {
          name: "",
          email: "",
          Products: "",
          Role: "",
          Packages: [],
        },
      ],
    };
  }

  const form = useForm<FormValues>({
    // resolver: zodResolver(formSchema),
    defaultValues: createDefaultValues(),
  })

  //console.log("🧾 Form defaultValues after useForm:", form.getValues());
  const { control, watch, reset, setValue, getValues, trigger } = form

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  })

  useEffect(() => {
    if (mode === "edit") {
      const values = getValues();
      //console.log("Edit mode watch values:", values.fields?.[0]);
    } else {
     // console.log("Create mode watch values:", watch());
    }
  }, [watch(), mode, getValues, "fields"]);

  // Reset form when dialog opens
  React.useEffect(() => {
    if (open) {
      // console.log("🔵 Dialog opened in RowAddingDialog");
      // console.log("🔵 Mode:", mode);
      // console.log("🔵 Initial Values:", initialValues);
      
      if (mode === "edit" && initialValues) {
        // For edit mode, set values from initialValues
        const formValues: FormValues = {
          user: initialValues.name || "",
          fields: [{
            name: initialValues.name || "",
            email: initialValues.email || "",
            Products: initialValues.Products || "",
            Role: initialValues.Role || "",
            Packages: initialValues.Packages || []
          }]
        };
       // console.log("🔵 Setting form values:", formValues);
        reset(formValues);
        
        // Force a re-render of the form fields
        setTimeout(() => {
          const currentValues = getValues();
          //console.log("🔵 Current form values after reset:", currentValues);
          
          // Set each field individually to ensure they are updated
          setValue("user", formValues.user);
          setValue("fields.0.name", formValues.fields[0].name);
          setValue("fields.0.email", formValues.fields[0].email);
          setValue("fields.0.Products", formValues.fields[0].Products);
          setValue("fields.0.Role", formValues.fields[0].Role);
          setValue("fields.0.Packages", formValues.fields[0].Packages);
        }, 0);
      } else {
        //console.log("🔵 Resetting to default values");
        reset(createDefaultValues());
      }
    }
  }, [open, reset, mode, initialValues, "user", "fields", setValue, getValues]);

  // Add a watch effect to monitor form values
  useEffect(() => {
    const subscription = watch((value) => {
      //console.log("🔵 Form values changed:", value);
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  const handleAddNewField = () => {
    if (mode === "edit") {
      // No need to add fields in edit mode
      return;
    }
    
    const defaultFieldValues: DynamicFieldValues = {
      name: "",
      email: "",
      Products: "",
      Role: "",
      Packages: []
    }
    append(defaultFieldValues as any)
    if (handleAddField) {
      handleAddField()
    }
  }

  const handleRemoveField = (index: number) => {
    if (mode === "edit") {
      // No need to remove fields in edit mode
      return;
    }
    if (fields.length > 1) remove(index)
  }

  const userSelected = mode === "edit" ? true : watch("user") ? true : false

  const isRowComplete = (index: number) => {
    if (mode === "edit") {
      const values = getValues().fields?.[0] as EditFormValues;
      return Boolean(
        values?.name &&
        values?.email &&
        values?.Products &&
        values?.Role &&
        values?.Packages?.length > 0
      );
    }

    const fieldsData = getValues().fields as DynamicFieldValues[]
    if (!fieldsData?.[index]) return false

    return dynamicFieldsConfig.every((fieldConfig) => {
      if (!fieldConfig.required) return true
      return fieldsData[index][fieldConfig.name] !== ""
    })
  }

  const areAllRowsComplete = () => {
    if (mode === "edit") {
      return isRowComplete(0);
    }

    const fieldsData = getValues().fields as DynamicFieldValues[]
    if (!fieldsData || fieldsData.length === 0) return false

    return fieldsData.every((field: DynamicFieldValues, index: number) => isRowComplete(index))
  }

  const isFormValid = userSelected && areAllRowsComplete()
  const lastRowIndex = mode === "edit" ? 0 : (watch("fields")?.length - 1 || 0)
  const isLastRowComplete = isRowComplete(lastRowIndex)

  // Watch for field changes to trigger validation
  if (mode !== "edit") {
    watch("fields")
  }

  const handleSubmit = (data: FormValues) => {
    //console.log("Form submission data:", data);

    if (!areAllRowsComplete()) {
      trigger()
      return
    }

    onSubmit(data)

    // Reset form after successful submission
    reset(createDefaultValues())
  }

  const handleFieldChange = (fieldName: string, value: string | string[], index: number) => {
    //console.log("🔵 Field change:", { fieldName, value, index });
    if (mode === "edit") {
      const path = `fields.0.${fieldName}`;
     // console.log("🔵 Setting field value:", { path, value });
      setValue(path as any, value);
      return;
    }

    // Clear dependent fields only, not the current field
    const dependentFields = dynamicFieldsConfig.filter((field) => field.dependsOn === fieldName)
    dependentFields.forEach((depField) => {
      setValue(`fields.${index}.${depField.name}` as any, depField.isMultiple ? [] : "")
    })

    // Call the parent's field change handler
    onFieldChange(fieldName, value, index, setValue)
  }

  const handleUserChange = (value: string) => {
    if (onUserSelect) {
      onUserSelect(value)
    }
  }

  const getFieldWidth = (fieldConfig: FieldConfig, isFirstField: boolean) => {
    if (fieldConfig.width) return fieldConfig.width
    if (isFirstField) return "w-full pl-2 md:w-1/5 md:mr-4 "
    return "w-full  md:flex-1 md:mx-2"
  }

  const renderField = (fieldConfig: FieldConfig, index: number, isUserField = false) => {
    const fieldName = isUserField
      ? userFieldConfig.name
      : `fields.${index}.${fieldConfig.name}`

    const dependsOnValue = fieldConfig.dependsOn
      ? watch(`fields.${index}.${fieldConfig.dependsOn}` as any)
      : null

    const dependsOnUser = !isUserField && fieldConfig.dependsOn === "user"
    const userValue = dependsOnUser ? watch("user") : null

    const isDisabled = Boolean(
      fieldConfig.disabled || 
      (!isUserField && !userValue && fieldConfig.dependsOn === "user") ||
      (fieldConfig.dependsOn && !dependsOnValue && fieldConfig.dependsOn !== "user")
    )

    const options = isUserField ? staticData[userFieldConfig.name] || [] : dynamicData[fieldConfig.name]?.[index] || []
    const isLoading = !isUserField && loadingStates[index]

    // Filter options based on search query
    const filteredOptions = options.filter(option => 
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
      <FormField
        control={control}
        name={fieldName as any}
        render={({ field }) => {
          const fieldValue = field.value;
          const isArrayValue = Array.isArray(fieldValue);
          const stringValue = typeof fieldValue === 'string' ? fieldValue : '';

          return (
            <FormItem className="pl-2 mb-0.5">
              <FormControl>
                {fieldConfig.type === "input" ? (
                  <input
                    type="text"
                    className="flex h-7 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                    value={stringValue}
                    onChange={(e) => field.onChange(e.target.value)}
                    disabled={isDisabled}
                    placeholder={fieldConfig.placeholder}
                  />
                ) : fieldConfig.type === "multiselect" || fieldConfig.isMultiple ? (
                  <MultiSelect
                    options={options}
                    value={isArrayValue ? (fieldValue as unknown as string[]) : []}
                    onValueChange={(values) => {
                      field.onChange(values);
                      if (!isUserField) {
                        setTimeout(() => {
                          handleFieldChange(fieldConfig.name, values, index);
                        }, 0);
                      }
                    }}
                    placeholder={fieldConfig.placeholder}
                    disabled={isDisabled}
                    className="h-7 text-sm"
                  />
                ) : (
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      if (!isUserField) {
                        setTimeout(() => {
                          handleFieldChange(fieldConfig.name, value, index);
                        }, 0);
                      }
                    }}
                    value={stringValue}
                    disabled={isDisabled}
                    onOpenChange={(open) => {
                      if (open) {
                        clearSearchQuery();
                      }
                    }}
                  >
                    <SelectTrigger className="text-sm h-7">
                      {/* Custom rendering for selected value */}
                      <SelectValue placeholder={fieldConfig.placeholder}>
                        {
                          // Find the label for the selected value
                          options.find(opt => opt.value === stringValue)?.label || ""
                        }
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      <div className="px-2 pb-2">
                        <input
                          ref={searchInputRef}
                          type="text"
                          placeholder="Search..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          onKeyDown={(e) => {
                            // Prevent select from closing when typing
                            e.stopPropagation();
                          }}
                          className="flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                        />
                      </div>
                      {(globalLoading && isUserField) || isLoading ? (
                        <div className="px-2 py-0.5 text-sm text-muted-foreground">{labels.loading}</div>
                      ) : filteredOptions.length > 0 ? (
                        filteredOptions.map((option, idx) => (
                          <SelectItem key={idx} value={option.value} disabled={option.disabled}>
                            {isUserField ? (
                              <EllipsisTooltip content={option.label} className="max-w-[180px]" />
                            ) : (
                              option.label
                            )}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="_no_data" disabled>
                          {labels.noDataFound}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto p-4">
        <DialogHeader className="font-semibold pb-1">{title}</DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="">
            {/* Dynamic Form Fields */}
            {fields.map((field: FieldArrayItem, index, array) => (
              <div key={field.id} className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50 shadow-sm">
                {index > 0 && <div className="text-xs text-gray-500 mb-3 font-medium">Row {index + 1}</div>}
                {/* Single Row: User, Product, Role, Package */}
                <div className="flex flex-col md:flex-row md:items-center md:space-y-0 mb-4">
                  {/* User Selection Field */}
                  <div className="w-full md:w-1/4 md:mr-4">
                    {(index === 0 || !userFieldConfig.showOnlyForFirstRow) && (
                      <div>
                        <FormLabel className="font-semibold block text-sm mb-1">
                          {userFieldConfig.label}
                        </FormLabel>
                        {renderField(userFieldConfig, index, true)}
                      </div>
                    )}
                  </div>

                  {/* Product Field */}
                  <div className="w-full md:w-1/4 md:mr-4">
                    <FormLabel className="font-semibold block text-sm mb-1">Products</FormLabel>
                    {renderField(dynamicFieldsConfig[0], index)}
                  </div>

                  {/* Role Field */}
                  <div className="w-full md:w-1/4 md:mr-4">
                    <FormLabel className="font-semibold block text-sm mb-1">Role</FormLabel>
                    {renderField(dynamicFieldsConfig[1], index)}
                  </div>

                  {/* Package Field */}
                  <div className="w-full md:w-1/4">
                    <FormLabel className="font-semibold block text-sm mb-1">Package</FormLabel>
                    {renderField(dynamicFieldsConfig[2], index)}
                  </div>
                </div>

                {/* Action Buttons */}
                {mode !== "edit" && (
                  <div className="flex justify-end mt-4">
                    <div className="flex space-x-2">
                      {index > 0 && (
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className={removeButtonConfig?.className || "h-8 w-8"}
                          onClick={() => handleRemoveField(index)}
                          disabled={array.length <= 1 || !userSelected}
                        >
                          {removeButtonConfig?.icon || <Trash2 className="h-4 w-4" />}
                        </Button>
                      )}
                      {index === array.length - 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className={addButtonConfig?.className || "h-8 w-8 hover:text-white"}
                          onClick={handleAddNewField}
                          disabled={!userSelected}
                        >
                          {addButtonConfig?.icon || <Plus className="h-5 w-5 text-primary" />}
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}

            <DialogFooter className="mt-6 space-x-2">
              <DialogClose asChild>
                <Button type="button" variant="default" className="bg-primary text-white hover:bg-secondary">
                  {labels.cancel}
                </Button>
              </DialogClose>
              <Button
                type="submit"
                variant="default"
                className="bg-primary text-white hover:bg-secondary"
                disabled={!isFormValid}
              >
                {labels.submit}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default DynamicFormDialog
