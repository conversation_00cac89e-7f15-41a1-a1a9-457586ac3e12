import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
  search_term:params.search_term,
  limit:params.limit,
  page:params.page,
});

export const usecreativewiseTraffic = (params: any, enabled = true) =>
  useAppQuery(
    ['creativewiseTraffic', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_CREATIVE_WISE_TRAFFIC}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  
  export const usefraudCategoryWiseTraffic = (params: any, enabled = true) =>
    useAppQuery(
      ['fraudCategoryWiseTraffic', JSON.stringify(params)],
      () =>
        apiCall({
          url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_FRAUD_CATEGORY_WISE_TRAFFIC}`,
          method: 'POST',
          data:getDefaultPayload(params),
        }),
      { enabled }
    );
     
export const usefraudDistributionByDeviceModel = (params: any, enabled = true) =>
    useAppQuery(
      ['fraudDistributionByDeviceModel', JSON.stringify(params)],
      () =>
        apiCall({
          url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.FRAUD_DISTRIBUTION_BY_DEVICE_MODEL}`,
          method: 'POST',
          data:getDefaultPayload(params),
        }),
      { enabled }
    );

    export const usefraudDistributionByDeviceMake = (params: any, enabled = true) =>
        useAppQuery(
            ['fraudDistributionByDeviceMake', JSON.stringify(params)],
            () =>
              apiCall({
                url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.FRAUD_DISTRIBUTION_BY_DEVICE_MAKE}`,
                method: 'POST',
                data:getDefaultPayload(params),
              }),
            { enabled }
          );

          export const usefraudDistributionByPublisher = (params: any, enabled = true) =>
            useAppQuery(
                ['fraudDistributionByPublisher', JSON.stringify(params)],
                () =>
                  apiCall({
                    url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_FRAUD_DISTRIBUTION_BY_PUBLISHER}`,
                    method: 'POST',
                    data:getDefaultPayload(params),
                  }),
                { enabled }
              );

              export const useimpressionDistributionByCampaign = (params: any, enabled = true) =>
                useAppQuery(
                    ['impressionDistributionByCampaign', JSON.stringify(params)],
                    () =>
                      apiCall({
                        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.IMPRESSION_DISTRIBUTION_BY_CAMPAIGN}`,
                        method: 'POST',
                        data:getDefaultPayload(params),
                      }),
                    { enabled }
                  );
                  export const useimpressionDistributionByCreative = (params: any, enabled = true) =>
                    useAppQuery(
                        ['impressionDistributionByCreative', JSON.stringify(params)],
                        () =>
                          apiCall({
                            url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.IMPRESSION_DISTRIBUTION_BY_CREATIVE}`,
                            method: 'POST',
                            data:getDefaultPayload(params),
                          }),
                        { enabled }
                      );
            export const useDeviceTypeWiseTraffic = (params: any, enabled = true) =>
              useAppQuery(
                ['deviceTypeWiseTraffic', JSON.stringify(params)],
                () =>
                  apiCall({
                    url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_DEVICE_TYPE_WISE_TRAFFIC}`,
                    method: 'POST',
                    data:getDefaultPayload(params),
                  }),
                { enabled }
              );
