"use client"

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>hart, CartesianGrid, XAxis, YAxis, Label, ResponsiveContainer} from "recharts"
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
  CustomTick,
} from "@/components/ui/chart";
import HeaderRow from "../HeaderRow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatValue, formatNumber } from "@/lib/utils";
import { InformationCard } from "../InformationCard";
import { Loader2 } from "lucide-react";
import { useFullscreen } from "@/hooks/use-fullscreen";
import CustomLegendContent from "@/components/ui/CustomLegends";

interface ChartBarStackedProps {
  handleExport?: () => void;
  onExpand: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  isexportcsv?:boolean;
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  heading?:string;
  bargap?:number;
  isInformCard?:boolean;
  layoutDirection?:string;
  isLegend?:boolean;
  ischangeLegend?:boolean;
  placeholder?:string;
  isLoading?:boolean;
  selectoptions?:string[];
  selectedFrequency?:string;
  handleFrequencyChange?: (value: string) => void; 
  isCartesian?:boolean;
  isPercentage?:boolean;
  truncateLength?: number;
  truncateLengthx?: number;
  yAxisXOffset?: number;
  yAxisXOffsetFullscreen?: number;
  marginTop?:number;
  marginRight?:number;
  marginLeft?:number;
  marginBottom?:number;
  barsize?:number;
  fullscreenbarsize?:number;
  dy?:number;
  fullscreenDy?:number;
  isCustomLegendContent?:boolean;
  graphheight?:number;
  /**
   * Height of the chart area in pixels. Default is 280.
   */
  height?: number;

  //sub_heading?:string
 InformCard?:{title:string,desc:string}[];
  chartData?: 
    {label: string;
    [key: string]: string | number;}[]
    chartConfig?: {
      [key: string]: {
        label: string;
        color: string;
      };
    };
  xAxis?: {
    dataKey?: string
    title?: string
    tickFormatter?: (value: string | number) => string
    isPercentage?: boolean  // Add this to control percentage display
  }
  yAxis?: {
    dataKey: string
    title?: string
    tickFormatter?: (value: string ) => string
  }
  isHorizontal?: boolean;
  AxisLabel?:string;
}

const ChartBarStacked:React.FC<ChartBarStackedProps> = ({ 
  heading ="heading",
  //sub_heading,
    handleTypeChange,
    visitEventOptions,
    isCartesian,
    graphheight=0,
    bargap=30,
    selectedType,
    handleExport,
    onExport,
    selectoptions =[],
    onExpand,
    handleFrequencyChange,
    title ,
    isSelect= false,
    isRadioButton =false,
    chartData=[],
    chartConfig,
    xAxis,
    yAxis,
    isPercentage=false,
    selectedFrequency,
    placeholder,
    isHorizontal,
    AxisLabel= "Value",
    InformCard=[],
    isInformCard=false,
    layoutDirection ="flex-col",
    isLegend=true,
    ischangeLegend=false,
    isLoading,
    truncateLength = 8,
    truncateLengthx = 8,
    yAxisXOffset = -12,
    yAxisXOffsetFullscreen = -12,
    height = 280,
    marginTop=20,
    marginRight=40,
    marginLeft=20,
    marginBottom=5,
    barsize=20,
    fullscreenbarsize=30,
    dy=4,
    fullscreenDy=10,
    isexportcsv=false,
    isCustomLegendContent=false,
  }) => {
    const isFullscreen = useFullscreen();
  const labels = Object.values(chartConfig || {}).map(item => item.label);
  const colors = Object.values(chartConfig || {}).map(item => item.color);
    // Calculate responsive chart height based on fullscreen state
const getChartHeight = () => {
  if (isFullscreen) return 550;
  //if (isHorizontal) return Math.max(300, chartData.length * 80);  // 80px per bar
  return height;
};
 const chartHeight = getChartHeight();
  const barSize = isFullscreen ? 30 : 20;
const barGap = isFullscreen ? 40 : 30;
const barCategoryGap = isFullscreen ? 0.2 : 0.1; // "20%" / "10%" in decimal

// calculate row height based on barSize + effective gap
const rowHeight = barSize + barGap * (1 - barCategoryGap);

const maxVisibleItems = 10;
const chartHeightAvailable = chartHeight - graphheight;

// final dynamic height
const dynamicHeight = isFullscreen
  ? chartHeightAvailable // always take full available space in fullscreen
  : chartData.length > maxVisibleItems
    ? chartHeightAvailable // cap at available height if > 10
    : chartData.length ; // // shrink based on rows if ≤ 10

    return (
   <Card className="border-none">
   <HeaderRow
      visitEventOptions={visitEventOptions}
      handleTypeChange={handleTypeChange}
      selectoptions={selectoptions}
      selectedType={selectedType}
      title={title}
      handleFrequencyChange={handleFrequencyChange}
      selectedFrequency={selectedFrequency}
      onExpand={onExpand}
      handleExport={handleExport}
      isRadioButton={isRadioButton}
      isSelect={isSelect}
      onExport={onExport}
      heading={heading}
      placeholder={placeholder}
      isexportcsv={isexportcsv}
/>
{isInformCard &&(
  <div className={`flex-1 px-4 flex flex-row ${isFullscreen ? 'gap-4 py-4' : ''}`}>
  {InformCard?.map((item, index) => (
        <InformationCard
          key={index}
          InformTitle={item.title}
          informDescription={item.desc}
          isFullscreen={isFullscreen}
        />
      ))}
  </div>
    )}
{/* <CardHeader>
  <CardTitle>
  </CardTitle>
</CardHeader> */}
 {isLoading ? (
      <div className="flex items-center justify-center h-[280px] overflow-hidden">
            <Loader2 className=" h-8 w-8 animate-spin text-primary" />
       </div>
     ):(
<CardContent className={`w-full h-full p-0 chart-card-content  ${isFullscreen ? '' : chartData.length > 0 ? 'overflow-hidden' : 'overflow-hidden'}`} style={{height: isFullscreen ? 600 : chartHeight}}>
  {chartData.length>0 ?(
    <div className="flex flex-col w-full h-full">
      {/* Chart Container */}
      {isHorizontal ? (
        <div className="flex-1 w-full">
          <ChartContainer config={chartConfig || {}} style={{ height: isFullscreen ? '550px' : "", width: '100%' }}
>                <div
      style={{
        overflowX: "auto",
        width: "100%",
      
      }}
    >
      <div className="overflow-x-auto scrollbar"
        style={{
           minWidth: `${chartData.length * 120}px`, // Dynamically stretch chart width
          height: isFullscreen ? "550px" :chartHeight-graphheight ,
        }}
      >
            <ResponsiveContainer height="100%" width="100%" >
            
              <BarChart
                data={chartData}
                layout="horizontal"
               //height= {280}
                margin={{ left: isFullscreen ? 50 : marginLeft, right: marginRight, top: marginTop, bottom: marginBottom }}
                barSize={isFullscreen ? fullscreenbarsize : barsize}
                barGap={isFullscreen ? 40 : 30}
            
              >
                {isCartesian &&(
                  <CartesianGrid strokeDasharray="2 2" stroke="#555" strokeWidth={0.5} horizontal={isHorizontal} vertical={!isHorizontal} />
                )}
                <XAxis 
                  className="text-body"
                  dataKey={isHorizontal ? xAxis?.dataKey : undefined}
                  type={isHorizontal ? 'category' : 'number'}
                  tickLine={false} 
                  axisLine={true}
                  tickFormatter={isHorizontal 
                    ? undefined 
                    : (value: string | number) => {
                        if (typeof value === 'number') {
                          if (xAxis?.isPercentage) {
                            return `${value}%`;
                          }
                          return formatNumber(value);
                        }
                        return value;
                      }
                  }
                  interval={0}
                  angle={isHorizontal ? -45 : 0}
                  textAnchor={isHorizontal ? 'end' : 'start'}
                  height={isHorizontal ? 80 : 30}
                  style={{fontSize: isFullscreen ? '16px' : '10px'}}
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} dy={dy} fullscreenDy={fullscreenDy}  axisType="x" truncateLength={truncateLengthx} textAnchor='end' xOffset={15} />}
                >
                  {isHorizontal && <Label style={{fontSize:'10px'}} value={xAxis?.title} offset={-20} position="insideBottom" />}
                </XAxis>
                {yAxis && (
                  <YAxis
                    className="text-body"
                    dataKey={isHorizontal ? undefined : yAxis.dataKey}
                    type={isHorizontal ? 'number' : 'category'}
                    tickLine={false}
                    axisLine={true} 
                    width={isHorizontal ? 50 : undefined}
                    tickMargin={isFullscreen ? 20 : 15}
                    interval={0}
                    height={isHorizontal ? 80 : 500}
                    textAnchor="start"
                    style={{fontSize: isFullscreen ? '16px' : '10px'}}
                    tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" textAnchor="start" truncateLength={truncateLength} yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />} 
                  >
                    {!isHorizontal && <Label style={{fontSize: isFullscreen ? '16px' : '10px'}} value={yAxis.title} angle={-90} position={isFullscreen ? { x: -20, y: 70 } : { x: -10, y: 60 }} offset={-20} />}
                  </YAxis>
                )}
                <ChartTooltip content={<ChartTooltipContent isPercentage={isPercentage} />} />
                {isLegend && (
                  <ChartLegend
                    content={<ChartLegendContent isFullscreen={isFullscreen} />}
                  />
                )}
                {/* Chart Bars */}
                {chartConfig &&
                  Object.keys(chartConfig).map((key) => (
                    <Bar key={key} dataKey={key} stackId="a" fill={chartConfig[key].color} minPointSize={3}/>
                  ))}
              </BarChart>
             
            </ResponsiveContainer>
             
            </div>
            </div>
             {isCustomLegendContent && (
            <div className={` bottom-0 z-10  pb-4 sm:overflow-visible md:overflow-visible ${isFullscreen ? 'pt-4' : 'pt-0'}`}>
            <CustomLegendContent labels={labels} colors={colors} />
          </div>
        )}
            </ChartContainer>
                  
        </div>
      ) : (
        // Vertical chart: enable horizontal scroll if many bars
        <div className="flex-1 w-full ">
          <div style={{ height: chartHeight, minWidth: '100%' }}>
            <div className="overflow-y-auto scrollbar "  
           style={{
    height: isFullscreen
      ? "100%" // or "auto" so it grows fully
      : chartHeightAvailable,
  }}>
            <ChartContainer config={chartConfig || {}} style={{ width: "100%" }} >
              <ResponsiveContainer height={isFullscreen ? 550 : dynamicHeight} width="100%" >
                <BarChart
                  data={chartData}
                  layout="vertical"
                  margin={{ left: isFullscreen ? 100 : 0, right: 40, top: 10, bottom: 20 }}
                  barSize={isFullscreen ? 30 : 20}
                  barGap={isFullscreen ? 40 :30 }
                  barCategoryGap={isFullscreen ? "20%" : "10%"}
                  //height={chartHeight}
                >
                  {isCartesian &&(
                    <CartesianGrid strokeDasharray="2 2" stroke="#555" strokeWidth={0.5} horizontal={isHorizontal} vertical={!isHorizontal} />
                  )}
                  <XAxis 
                    className="text-small-font"
                    dataKey={isHorizontal ? xAxis?.dataKey : undefined}
                    type={isHorizontal ? 'category' : 'number'}
                    tickLine={false} 
                    axisLine={true}
                    tickFormatter={isHorizontal 
                      ? undefined 
                      : (value: string | number) => {
                          if (typeof value === 'number') {
                            if (xAxis?.isPercentage) {
                              return `${value}%`;
                            }
                            return formatNumber(value);
                          }
                          return value;
                        }
                    }
                    interval={0}
                    angle={isHorizontal ? -45 : 0}
                    textAnchor={isHorizontal ? 'end' : 'start'}
                    height={isHorizontal ? 80 : 30}
                    style={{fontSize: isFullscreen ? '16px' : '10px'}}
                    tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen}  dy={10} fullscreenDy={fullscreenDy}   yAxisXOffset= "-40" axisType="x" truncateLength={truncateLength} />}
                  >
                    {isHorizontal && <Label style={{fontSize:'10px'}} value={xAxis?.title} offset={-20} position="insideBottom" />}
                  </XAxis>
                  {yAxis && (
                    <YAxis
                      className="text-body"
                      dataKey={isHorizontal ? undefined : yAxis.dataKey}
                      type={isHorizontal ? 'number' : 'category'}
                      tickLine={false}
                      axisLine={true}
                      tickFormatter={isHorizontal 
                        ? (value: number) => `${(value * 1).toFixed(0)}%`
                        : (value: string) => {
                            return value.length > 10 ? value.substring(0, 5) + "..." : formatNumber(Number(value));
                          }
                      }  
                      width={isHorizontal ? 50 : (isFullscreen ? 120 : 110)} 
                      tickMargin={isFullscreen ? 20 : 15}
                      interval={0}
                      height={isHorizontal ? 80 : 500}
                      textAnchor="start"
                      style={{fontSize: isFullscreen ? '16px' : '10px'}}
                      tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" textAnchor="start" truncateLength={truncateLength} yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />} 
                    >
                      {!isHorizontal && <Label style={{fontSize: isFullscreen ? '16px' : '12px'}} value={yAxis.title} angle={-90} dy={dy}  position="insideLeft"
                       offset={isFullscreen ? -70 : -20} />}
                    </YAxis>
                  )}
                  <ChartTooltip content={<ChartTooltipContent isPercentage={isPercentage} />} />
                  {isLegend && (
                    <ChartLegend
                      content={<ChartLegendContent isFullscreen={isFullscreen} />}
                    />
                  )}
                  {/* Chart Bars */}
                  {chartConfig &&
                    Object.keys(chartConfig).map((key) => (
                      <Bar key={key} dataKey={key} stackId="a" fill={chartConfig[key].color} />
                    ))}
                </BarChart>
              </ResponsiveContainer>
         
            </ChartContainer>
            </div>
              {isCustomLegendContent && (
            <div className={` bottom-0 z-10  pt-1 pb-2 sm:overflow-visible md:overflow-visible ${isFullscreen ? 'pt-4' : 'pt-0'}`}>
            <CustomLegendContent labels={labels} colors={colors} />
          </div>
        )}
          </div>
        </div>
      )}
      {/* Legend Container Below or Side Based on layoutDirection */}
      {ischangeLegend && (
        <div className="grid grid-cols-5 w-full p-0 "> {/* 5 columns with a gap */}
          {chartConfig &&
            Object.keys(chartConfig).map((key) => (
              <div key={key} className="flex items-center">
                <div
                  className={`mr-2 rounded-sm ${isFullscreen ? 'w-3 h-3' : 'w-2 h-2'}`}
                  style={{ backgroundColor: chartConfig[key].color }}
                ></div>
                <span className={isFullscreen ? 'text-base' : 'text-small-font'}>{chartConfig[key].label}</span>
              </div>
            ))}
        </div>
      )}
    </div>
 
  ):( <div className="flex items-center justify-center h-[250px] overflow-hidden">
    <span className="text-small-font">No Data Found.!</span>
  </div>)}
</CardContent>
     )}
    </Card>
  );
}
export default ChartBarStacked
