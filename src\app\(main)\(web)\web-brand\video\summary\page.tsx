'use client';
import React, { useMemo } from 'react';
import { Card } from '@/components/ui/card';
import ChartBarStacked from '@/components/mf/charts/stackedBarChart';
import { onExpand, downloadURI, debounce, handleExportData,downloadFileFromUrl } from "@/lib/utils";
import { useCallback,useRef ,useEffect,useState} from 'react';
import domToImage from "dom-to-image";
import SummaryCardGroup from '../commonmodule/page';
import DynamicBarChart from '@/components/mf/charts/DynamicBarChart';
import { Filter } from "@/components/mf/Filters";
import {  useFraudCategoryWiseTraffic, useDeviceTypeWiseTraffic, useSourceWiseTraffic } from './apicallPage';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { useWastageFilters } from '../../Filters/useFilters';
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';
import {
  useFilterChangeHandler,
  FilterState,
} from  '../../Filters/buildFilters';



const Summary = () => {
      const cardRefs = useRef<Record<string, HTMLElement | null>>({});
      const [expandedCard, setExpandedCard] = useState<string | null>(null);
        const { selectedPackage } = usePackage();
        const { startDate, endDate } = useDateRange();
       const [exportType, setExportType] = useState<string | null>(null);
        const [isExporting, setIsExporting] = useState(false);

const [query, setQuery] = useState({
  publisher: ["all"],
  campaign: ["all"],
  channel: ["all"],
  fraud_category:["all"],
  fraud_sub_category:["all"],
  creative_id:["all"],
  sub_publisher:["all"],
  campaign_id:["all"],
});

const [loadedFilter, setLoadedFilter] = useState<FilterState>({});

const params =useMemo(() => {
return {
  package_name: selectedPackage,
  start_date: startDate,
  end_date: endDate,

}},[selectedPackage,startDate,endDate]);
const sourcepayloadfield = {
  ...params,
  ...query,
}
const devicepayloadfield = {
  ...params,
  ...query,
}
const fraudpayloadfield = {
  ...params,
  ...query,

}
        const isReady =
  !!selectedPackage && !!startDate && !!endDate;
       const {data:fraudData,isLoading:fraudLoading,error:fraudError} = useFraudCategoryWiseTraffic(fraudpayloadfield,isReady || exportType !== 'fraud');
      const { data: responseData, isLoading:Loading, error:errorDevice} = useDeviceTypeWiseTraffic(devicepayloadfield,isReady || exportType !== 'device');
      const { data: sourceData, isLoading:LoadingSource, error:errorSource } = useSourceWiseTraffic(sourcepayloadfield,isReady || exportType !== 'source');
      

            const onExport = useCallback(
        async (s: string, title: string, key: string) => {
          const ref = cardRefs.current[key];
          if (!ref) return;
    
          switch (s) {
            case "png":
              const screenshot = await domToImage.toPng(ref);
              downloadURI(screenshot, title + ".png");
              break;
            default:
          }
        },
        []
      );
      const handleExpand = (key: string) => {
        onExpand(key, cardRefs, expandedCard, setExpandedCard);
      };


  const sourcechartData = 
  Array.isArray(sourceData?.data)
  ? sourceData?.data.map((item:any) => ({
  label: item.label,
  "F-Cap Violation": item["F-Cap Violation"],
  IVT: item.IVT,
  "Brand Unsafe": item["Brand Unsafe"],
  Viewability: item.Viewability,
}))
  : [];

const sourcechartConfig = sourceData?.config ?? {};


  const devicechartData = Array.isArray(responseData?.data)
  ? responseData?.data.map((item:any) => ({
  label: item.label,
  desktop: item.desktop,
  mobile: item.mobile,
  ctv: item.ctv,
  others: item.others,
}))
  : [];

const devicechartConfig = responseData?.config ?? {};

const xAxisConfigS = {
    dataKey: "label",
  };

  const xAxisConfigD = {
    dataKey: "label",   
  };

  const xAxisConfigstack = {
    dataKey: "value",
  
  };

const FraudchartData = Array.isArray(fraudData?.data)
  ? fraudData.data.map((item: any) => ({
      label: item.label,
      Percentage: item.Percentage,
      Total: item.Total,
    }))
  : [];


const FraudchartConfig = fraudData?.config ?? {};

const filter = useWastageFilters(params, query);
const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
const handleExportClick = async (type: string) => {
  setExportType(type as any);
  setIsExporting(true);
};

useExportCsv({
  exportParams: params,
  queryParams: query,
  exportType,
  setExportType,
  isExporting,
  setIsExporting,
  endpointMap: {
    fraud: Endpoint.WebBrand.FRAUD_CATEGORY_WISE_TRAFFIC,
    device: Endpoint.WebBrand.DEVICE_TYPE_WISE_TRAFFIC,
    source: Endpoint.WebBrand.SOURCE_WISE_TRAFFIC,
  },
  baseUrlMap: {
    fraud: process.env.NEXT_PUBLIC_WEB_BRAND!,
    device: process.env.NEXT_PUBLIC_WEB_BRAND!,
    source: process.env.NEXT_PUBLIC_WEB_BRAND!,
  },
});

    return (
       <div className="p-2 w-full grid grid-col  gap-2">
         <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 sm:overflow-x-auto  scrollbar w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
              <Filter filter={filter} onChange={handleFilterChange} />
              </div>
       <SummaryCardGroup params={params} query={query} />
 
  <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-2 ">
    <Card  ref={(el) => (cardRefs.current["source_wise_split"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Source Wise Split"
         onExport={() => onExport("png", "Source Wise Split", "source_wise_split")}
         onExpand={() => handleExpand("source_wise_split")}
         handleExport={() => handleExportClick("source")}
         isexportcsv={true}
        chartConfig={sourcechartConfig}
        chartData={sourcechartData}
        isHorizontal={true}
        xAxis={xAxisConfigS}
        yAxis={xAxisConfigstack}
        height={330}
        isLoading={LoadingSource}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={10}
        truncateLength={15}
        barsize={50}
        graphheight={50}
        fullscreenbarsize={100}
        isLegend={false}
        isCustomLegendContent={true}
 
        />
    </Card>
    <Card  ref={(el) => (cardRefs.current["device_split"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Device*  Type Wise Split"
         onExport={() => onExport("png", "Device Type Wise Split", "device_split")}
         onExpand={() => handleExpand("device_split")}
         isexportcsv={true}
         handleExport={() => handleExportClick("device")}
        chartConfig={devicechartConfig}
        chartData={devicechartData}
        isHorizontal={true}
        xAxis={xAxisConfigD}
        yAxis={xAxisConfigstack}
        height={330}
        graphheight={40}
        isLoading={Loading}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={10}
        truncateLength={15}
        barsize={50}
        fullscreenbarsize={100}
        isLegend={false}
        isCustomLegendContent={true}
        // ischangeLegend={true}
        />
    </Card>
     <Card ref={(el) => (cardRefs.current["fraud_subfraud"] = el!)} className='p-2'> 
      <DynamicBarChart
      title="Fraud Category and Sub-Category Contribution"
      onExport={() => onExport("png", "Fraud Category and Sub-Category Contribution", "fraud_subfraud")}
      onExpand={() => handleExpand("fraud_subfraud")}
      isexportcsv={true}
      handleExport={() => handleExportClick("fraud")}
      data={FraudchartData}
      config={FraudchartConfig}
      isHorizontal={false}
      marginBottom={10}
      isLoading={fraudLoading}
      marginRight={20}
      yaxis1={{
        yAxisId: "right",
        orientation: "right",
        stroke: "hsl(var(--chart-1))",
      } 
      }
     
     // marginTop={40}
      barsize={30}
      height="280px"
      yAxisPercentage={true}
       rightAxisKeys={["Percentage"]}
       yaxisright={true}
      />
      
    </Card>
  </div>
  <div className='grid grid-cols-1 '> 
    <Card className='p-2 shadow-md text-xs'>
        * Device Classifications may overlap  - certain CTV devices can function as mobile devices and vice versa Please interpret device level data with caution.
    </Card>
  </div>
</div>

    );
};

export default Summary;
