'use client'
import React from 'react';
import DoubleL<PERSON><PERSON><PERSON> from '@/components/mf/charts/DoubleLineChart';
import { onExpand, downloadURI, debounce, handleExportData } from "@/lib/utils";
import domToImage from "dom-to-image";
import { useCallback, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import ResizableTable from '@/components/mf/ReportingToolTable';
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';
import {
    buildFilter,
    useFilterChangeHandler,
    FilterState,
} from "../../Filters/buildFilters";

import { useWastageFilters } from '../../Filters/useFilters'
import ChartBarStacked from '@/components/mf/charts/stackedBarChart';

const Geo_distribution = () => {
    const cardRefs = useRef<Record<string, HTMLElement | null>>({});
    const [expandedCard, setExpandedCard] = useState<string | null>(null);
    const [selectedFrequency, setSelectedFrequency] = useState("date");
    const { selectedPackage } = usePackage();
    const { startDate, endDate } = useDateRange();

    const params = {
        package_name: selectedPackage,
        start_date: startDate,
        end_date: endDate,
    };
    const [query, setQuery] = useState({
        publishers: ["all"],
        campaigns: ["all"],
        channels: ["all"],
        fraudcategory: ["all"],
        subfraudcategory: ["all"],
        creative_id: ["all"],
        publisher_ids: ["all"],
    });
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
    const onExport = useCallback(
        async (s: string, title: string, key: string) => {
            const ref = cardRefs.current[key];
            if (!ref) return;

            switch (s) {
                case "png":
                    const screenshot = await domToImage.toPng(ref);
                    downloadURI(screenshot, title + ".png");
                    break;
                default:
            }
        },
        []
    );
    const handleExpand = (key: string) => {
        onExpand(key, cardRefs, expandedCard, setExpandedCard);
    };

    const geochartData = [

        { label: "07-07-2025", "brand_safe": 186, "brand_unsafe": 80, "video_unavailable": 40 },
        { label: "08-07-2025", "brand_safe": 305, "brand_unsafe": 20, "video_unavailable": 70 },
        { label: "09-07-2025", "brand_safe": 237, "brand_unsafe": 14, "video_unavailable": 20 },
        { label: "10-07-2025", "brand_safe": 186, "brand_unsafe": 60, "video_unavailable": 80 },
        { label: "11-07-2025", "brand_safe": 305, "brand_unsafe": 25, "video_unavailable": 90 },
        { label: "12-07-2025", "brand_safe": 237, "brand_unsafe": 51, "video_unavailable": 30 },
        { label: "07-07-2025", "brand_safe": 186, "brand_unsafe": 80, "video_unavailable": 40 },
        { label: "08-07-2025", "brand_safe": 305, "brand_unsafe": 20, "video_unavailable": 70 },
        { label: "09-07-2025", "brand_safe": 237, "brand_unsafe": 14, "video_unavailable": 20 },
        { label: "10-07-2025", "brand_safe": 186, "brand_unsafe": 60, "video_unavailable": 80 },
        { label: "11-07-2025", "brand_safe": 305, "brand_unsafe": 25, "video_unavailable": 90 },
        { label: "12-07-2025", "brand_safe": 237, "brand_unsafe": 51, "video_unavailable": 30 },
    ]

    const geochartConfig = {
        "brand_safe": {
            label: "Brand Safe",
            color: "#00A86B",
        },
        "brand_unsafe": {
            label: "Brand Unsafe",
            color: "#8A0000",
        },
        "video_unavailable": {
            label: "Video Unavailable",
            color: "#093FB4",
        },
    }
    const selectOptions = ["Daily", "Weekly", "Monthly", "Yearly"];

    const [sercterm, setSearchTerm] = useState('');
    const UnsafeHeader = [
        { title: "Placement ID", key: "Placement ID" },
        //  { title: "Content Name", key: "Content Name" },
        { title: "Unsafe Category", key: "Unsafe Category" },
        { title: "Impressions(Descending Order)", key: "Impressions(Descending Order)" },
    ]
    const SafeHeader = [
        { title: "Placement ID", key: "Placement ID" },
        //  { title: "Content Name", key: "Content Name" },
        { title: "Safe Category", key: "Safe Category" },
        { title: "Impressions(Descending Order)", key: "Impressions(Descending Order)" },
    ]
    const BrandHeader = [
        { title: "Placement ID - Unsuitable", key: "Placement ID" },
        { title: "Category", key: "Category" },
        { title: "Impressions(Descending Order)", key: "Impressions(Descending Order)" },
    ]
  const CampaignHeader = [
        { title: "Campaign ID / Name", key: "Campaign ID / Name" },
        { title: "Unsafe Category", key: "Unsafe Category" },
        { title: "Placement Counts", key:"Placement Counts"},
        { title: "Impressions(Descending Order)", key: "Impressions(Descending Order)" },
    ]
    const UnsafereportData = [
        {
            "Placement ID": "Video1",
            // "Content Name": "Showname",
            "Unsafe Category": "Debatable",
            "Impressions(Descending Order)": "7,323",
        },
        {
            "Placement ID": "Video2",
            // "Content Name": "Showname",
            "Unsafe Category": "Terroroism",
            "Impressions(Descending Order)": "6,723",
        },
        {
            "Placement ID": "Video3",
            // "Content Name": "Showname",
            "Unsafe Category": "CategoryX",
            "Impressions(Descending Order)": "5,423",
        },
        {
            "Placement ID": "Video4",
            // "Content Name": "Showname",
            "Unsafe Category": "Hate Speech",
            "Impressions(Descending Order)": "3,423",
        },
        {
            "Placement ID": "Video4X",
            // "Content Name": "Showname",
            "Unsafe Category": "CategoryABC",
            "Impressions(Descending Order)": "423",
        },
    ]
    const safereportData = [
        {
            "Placement ID": "Video1",
            // "Content Name": "Showname",
            "Safe Category": " Brand Safe",
            "Impressions(Descending Order)": "2,323",
        },
        {
            "Placement ID": "Video2",
            // "Content Name": "Showname",
            "Safe Category": "Brand Safe",
            "Impressions(Descending Order)": "71,23",
        },
        {
            "Placement ID": "Video3",
            // "Content Name": "Showname",
            "Safe Category": "Brand Safe",
            "Impressions(Descending Order)": "6423",
        },
        {
            "Placement ID": "Video4",
            // "Content Name": "Showname",
            "Safe Category": "Brand Safe",
            "Impressions(Descending Order)": "3,423",
        },
        {
            "Placement ID": "Video4X",
            // "Content Name": "Showname",
            "Safe Category": "Brand Safe",
            "Impressions(Descending Order)": "423",
        },
    ]
    const BrandreportData = [
        {
            "Placement ID": "Video1",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "9,323",
        },
        {
            "Placement ID": "Video2",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "8,023",
        },
        {
            "Placement ID": "Video3",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "7,423",
        },
        {
            "Placement ID": "Video4",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "6,523",
        },
        {
            "Placement ID": "Video4X",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "5,423",
        },
        {
            "Placement ID": "Video3",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "4,423",
        },
        {
            "Placement ID": "Video4",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "3,423",
        },
        {
            "Placement ID": "Video4X",
            // "Content Name": "Showname",
            "Category": "Safe-Unsuitable",
            "Impressions(Descending Order)": "423",
        },
    ]
      const CampaignreportData = [
        {
            "Campaign ID / Name": "Video1",
            "Unsafe Category": "Debatable",
            "Placement Counts": "43,333",
            "Impressions(Descending Order)": "7,323",
        },
        {
            "Campaign ID / Name": "Video2",
            // "Content Name": "Showname",
            "Unsafe Category": "Terroroism",
            "Placement Counts": "53,333",
            "Impressions(Descending Order)": "6,723",
        },
        {
            "Campaign ID / Name": "Video3",
            // "Content Name": "Showname",
            "Unsafe Category": "CategoryX",
            "Placement Counts": "33",
            "Impressions(Descending Order)": "5,423",
        },
        {
            "Campaign ID / Name": "Video4",
            // "Content Name": "Showname",
            "Unsafe Category": "Hate Speech",
            "Placement Counts": "333",
            "Impressions(Descending Order)": "3,423",
        },
        {
            "Campaign ID / Name": "Video4X",
            // "Content Name": "Showname",
            "Unsafe Category": "CategoryABC",
            "Placement Counts": "4,333",
            "Impressions(Descending Order)": "423",
        },
    ]
    const FraudchartData = [
        { label: "Death injury or Millitary", Total: 400 },
        { label: "Debated Sensive Society", Total: 100 },
        { label: "Adult and explict Sexual Content", Total: 300 },
        { label: "Obensity & Profanity", Total: 200 },
        { label: "Online piracy", Total: 430 }
    ]
    const FraudchartConfig = {
        "Total": {
            "label": "Total",
            "color": "#0D5EA6"
        }
    }
    const xAxisConfigstack = {
        dataKey: "label",

    };
    const xAxisConfigS = {
        dataKey: "label",
    };
    const filter = useWastageFilters(params, query);

    const handleFilterChange = useFilterChangeHandler(
        loadedFilter,
        setQuery,
        setLoadedFilter
    );
    return (
        <div className=" w-full grid grid-col p-2 gap-2">
            <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                <Filter filter={filter} onChange={handleFilterChange} />
            </div>
            <div className="gap-1 w-full">
                <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Trend and Geo Distribution</div>
                <div className="grid grid-cols-3 w-full gap-2 ">
                    <Card ref={(el) => (cardRefs.current["brand_unsafe_placement"] = el!)} className='p-2'>
                        <DoubleLineChart
                            title="Brand Unsafe Placement"
                            onExport={() => onExport("png", "Brand Unsafe placement", "brand_unsafe_placement")}
                            onExpand={() => handleExpand("brand_unsafe_placement")}
                            chartConfig={geochartConfig}
                            chartData={geochartData}
                            // isSelect
                            // selectoptions={selectOptions}
                            // placeholder='Daily'
                            YAxis1={{
                                yAxisId: "right",
                                orientation: "right",
                                stroke: "hsl(var(--chart-3))",
                                title: "Branded Unsafe ",

                            }}
                            YLeftAxis={{
                                title: "Branded Safe"
                            }}
                            rightAxisKeys={["mobile"]}
                            selectedFrequency={selectedFrequency}
                            titley1_color="#b91c1c"
                            titley_color="#093FB4"
                            yAxisXOffset={-20}
                            yAxisXOffsetFullscreen={-35}
                            datalength={10}
                            leftmargin={30}
                            rightmargin={20}
                            yAxisPercentage={true}
                             isLegend={false}
                            PercentageLabel="mobile"
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["Brand_Unsafe_Impression"] = el!)} className='p-2'>
                        <DoubleLineChart
                            title="Brand Unsafe Impression"
                            onExport={() => onExport("png", "Brand Unsafe Impression", "Brand_Unsafe_Impression")}
                            onExpand={() => handleExpand("Brand_Unsafe_Impression")}
                            chartConfig={geochartConfig}
                            chartData={geochartData}
                            // isSelect
                            // selectoptions={selectOptions}
                            // placeholder='Daily'
                            YAxis1={{
                                yAxisId: "right",
                                orientation: "right",
                                stroke: "hsl(var(--chart-3))",
                                title: "Branded Unsafe ",

                            }}
                            YLeftAxis={{
                                title: "Branded Safe"
                            }}
                            rightAxisKeys={["mobile"]}
                            selectedFrequency={selectedFrequency}
                            titley1_color="#b91c1c"
                            titley_color="#093FB4"
                            yAxisXOffset={-20}
                            yAxisXOffsetFullscreen={-35}
                            datalength={10}
                            leftmargin={30}
                            rightmargin={20}
                            yAxisPercentage={true}
                            PercentageLabel="mobile"
                            isLegend={false}
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["brand_unsafe_categories"] = el!)} className='p-2'>

                        <ChartBarStacked
                            title="Brand Unsafe Categories"
                            onExport={() => onExport("png", "Brand Unsafe Categories", "brand_unsafe_categories")}
                            onExpand={() => handleExpand("brand_unsafe_categories")}
                            chartData={FraudchartData}
                            chartConfig={FraudchartConfig}
                            isHorizontal={false}
                            xAxis={xAxisConfigS}
                            yAxis={xAxisConfigstack}
                            height={300}
                            // isLoading={LoadingSource}
                            marginBottom={10}
                            marginLeft={10}
                            marginRight={20}
                            marginTop={30}
                            barsize={50}
                            fullscreenbarsize={100}
                            isLegend={false}
                            truncateLength={12}
                            yAxisXOffset={-65}
                            yAxisXOffsetFullscreen={-100}
                        // ischangeLegend={true}

                        />

                    </Card>
                </div>
                </div>
                <div className="gap-1 w-full">
                    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Unsafe Placement ID</div>
                    <div className="grid grid-cols-1 w-full">
                        <ResizableTable
                            isPaginated={true}
                            columns={UnsafeHeader}
                            data={UnsafereportData}
                            isSearchable={true}
                            isUserTable={false}
                            isTableDownload={true}
                            height={410}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={sercterm}
                        />
                    </div>
                </div>
                <div className="gap-1 w-full">
                    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Safe Placement ID</div>
                    <div className="grid grid-cols-1 w-full">
                        <ResizableTable
                            isPaginated={true}
                            columns={SafeHeader}
                            data={safereportData}
                            isTableDownload={true}
                            isSearchable={true}
                            isUserTable={false}
                            height={410}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={sercterm}
                        />
                    </div>
                </div>
                <div className="gap-1 w-full">
                    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Suitability</div>
                    <div className="grid grid-cols-1 w-full">
                        <ResizableTable
                            isPaginated={true}
                            columns={BrandHeader}
                            data={BrandreportData}
                            isSearchable={true}
                            isTableDownload={true}
                            isUserTable={false}
                            height={410}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={sercterm}
                        />
                    </div>
                </div>
                <div className="gap-1 w-full">
                    <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Campaign Wise Unsafe Placements</div>
                    <div className="grid grid-cols-1 w-full">
                        <ResizableTable
                            isPaginated={true}
                            columns={CampaignHeader}
                            data={CampaignreportData}
                            isSearchable={true}
                            isTableDownload={true}
                            isUserTable={false}
                            height={410}
                            setSearchTerm={setSearchTerm}
                            SearchTerm={sercterm}
                        />
                    </div>
                </div>
            </div>

    )
}

export default Geo_distribution