"use client";

import ResizableTable, { Column } from "@/components/mf/ReportingToolTable";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState,useMemo,useEffect } from "react";
import { Button } from "@/components/ui/button";
import { usePackageMappingQuery,useProductListQuery,useDeletePackageMutation ,useCreatePackage,useEditPackageMutation} from '../queries/product-mapping'

import { z } from "zod";
import { useForm, useFieldArray } from "react-hook-form";
import ReusableDialog from "@/components/ui/dialogUserMangement"
import Endpoint from "../../../common/endpoint";
import ToastContent,{ToastType} from "@/components/mf/ToastContent";
import { useApiCall } from "../../queries/api_base";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTit<PERSON>,
  DialogFooter,
  DialogDescription,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import JSZip from "jszip";
import { format, subDays } from "date-fns";
import DeleteDialog from "@/components/ui/deletedialog";


// New: Embedded Label options
// const embeddedLabelOptions = [
//   { value: "labelA", label: "Label A" },
//   { value: "labelB", label: "Label B" },
//   { value: "labelC", label: "Label C" },
// ];

// Update schema for array of rows
const formSchemaAddPackage = z.object({
  rows: z.array(
    z.object({
      product: z.string().min(1, "Product is required"),
      package: z.string().min(1, "Package is required"),
      packageTitle: z.string().min(1, "Package Title is required"),
    })
  ).min(1, "At least one row is required"),
  embeddedMenus: z.array(
    z.object({
      embeddedLabel: z.string().optional().or(z.literal("")),
      url: z.string().optional().or(z.literal("")), // <-- changed from .url() to just .string()
    })
  ).optional(),
});
type FormValuesAddPackage = z.infer<typeof formSchemaAddPackage>;

interface UserFormModalsProps {
  onPackageAdded: (user: Omit<FormValuesAddPackage, "password">) => void;
}
 interface PackageCreater{
  message:string;
 }
 interface ProductPackage {
  Product: string;
  Package: string[];
}


const CampaignOverviewPage: React.FC = () => {
  
  //const [activeTab, setActiveTab] = useState<string>("Campaign Overview Logs");
  const [rowCount, setRowCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<string | null>(null);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [emailTo, setEmailTo] = useState("");
  const [addPackageOpen, setAddPackageOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<string>("");  
  const [dialogMode, setDialogMode] = useState<"create" | "edit" | "view">("create");
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<Record<string, string>>({})
    const [isSubmitting, setIsSubmitting] = useState(false);
   const [Debouncedpackage_name, setDebouncedpackage_name] = useState('');
   const [searchText, setSearchText] = useState('');
       const [page, setPage] = useState(1);
       const [limit, setLimit] = useState(10);

   const {
  data: packagelist,
  refetch: refetchPackageMapping,
  isLoading,
} = usePackageMappingQuery({
  package_name: Debouncedpackage_name,
  page,
  limit,
});
 useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedpackage_name(searchText);
    }, 500);

    return () => clearTimeout(handler);
  }, [searchText]);

      const productListQuery = useProductListQuery() ;
      const deletePackageQuery =  useDeletePackageMutation();
      const editPackageQuery = useEditPackageMutation();
      const createPackageQuery = useCreatePackage();

    const [toastData, setToastData] = useState<{
    type: ToastType;
    title: string;
    description?: string;
    variant?: "default" | "destructive" | null;
  } | null>(null);
  const [selectedReport, setSelectedReport] = useState<Record<
    string,
    string | number
  > | null>(null);

    const totalPages = packagelist?.total_pages || 0;
    const tableDatas = useMemo(() => {
      return (packagelist?.data ?? []).map((entry) => ({
        Product: entry?.ProductName?.[0] || "-",
        Package: entry?.PackageName || "-"
      }));
    }, [packagelist]);
  
  const formAddPackage = useForm<FormValuesAddPackage>({
    resolver: zodResolver(formSchemaAddPackage),
    defaultValues: {
      rows: [
        { product: "", package: "", packageTitle: "" },
      ],
      embeddedMenus: [{ embeddedLabel: "", url: "" }],
    },
  });
  const { control, handleSubmit, reset, formState } = formAddPackage;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "rows",
  });
  const { fields: embeddedMenuFields, append: appendEmbeddedMenu, remove: removeEmbeddedMenu } = useFieldArray({
    control,
    name: "embeddedMenus",
  });
  // State to hold embedded label options per row
  const [embeddedLabelOptions, setEmbeddedLabelOptions] = useState<{ [rowIdx: number]: { value: string; label: string }[] }>({});
  const [embeddedLabelLoading, setEmbeddedLabelLoading] = useState<{ [rowIdx: number]: boolean }>({});

  // Helper to fetch role_id for a product
  const fetchRoleIdForProduct = async (productName: string) => {
    // Use the same logic as useRoleListQuery but fetch directly
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.ROLE_LIST}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: typeof window !== "undefined" ? localStorage.getItem("IDToken") || "" : "",
        },
        body: JSON.stringify({ product_name: productName }),
      }
    );
    const data = await response.json();
    // Return the first role's _id (assuming one role per product)
    return Array.isArray(data) && data.length > 0 ? data[0]._id : null;
  };

  // Helper to fetch embedded label options for a role_id
  const fetchEmbeddedLabels = async (role_id: string, productName: string) => {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.EMBEDDED_MENU}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: typeof window !== "undefined" ? localStorage.getItem("IDToken") || "" : "",
        },
        body: JSON.stringify({ product_name: productName }),
      }
    );
    const data = await response.json();
    // Map the API response structure: [{Name, Route}] to {value, label}
    return Array.isArray(data)
      ? data.map((item: any) => ({ value: item.Name, label: item.Name }))
      : [];
  };

  // Handler for when a product is selected in a row
  const handleProductChange = async (rowIdx: number, productValue: string) => {
    // Set loading state for this row
    setEmbeddedLabelLoading((prev) => ({ ...prev, [rowIdx]: true }));
    setEmbeddedLabelOptions((prev) => ({ ...prev, [rowIdx]: [] }));
    // Fetch role_id for the selected product
    const role_id = await fetchRoleIdForProduct(productValue);
    if (role_id) {
      // Fetch embedded label options for this role_id
      const options = await fetchEmbeddedLabels(role_id, productValue);
      setEmbeddedLabelOptions((prev) => ({ ...prev, [rowIdx]: options }));
    } else {
      setEmbeddedLabelOptions((prev) => ({ ...prev, [rowIdx]: [] }));
    }
    setEmbeddedLabelLoading((prev) => ({ ...prev, [rowIdx]: false }));
  };

  // ... rest of your code continues unchanged

  // In onSubmitPackage, ensure only the required fields are sent (product, package, packageTitle, embeddedLabel, url)
  const onSubmitPackage = async (data: FormValuesAddPackage) => {
    setIsSubmitting(true);
    try {
      // Transform the form data to match the required API payload structure
      const { product, package: pkg, packageTitle } = data.rows[0];
      const embeddedMenus = data.embeddedMenus?.filter(menu => menu.embeddedLabel && menu.url) || [];
      
      const payload = {
        PackageName: pkg,
        PackageTitle: packageTitle,
        ProductName: [product],
        EmbeddedMenus: embeddedMenus.map(menu => ({
          MenuName: menu.embeddedLabel!,
          EmbeddedUrl: menu.url || ""
        }))
      };

      // Call the API with the transformed payload
      await createPackageQuery.mutateAsync(payload);
      await refetchPackageMapping();
      setPage(1);
      setToastData({
        type: "success",
        title: "Success",
        description: "Packages added successfully!",
        variant: "default",
      });
      reset({ 
        rows: [{ product: "", package: "", packageTitle: "" }], 
        embeddedMenus: [{ embeddedLabel: "", url: "" }] 
      });
      setAddPackageOpen(false);
      setDialogOpen(false);
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: error?.response?.data?.error?.details?.[0]?.msg || error.message,
        variant: "default",
      });
      reset({ 
        rows: [{ product: "", package: "", packageTitle: "" }], 
        embeddedMenus: [{ embeddedLabel: "", url: "" }] 
      });
    }
  };

  const handleAddPackage = () => {
    setAddPackageOpen(true);
    refetchPackageMapping();
    productListQuery.refetch();

  };


 const handleDelete = (item: Record<string, string | number>) => {
  // Assuming item.packageName or similar holds the package name  
  setRowToDelete( item.Package as string);
  setDeleteDialogOpen(true);
};

  
  const handleEdit = (item: Record<string, string | number>) => {
    setDialogMode("edit");
    setSelectedUser({
    product:String(item.Product ||""),
    package:String(item.Package ||""),
    })
    if (!productListQuery.data && !productListQuery.isFetching) {
    productListQuery.refetch();
  }
    setDialogOpen(true)
  }

  const handleView = (item: Record<string, string | number>) => {
  //  console.log("handle view",item);
    
    setDialogMode("view")
    setSelectedUser({
    product:String(item.Product ||""),
    package:String(item.Package ||""),
    })
    setDialogOpen(true)
  }
const confirmDelete = async () => {
 // console.log("delete:",rowToDelete);
  
  if (!rowToDelete) return;

   try {
      const result = await deletePackageQuery.mutateAsync(rowToDelete)
      setToastData({
        type: "success",
        title: "Success",
        description: result.message,
        variant: "default"
      })
      setDeleteDialogOpen(false)
      setRowToDelete(null)
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: error.response?.data?.message || error.message
      })
    }
  }

  const handleSend = (item: Record<string, string | number>) => {
    setSelectedReport(item);
    setEmailModalOpen(true);
  };


 const getDialogFields = () => {
    if (dialogMode === "edit") {
    //  console.log("edit values product",productListQuery.data);
      
 
      return [
        { name: "product", label: "Product", type: "select" ,options:productListQuery.data},
        { name: "package", label: "Package", type: "input" , disabled:true},
      ]
    } 
     else if (dialogMode === "view") {
      return [
       { name: "product", label: "Product", type: "input",  disabled: true},
        { name: "package", label: "Package", type: "input" , disabled: true},
      ]
    }
    return []
  }

  // Wrapper function for ReusableDialog onSubmit
  const handleDialogSubmit = (data: Record<string, string>) => {
    // This is just a placeholder since the dialog is not used for the main form
    console.log("Dialog submit:", data);
  };



  const adgrpcolumns: Column<ProductPackage>[] = [
    {
      title: "Product",
      key: "Product",
      render: (data: ProductPackage) => (
        <div className="text-left ">{data.Product}</div>
      ),
    },
    {
      title: "Package  Name",
      key: "Package",
      render: (data: ProductPackage) => (
        <div className="text-left">{data.Package}</div>
      ),
    },
  ];

  

  const handleRefetch = (params?: { startDate?: Date; endDate?: Date }) => {
    if (params?.startDate && params?.endDate) {
      // console.log(
      //   "Refetching data for range:",
      //   params.startDate,
      //   params.endDate
      // );
    }
  };
 



 
  return (
      <div className="relative">
         {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}
        <div className="p-3">
          <ResizableTable
            columns={adgrpcolumns ?? []}
            isgenerateTable={false}
            isUserTable={false}
            isUserPackage={true}
            isMappingPackage={false}
            data={tableDatas}
            isTableDownload={false}
            isLoading={isLoading}
             isDownload={false}
            headerColor="#DCDCDC"
            itemCount={setRowCount}
            isSearchable={true}
             setSearchTerm={setSearchText}
            SearchTerm={searchText}
            isRefetch={false}
            onRefetch={handleRefetch}
            handleAddPackage={handleAddPackage}
            isEdit={true}
            onEdit={handleEdit}
            isSend={false}
            isView={true}
             onLimitChange={(newLimit: number) => {
            //  console.log("New limit selected:", newLimit);
              setLimit(newLimit);
            }}
            onPageChangeP={(newPage: number) => {
              setPage(newPage);
            }}
            totalPages={totalPages} 
            onView={handleView}
            onSend={handleSend}
            isClone={false}
            isSelectable={false}
            isDelete={true}
            onDelete={handleDelete}
          />
        <ReusableDialog
           open={dialogOpen}
           setOpen={setDialogOpen}
           onSubmit={handleDialogSubmit}
           DialogTitles={
             dialogMode === "edit" ? "Edit Package" : dialogMode === "view" ? "Package Details" : "Add Package"
           }
           fields={getDialogFields()}
           defaultValues={selectedUser}
           dialogMode={dialogMode} // "add" | "edit" | "view"
           isSubmitting={isSubmitting}
           isViewMode={dialogMode === "view"} // ✅ pass isViewMode directly
            />
        </div>
           <DeleteDialog
                open={deleteDialogOpen}
                onOpenChange={setDeleteDialogOpen}
                onConfirm={confirmDelete}
                title="Delete Package"
                description={`Are you sure you want to delete "${rowToDelete}"?`}
              />
      


        {/* add user form */}
        <Dialog open={addPackageOpen} onOpenChange={setAddPackageOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader className="font-semibold"></DialogHeader>
            <Form {...formAddPackage}>
              <form onSubmit={handleSubmit(onSubmitPackage)} className="space-y-6">
                {/* Package Information Section */}
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg mb-4">Add Pacakge</h3>
                  {/* Table headers */}
                  <div className="flex mb-2 font-semibold">
                    <div className="w-1/3">Product</div>
                    <div className="w-1/3">Package</div>
                    <div className="w-1/3">Package Title</div>
                  </div>
                  {fields.map((field, idx) => (
                    <div className="flex items-center mb-2" key={field.id}>
                      {/* Product dropdown */}
                      <div className="w-1/3 mr-2">
                      <FormField
                          control={control}
                          name={`rows.${idx}.product`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Select
                                  onValueChange={async (value) => {
                                    field.onChange(value);
                                    await handleProductChange(idx, value);
                                  }}
                                value={field.value}
                              >
                                  <SelectTrigger className="h-9">
                                  <SelectValue placeholder="Select Product" />
                                </SelectTrigger>
                                <SelectContent>
                                 {productListQuery.data && productListQuery.data.length > 0 ? (
                                      productListQuery.data.map((product, index) => {
                                        const rawName = product.label ?? "";
                                        const value = rawName.trim() === "" ? `no-product-${index}` : rawName;
                                        return (
                                          <SelectItem key={index} value={value}>
                                            {rawName.trim() === "" ? "No Product" : rawName}
                                          </SelectItem>
                                        );
                                      })
                                    ) : (
                                      <div className="px-2 py-1 text-sm text-muted-foreground">
                                        No Products Found
                                      </div>
                                    )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                      {/* Package input */}
                      <div className="w-1/3 mr-2">
                      <FormField
                          control={control}
                          name={`rows.${idx}.package`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input
                                type="text"
                                placeholder="Enter package name"
                                className="h-9"
                                {...field}
                              />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* Package Title input */}
                      <div className="w-1/3 mr-2">
                      <FormField
                          control={control}
                          name={`rows.${idx}.packageTitle`}
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input
                                type="text"
                                placeholder="Enter package title"
                                className="h-9"
                                {...field}
                              />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                  </div>
                  ))}
                </div>

                {/* Embedded Menus Section */}
                <div className="space-y-2">
                  {/* <h3 className="font-semibold text-lg mb-4">Embedded Menus</h3> */}
                  {/* Table headers */}
                  <div className="flex mb-2 font-semibold">
                    <div className="w-1/2">Embedded Label</div>
                    <div className="w-1/2">URL/ Dashboard ID</div>
                    <div className="w-8"></div>
                  </div>
                  {embeddedMenuFields.map((field, idx) => (
                    <div className="flex items-center mb-2" key={field.id}>
                      {/* Embedded Label dropdown */}
                      <div className="w-1/2 mr-2">
                        <FormField
                          control={control}
                          name={`embeddedMenus.${idx}.embeddedLabel`}
                          render={({ field }) => (
                            <FormItem className="mb-0">
                              <FormControl>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                  disabled={embeddedLabelLoading[0] || !embeddedLabelOptions[0] || embeddedLabelOptions[0].length === 0}
                                >
                                  <SelectTrigger className="h-9">
                                    <SelectValue placeholder={embeddedLabelLoading[0] ? "Loading..." : "Select Label"} />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {embeddedLabelOptions[0] && embeddedLabelOptions[0].length > 0 ? (
                                      embeddedLabelOptions[0].map((option) => (
                                        <SelectItem key={option.value} value={option.value}>
                                          {option.label}
                                        </SelectItem>
                                      ))
                                    ) : (
                                      <div className="px-2 py-1 text-sm text-muted-foreground">
                                        {embeddedLabelLoading[0] ? "Loading..." : "No Labels Found"}
                                      </div>
                                    )}
                                  </SelectContent>
                                </Select>
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      {/* URL input */}
                      <div className="w-1/2 mr-2">
                        <FormField
                          control={control}
                          name={`embeddedMenus.${idx}.url`}
                          render={({ field }) => (
                            <FormItem className="mb-0">
                              <FormControl>
                                <Input
                                  type="text"
                                  placeholder="Enter URL/ Dashboard ID"
                                  className="h-9"
                                  {...field}
                                />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                      {/* Plus icon to add embedded menu row */}
                      <div className="w-8 flex items-center">
                        {idx === embeddedMenuFields.length - 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            className="p-2"
                            onClick={() => appendEmbeddedMenu({ embeddedLabel: "", url: "" })}
                          >
                            <span className="text-xl">+</span>
                          </Button>
                        )}
                        {embeddedMenuFields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            className="p-2"
                            onClick={() => removeEmbeddedMenu(idx)}
                          >
                            <span className="text-xl">-</span>
                          </Button>
                        )}
                      </div>
                  </div>
                  ))}
                </div>
                <DialogFooter className="align items-center">
                  <DialogClose asChild>
                    <Button type="button" variant="default" className="pr-2bg-primary text-white hover:bg-secondary">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type="submit" variant="default" className="pr-2bg-primary text-white hover:bg-secondary">
                    Submit
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
  );
};

export default CampaignOverviewPage;