import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MultiSelect } from "@/components/ui/multi-select";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import React from "react";

interface SelectOption {
  value: string;
  label: string;
}

interface EditUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: any;
  productList: SelectOption[];
  roleList: SelectOption[];
  packageList: SelectOption[];
  onProductChange: (value: string) => void;
  onSubmit: (data: any) => void;
  isSubmitting?: boolean;
}

export function EditUserDialog({
  open,
  onOpenChange,
  selectedUser,
  productList,
  roleList,
  packageList,
  onProductChange,
  onSubmit,
  isSubmitting = false
}: EditUserDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    product: "",
    role: "",
    packages: [] as string[]
  });

  // Prefill values when dialog opens
  useEffect(() => {
    if (open && selectedUser) {
     // console.log("🔵 Dialog opened with user:", selectedUser);
      // Set prefill values from selectedUser
      const initialFormData = {
        name: selectedUser.name || selectedUser.Name || "",
        email: selectedUser.email || selectedUser.Email || "",
        product: selectedUser.Products || "",
        role: selectedUser.Role || "", // This might be an alias, we'll map it to ID later
        packages: Array.isArray(selectedUser.Packages) 
          ? selectedUser.Packages 
          : typeof selectedUser.Packages === "string" 
            ? selectedUser.Packages.split(",").map((p: string) => p.trim())
            : []
      };
      setFormData(initialFormData);

      // If product exists, get roles and packages for that product
      if (selectedUser.Products) {
        // Don't call onProductChange here - it's already handled by parent
        //console.log("🔵 Initial product:", selectedUser.Products);
      }
    }
  }, [open, selectedUser]); // Remove onProductChange from deps

  // Create role options from API response only
  const roleOptions = React.useMemo(() => {
   // console.log("🔵 Role list from API:", roleList);
    return roleList;
  }, [roleList]);

  // Create package options from API response only
  const packageOptions = React.useMemo(() => {
   // console.log("🔵 Package list from API:", packageList);
    return packageList;
  }, [packageList]);

  // Handle product change - get new roles and packages
  const handleProductChange = (value: string) => {
   // console.log("🔵 Product changed to:", value);
    
    // Update form data in child component
    setFormData(prev => ({
      ...prev,
      product: value,
      role: "",
      packages: []
    }));
    
    // Call parent's onProductChange
    if (value) {
      console.log("🔵 Calling onProductChange with:", value);
      onProductChange(value);
    }
  };

  // Debug logging for API response
  useEffect(() => {
   
  }, [roleList, packageList]);

  // Debug logging for form data changes
  useEffect(() => {
   
  }, [formData]);

  // Debug logging for role and package lists
  useEffect(() => {
    // console.log("🟣 Role list updated:", roleList);
    // console.log("🟣 Package list updated:", packageList);
  }, [roleList, packageList]);

  // Map role alias to role ID when role list is loaded
  useEffect(() => {
    if (open && selectedUser && roleList.length > 0 && formData.role) {
      // Find the role ID that corresponds to the role alias
      const roleOption = roleList.find(role => role.label === formData.role);
      if (roleOption && roleOption.value !== formData.role) {
        // Only update if the current role is not already an ID
        setFormData(prev => ({
          ...prev,
          role: roleOption.value // Set the role ID instead of alias
        }));
      }
    }
  }, [roleList, open, selectedUser, formData.role]);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setFormData({
        name: "",
        email: "",
        product: "",
        role: "",
        packages: []
      });
    }
  }, [open]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        key={selectedUser?._id || "default"}
        className="sm:max-w-[1200px]"
      >
        <DialogHeader>
          <DialogTitle>Edit Product Mapping</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-5 gap-4">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter name"
                disabled
              />
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                value={formData.email}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, email: e.target.value }))
                }
                placeholder="Enter email"
                disabled
              />
            </div>

            {/* Product */}
            <div className="space-y-2">
              <Label htmlFor="product">Product</Label>
              <Select
                value={formData.product}
                onValueChange={handleProductChange}
                disabled={isSubmitting}
              >
                <SelectTrigger className="w-full">
                  <SelectValue>
                    {formData.product || "Select product"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {productList.map((product) => (
                    <SelectItem key={product.value} value={product.value}>
                      {product.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={formData.role}
                onValueChange={(value) => {
                  // console.log("🔵 Role selected:", value);
                  setFormData((prev) => ({ ...prev, role: value }));
                }}
                disabled={!formData.product || isSubmitting}
              >
                <SelectTrigger className="w-full">
                  <SelectValue>
                    {formData.role 
                      ? roleOptions.find(role => role.value === formData.role)?.label || formData.role
                      : "Select role"
                    }
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((role) => (
                    <SelectItem key={role.value} value={role.value}>
                      {role.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Packages */}
            <div className="space-y-2">
              <Label htmlFor="packages">Packages</Label>
              <MultiSelect
                options={packageOptions}
                value={formData.packages}
                onValueChange={(values) => {
                  // console.log("🔵 Packages selected:", values);
                  setFormData((prev) => ({ ...prev, packages: values }));
                }}
                placeholder="Select packages"
                disabled={!formData.product || isSubmitting}
                className="bg-white h-10 text-black"
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 pt-4 rounded-md">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}