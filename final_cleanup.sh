#!/bin/bash

# Final cleanup script for overall-summary page
# This script will remove all old filter code and fix DashboardFilters usage

FILE="src/app/(main)/(web)/web-analytics/Dashboard/overall-summary/page.tsx"
BACKUP_FILE="${FILE}.backup.$(date +%Y%m%d_%H%M%S)"

echo "Creating backup: $BACKUP_FILE"
cp "$FILE" "$BACKUP_FILE"

echo "Starting final cleanup..."

# Remove filter state variables (lines 320-331)
echo "Removing filter state variables..."
sed -i '320,331d' "$FILE"

# Remove filter API calls (lines 350-450 approximately)
echo "Removing filter API calls..."
# Find the start of the first API call
START_API=$(grep -n "// Publishers Filter API" "$FILE" | cut -d: -f1)
if [ ! -z "$START_API" ]; then
    # Find the end of the last API call (look for the closing of eventTypeFilterApi)
    END_API=$(grep -n "// evenyt type Filter API" "$FILE" | cut -d: -f1)
    if [ ! -z "$END_API" ]; then
        # Add some lines to include the entire API call
        END_API=$((END_API + 25))
        sed -i "${START_API},${END_API}d" "$FILE"
    fi
fi

# Remove the filter useMemo computation
echo "Removing filter useMemo computation..."
START_FILTER=$(grep -n "const filter = React.useMemo" "$FILE" | cut -d: -f1)
if [ ! -z "$START_FILTER" ]; then
    # Find the end by looking for the closing bracket and dependencies
    END_FILTER=$(grep -n "query.event_type," "$FILE" | tail -1 | cut -d: -f1)
    if [ ! -z "$END_FILTER" ]; then
        END_FILTER=$((END_FILTER + 5))
        sed -i "${START_FILTER},${END_FILTER}d" "$FILE"
    fi
fi

# Remove the old handleFilterChange function
echo "Removing old handleFilterChange function..."
START_HANDLE=$(grep -n "// Update the handleFilterChange function" "$FILE" | cut -d: -f1)
if [ ! -z "$START_HANDLE" ]; then
    # Find the end by looking for the closing of the function
    END_HANDLE=$(grep -n "}, \[loadedFilter\]" "$FILE" | cut -d: -f1)
    if [ ! -z "$END_HANDLE" ]; then
        END_HANDLE=$((END_HANDLE + 1))
        sed -i "${START_HANDLE},${END_HANDLE}d" "$FILE"
    fi
fi

# Fix the DashboardFilters usage
echo "Fixing DashboardFilters usage..."
sed -i 's/<DashboardFilters filter={filter} onChange={handleFilterChange} \/>/<DashboardFilters onFilterChange={handleFilterChange} \/>/g' "$FILE"

# Remove unused interfaces
echo "Removing unused filter interfaces..."
sed -i '/interface FilterItem/,/}/d' "$FILE"
sed -i '/interface FilterState/,/}/d' "$FILE"
sed -i '/interface FilterPayload/,/}/d' "$FILE"

echo "Final cleanup completed! Backup saved as: $BACKUP_FILE"
echo "Please review the changes and test the application." 