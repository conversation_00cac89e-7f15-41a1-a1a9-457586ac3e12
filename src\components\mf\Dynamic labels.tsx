// utils/chartUtils.tsx ✅
'use client';
export const DynamicLabel = ({
  x,
  y,
  value,
  isDarkMode,
  isPercentageLabel = false,
}: {
  x: number;
  y: number;
  value: number | string;
  isPercentageLabel?: boolean;
  isDarkMode?: boolean;
}) => {
  const parsedValue =
    typeof value === 'string' ? parseFloat(value.replace('%', '')) : value;

  const formattedValue = isPercentageLabel
    ? `${parsedValue}%`
    : typeof parsedValue === 'number' && !isNaN(parsedValue)
    ? Intl.NumberFormat('en', { notation: 'compact', maximumFractionDigits: 1 }).format(parsedValue)
    : value;

  return (
    <text
      x={x}
      y={y}
      dy="-10"
      textAnchor="middle"
      fontSize={10}
      style={{ fill: isDarkMode ? "#fff" : "#000" }}
    >
      {formattedValue}
    </text>
  );
};
