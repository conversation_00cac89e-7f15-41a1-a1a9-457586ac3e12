"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter, Settings, X, Plus, Minus, Loader2 } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";

import { useRouter, useSearchParams } from "next/navigation";
import { useApiCall } from "../../../queries/api_base";
import Endpoint from "../../../common/endpoint";
import { usePackage } from "@/components/mf/PackageContext";
import FilterModal from "@/components/report/filterModal";
import DeliveryOptionsModal from "@/components/report/deliveryoptionsModal";
import ThresholdModal from "@/components/report/thresholdModal";
import ConfirmationDialog from "@/components/report/confirmationDialog";
import ToastContent,{
  ToastType,
} from "@/components/mf/ToastContent";
import { useToast } from "@/hooks/use-toast";

interface EmailListModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface GroupedOption {
  label: string;
  items: { id: string; label: string }[];
}

const GenerateReportPage = () => {

   const [toastData, setToastData] = useState<{
      type: ToastType;
      title: string;
      description?: string;
      variant?: "default" | "destructive" | null;
    } | null>(null);
  const { selectedPackage } = usePackage();
 

  const [frequency, setFrequency] = useState<string>("last-day");
  const [fileType, setFileType] = useState<string>("csv");
  const [thresholdModalOpen, setThresholdModalOpen] = useState(false);
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [customEmails, setCustomEmails] = useState<string[]>([""]);
  const [columnOrder, setColumnOrder] = useState<
    Array<{ id: string; content: string; type: "dimension" | "metric" }>
  >([]);
  const [selectedColumns, setSelectedColumns] = useState<typeof columnOrder>(
    []
  );
  const [selectedDimensions, setSelectedDimensions] = useState<string[]>([]);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const [selectedItemForThreshold, setSelectedItemForThreshold] = useState<{
    id: string;
    label: string;
  } | null>(null);
  const [templateValue, setTemplateValue] = useState<string[]>([]);
  const [selectedItemForFilter, setSelectedItemForFilter] = useState<{
    id: string;
    label: string;
  } | null>(null);
  const [dimensionSearch, setDimensionSearch] = useState("");
  const [metricSearch, setMetricSearch] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get("id");
  const mode = searchParams.get("mode");
  const frequencyValue = searchParams.get("frequency");
  const [reportName, setReportName] = useState("");
  const [reportCategory, setReportCategory] = useState<string>("summary");
  const [category, setCategory] = useState<string>("");
  const [categoryOptions, setCategoryOptions] = useState<string[]>([]);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(false);
  const [categoryError, setCategoryError] = useState<string | null>(null);

  const [deliveryModalOpen, setDeliveryModalOpen] = useState(false);
  const [deliveryModalType, setDeliveryModalType] = useState<
    "schedule" | "download"
  >("schedule");
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [dimensions, setDimensions] = useState<GroupedOption[]>([]);
  const [metrics, setMetrics] = useState<GroupedOption[]>([]);
  const [openMetricPopover, setOpenMetricPopover] = useState(false);
  const [openDimensionPopover, setOpenDimensionPopover] = useState(false);
  const [filterData, setFilterData] = useState<any>();
  const [metricsThresholds, setMetricsThresholds] = useState<
    Array<{
      field: string;
      operator: string;
      value: string;
    }>
  >([]);
  const [deliveryData, setDeliveryData] = useState<any | null>(undefined);
  const [statusCheck, setStatusCheck] = useState("no");

  const [dimensionsFilters, setDimensionsFilters] = useState<
    Array<{
      field: string;
      value: string[];
    }>
  >([]);

  const [reportNameError, setReportNameError] = useState<string | null>(null);
  const [dimensionsError, setDimensionsError] = useState<string | null>(null);
  const [metricsError, setMetricsError] = useState<string | null>(null);

  const { toast } = useToast();

  // api  call template

  const { result: templateApi, loading: templateLoading } = useApiCall({
    url: process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.REPORT_TEMPLATE,
    method: "GET",
    onSuccess: (data) => {},
    onError: (error) => {
      console.error("Error fetching template:", error);
    },
  });

  // Manually trigger the call on component mount and set default template
  useEffect(() => {
    if (templateApi?.data) {
      const templatesArray = Array.isArray(templateApi.data)
        ? templateApi.data
        : [];
      const updatedTemplates = [...templatesArray, "Custom"];
      setTemplateValue(updatedTemplates);
      
      // Set default template if not in edit mode
      if (!editId && updatedTemplates.length > 0 && !selectedTemplate) {
        handleTemplateChange(updatedTemplates[0]);
      }
    }
  }, [templateApi?.data]);

  // api call for template fields

  const { result: templateFieldsMutation, loading: templateFieldsLoading } =
    useApiCall({
      url:
        process.env.NEXT_PUBLIC_USER_MANAGEMENT +
        Endpoint.REPORT_TEMPLATE_FIELDS,
      method: "POST",
      params: { template_name: selectedTemplate },
      onSuccess: (data: { dimensions?: any[]; metrics?: any[] }) => {
        setDimensions(data?.dimensions || []);
        setMetrics(data?.metrics || []);
      },
      onError: (error) => {
        console.error("Error fetching template:", error);
      },
    });

  // api call for custom template

  const {
    result: customTemplateFieldsMutation,
    loading: CustomTemplateFieldsLoading,
  } = useApiCall({
    url:
      process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.REPORT_CUSTOM_TEMPLATE,
    method: "POST",
    manual: true,
    onSuccess: (data: { dimensions?: any[]; metrics?: any[] }) => {
      setDimensions(data?.dimensions || []);
      setMetrics(data?.metrics || []);
    },
    onError: (error) => {
      console.error("Error fetching template:", error);
    },
  });

  // api call for filter
  const { result: filterApi, loading: filterLoading } = useApiCall({
    url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.REPORT_FILTERS}/${selectedItemForFilter?.id}`,

    method: "POST",
    manual: true,
    onSuccess: (data) => {
      setFilterData(data);
    },
    onError: (error) => {
      console.error("Error fetching report:", error);
    },
  });

  // api call for creating report
  const { result: createReportApi, loading: createReportLoading } = useApiCall({
    url: process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.REPORT_CREATE_API,
    method: "POST",
    manual: true,
    onSuccess: (data) => {
       setToastData({
        type: "success",
        title: "Success",
        description: "Report created successfully!",
        variant: "default",
      });
      if (data?.status === "Success") {
        router.push("/web-analytics/reportingtool/report");
      }
    },
    onError: (error) => {
      console.error("Error creating report:", error);
    },
  });

  // Update columnOrder when dimensions or metrics change
  useEffect(() => {
    const dimensionColumns = selectedDimensions.map((dim) => ({
      id: `dim-${dim}`,
      content: dim,
      type: "dimension" as const,
    }));

    const metricColumns = selectedMetrics.map((metric) => ({
      id: `metric-${metric}`,
      content: metric,
      type: "metric" as const,
    }));

    setColumnOrder([...dimensionColumns, ...metricColumns]);
  }, [selectedDimensions, selectedMetrics]);

  // api call for view report
  const { result: viewReportApi, loading: viewReportLoading } = useApiCall({
    url: process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.VIEW_REPORT_API,
    method: "POST",
    manual: true,
    onSuccess: (data) => {},
    onError: (error) => {
      console.error("Error fetching report:", error);
    },
  });

  useEffect(() => {
    if (editId) {
      if (
        viewReportApi &&
        typeof (viewReportApi as any).mutate === "function"
      ) {
        (viewReportApi as any).mutate({ id: editId });
      }
    }
  }, [editId]);

  // Replace the useEffect that sets the form data with this:
  useEffect(() => {
    if (viewReportApi?.data) {
      const reportData = viewReportApi.data.data;

      setReportName(reportData.report_name);
      setReportCategory(reportData.report_type);
      setFrequency(reportData.occurence);
      setFileType(reportData.reportFormats);
      setSelectedTemplate(reportData.template);

      // Set dimensions filters from the report data
      if (reportData.dimensions && reportData.dimensions.length > 0) {
        setDimensionsFilters(reportData.dimensions);

        // Extract dimension fields for selection
        const dimensionFields = reportData.dimensions.map((dim) => dim.field);
        setSelectedDimensions(dimensionFields);

        const dimension2 = reportData.dimensions.map((dim) => ({
          field: dim.field,
          value: dim.value,
        }));

        const transformed = dimension2.map((dimension) => ({
          label: "",
          items: [
            {
              id: dimension.field,
              label: dimension.field,
            },
          ],
        }));

        setDimensions(transformed);
      }

      if (reportData.metrics && reportData.metrics.length > 0) {
        // This is already the correct structure
        const metricData = reportData.metrics.map((metric) => ({
          field: metric.field,
          operator: metric.operator,
          value: metric.value,
        }));

        const metricsFields = reportData.metrics.map((met) => met.field);

        const convertToNestedStructure = (
          data,
          groupLabel = ""
        ) => {
          return [
            {
              label: groupLabel,
              items: data.map((item) => ({
                id: item,
                label: item,
              })),
            },
          ];
        };

        const result = convertToNestedStructure(metricsFields);
        setMetrics(result);

        setMetricsThresholds(metricData);

        const metricFields = metricData.map((m) => m.field);
        setSelectedMetrics(metricFields);
      }

      // Set delivery options
      if (reportData.deliveryOptions) {
        setDeliveryData(reportData.deliveryOptions);
      }
    }
  }, [viewReportApi?.data]);

  useEffect(() => {
    // If we have dimensions and metrics data and the template is set to Custom,
    // we need to load the custom template fields
    if (
      selectedTemplate === "Custom" &&
      ((selectedDimensions && selectedDimensions.length > 0) ||
        (selectedMetrics && selectedMetrics.length > 0))
    ) {
      const payload = {
        product: "WEB",
      };
      if (
        customTemplateFieldsMutation &&
        typeof (customTemplateFieldsMutation as any).mutate === "function"
      ) {
        (customTemplateFieldsMutation as any).mutate(payload);
      }
    }
  }, [selectedTemplate, selectedDimensions, selectedMetrics]);

  useEffect(() => {
    console.log("Debug - Current state:", {
      selectedTemplate,
      dimensionsLength: dimensions,
      selectedDimensionsLength: selectedDimensions.length,
      dimensionsFiltersLength: dimensionsFilters.length,
      editId,
      hasViewData: !!viewReportApi?.data,
    });
  }, [
    selectedTemplate,
    dimensions,
    selectedDimensions,
    dimensionsFilters,
    editId,
    viewReportApi?.data,
  ]);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(columnOrder || []);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setColumnOrder(items);
  };

  const handleColumnSelect = (value: string) => {
    const selectedColumn = columnOrder?.find((col) => col.id === value);
    if (selectedColumn && !selectedColumns?.find((col) => col.id === value)) {
      setSelectedColumns([...selectedColumns, selectedColumn]);
    }
  };

  const handleRemoveColumn = (id: string) => {
    setSelectedColumns(selectedColumns?.filter((col) => col.id !== id) || []);
  };

  const handleDimensionSelect = (value: string) => {
    setSelectedDimensions((prev) => {
      const currentSelected = prev || [];
      if (currentSelected.includes(value)) {
        return currentSelected.filter((dim) => dim !== value);
      }
      const newSelected = [...currentSelected, value];
      // Clear error if at least one dimension is selected
      if (newSelected.length > 0) {
        setDimensionsError(null);
      }
      return newSelected;
    });
  };

  const handleMetricSelect = (value: string) => {
    setSelectedMetrics((prev) => {
      const currentSelected = prev || [];
      if (currentSelected.includes(value)) {
        return currentSelected.filter((metric) => metric !== value);
      }
      const newSelected = [...currentSelected, value];
      // Clear error if at least one metric is selected
      if (newSelected.length > 0) {
        setMetricsError(null);
      }
      return newSelected;
    });
  };

  const handleFilterClick = (item: { id: string; label: string }) => {
    setSelectedItemForFilter(item);
    const payload = {
      package_name: selectedPackage,
    };
    if (typeof (filterApi as any).mutate === "function") {
      (filterApi as any).mutate(payload);
    }
    setFilterModalOpen(true);
  };

  const handleSaveThreshold = (thresholdData: {
    field: string;
    operator: string;
    value: string;
  }) => {
    // Check if this metric already has a threshold
    const existingIndex = metricsThresholds.findIndex(
      (metric) => metric.field === thresholdData.field
    );

    if (existingIndex !== -1) {
      // Update existing threshold
      const updatedThresholds = [...metricsThresholds];
      updatedThresholds[existingIndex] = thresholdData;
      setMetricsThresholds(updatedThresholds);
    } else {
      // Add new threshold
      setMetricsThresholds([...metricsThresholds, thresholdData]);
    }
  };

  const handleSettingsClick = (item: { id: string; label: string }) => {
    setSelectedItemForThreshold(item);
    setThresholdModalOpen(true);
  };

  const handleCustomEmailChange = (index: number, value: string) => {
    const newEmails = [...(customEmails || [""])];
    newEmails[index] = value;
    setCustomEmails(newEmails);
  };

  const handleAddCustomEmail = () => {
    setCustomEmails([...(customEmails || [""]), ""]);
  };

  const handleRemoveCustomEmail = (index: number) => {
    if ((customEmails || []).length > 1) {
      const newEmails = (customEmails || []).filter((_, i) => i !== index);
      setCustomEmails(newEmails);
    }
  };

  // Handle template selection with validation
  const handleTemplateChange = (value: string) => {
    const payload = {
      product: "WEB",
    };
    if (value === "Custom") {
      if (customTemplateFieldsMutation) {
        (customTemplateFieldsMutation as any).mutate(payload);
      }
    }

    if (value !== "Custom") {
      if (
        templateFieldsMutation &&
        typeof (templateFieldsMutation as any).mutate === "function"
      ) {
        (templateFieldsMutation as any).mutate();
      }
    }
    setSelectedTemplate(value);
    setSelectedDimensions([]);
    setSelectedMetrics([]);
    setDimensionsError(null);
    setMetricsError(null);
  };

  const handleDownloadClick = () => {
    if (!reportName.trim()) {
      setReportNameError("Report name is mandatory.");
      return;
    }

    if (selectedTemplate === "Custom") {
      let hasError = false;
      if (selectedDimensions.length === 0) {
        setDimensionsError("Please select at least one dimension");
        hasError = true;
      }
      // if (selectedMetrics.length === 0) {
      //   setMetricsError("Please select at least one metric");
      //   hasError = true;
      // }
      if (hasError) return;
    }

    // setConfirmationDialogOpen(true);
    setDeliveryModalType("download");
    setDeliveryModalOpen(true);
    setStatusCheck("yes");
  };

  const handleFilterSave = (dimensionData: {
    field: string;
    value: string[];
  }) => {
    setDimensionsFilters((prev) => {
      const existingIndex = prev.findIndex(
        (dim) => dim.field === dimensionData.field
      );

      if (existingIndex !== -1) {
        const updatedFilters = [...prev];
        updatedFilters[existingIndex] = dimensionData;
        return updatedFilters;
      } else {
        return [...prev, dimensionData];
      }
    });
  };

 
  const { result: editReportApi, loading: editReportLoading } = useApiCall({
    url: process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.REPORT_EDIT_API,
    method: "PUT",
    manual: true,
    onSuccess: (data) => {
      // toast({
      //   title: "Success",
      //   description: "Report updated successfully!",
      //   duration: 3000,
      // });
 setToastData({
        type: "success",
        title: "Success",
        description: "Report updated successfully!",
        variant: "default",
      });

        router.push("/web-analytics/reportingtool/report");
    },
    onError: (error) => {
      console.error("Error editing report:", error);
    },
  });

  // Fixed handleModalSubmit function
  const handleModalSubmit = (data: any) => {
    setDeliveryData(data);

    // Create dimensions payload
    let dimensionsForPayload: Array<{ field: string; value: string[] }> = [];

   

    if (selectedTemplate === "Custom") {
      dimensionsForPayload = selectedDimensions.map((dimensionId) => {
        const existingFilter = dimensionsFilters.find(
          (filter) => filter.field === dimensionId
        );

        if (existingFilter) {
          return existingFilter;
        } else {
          return {
            field: dimensionId,
            value: [],
          };
        }
      });
    } else {
      // For non-custom templates, include all dimensions from the template
      const allDimensions: Array<{ id: string; label: string }> = [];

      dimensions.forEach((group) => {
        group.items.forEach((item) => {
          allDimensions.push(item);
        });
      });

      // Use existing filter value if available, otherwise default to empty array
      dimensionsForPayload = allDimensions.map((dimension) => {
        const existingFilter = dimensionsFilters.find(
          (filter) => filter.field === dimension.id
        );

        return {
          field: dimension.id,
          value: existingFilter ? existingFilter.value : [],
        };
      });
    }

    // Create metrics payload
    let metricsForPayload: Array<{
      field: string;
      operator: string;
      value: string;
    }> = [];

   

    // Create the base payload

    if (selectedTemplate === "Custom") {
      metricsForPayload = selectedMetrics.map((metricId) => {
        const existingThreshold = metricsThresholds.find(
          (threshold) => threshold.field === metricId
        );

        if (existingThreshold) {
          return existingThreshold;
        } else {
          return {
            field: metricId,
            operator: "",
            value: "",
          };
        }
      });
    } else {
      // For non-custom templates, include all metrics from the template
      const allMetrics: Array<{ id: string; label: string }> = [];

      metrics.forEach((group) => {
        group.items.forEach((item) => {
          allMetrics.push(item);
        });
      });

      // Use existing threshold if available
      metricsForPayload = allMetrics.map((metric) => {
        const existingThreshold = metricsThresholds.find(
          (threshold) => threshold.field === metric.id
        );

        return {
          field: metric.id,
          operator: existingThreshold ? existingThreshold.operator : "",
          value: existingThreshold ? existingThreshold.value : "",
        };
      });
    }

    const basePayload = {
      report_name: reportName,
      occurence: frequency,
      package_name: selectedPackage,
      dimensions: dimensionsForPayload,
      // metrics: metricsForPayload,
      reportFormats: fileType,
      report_type: reportCategory,
      deliveryOptions: data,
      download: deliveryModalType === "download" ? "yes" : "no",
      template: selectedTemplate,
      category: category,
    };

    // FIXED: Conditional API calling with proper mutate functions
    if (editId) {
      // If editing an existing report, call edit API only
      const updatePayload = {
        ...basePayload,
        id: editId, // Include the report ID for updating
      };

      // Use the mutate function directly if available, otherwise fall back to the old method
      if (
        editReportApi &&
        typeof (editReportApi as any).mutate === "function"
      ) {
        editReportApi.mutate(updatePayload);
      } else if (
        editReportApi &&
        typeof (editReportApi as any).mutate === "function"
      ) {
        (editReportApi as any).mutate(updatePayload);
      } else {
        console.error("Edit API mutate function not available");
      }
    } else {
      // If creating a new report, call create API only
      if (
        createReportApi &&
        typeof (createReportApi as any).mutate === "function"
      ) {
        createReportApi.mutate(basePayload);
      } else if (
        createReportApi &&
        typeof (createReportApi as any).mutate === "function"
      ) {
        (createReportApi as any).mutate(basePayload);
      } else {
        console.error("Create API mutate function not available");
      }
    }

    setDeliveryModalOpen(false);
  };

  // Fixed handleConfirmation function
  const handleConfirmation = (action: "cloud" | "email" | "download") => {
    setConfirmationDialogOpen(false);
    if (action === "cloud" || action === "email") {
      setDeliveryModalType("download");
      setDeliveryModalOpen(true);
    } else {
      if (deliveryData) {
        const basePayload = {
          report_name: reportName,
          occurence: frequency,
          package_name: selectedPackage,
          dimensions: dimensionsFilters,
          metrics: metricsThresholds,
          reportFormats: fileType,
          report_type: reportCategory,
          deliveryOptions: deliveryData,
          download: "yes",
          template: selectedTemplate,
          category: category,
        };

        // FIXED: Conditional API calling for download
        if (editId) {
          // If editing, call edit API
          const updatePayload = {
            ...basePayload,
            id: editId,
          };

          if (
            editReportApi &&
            typeof (editReportApi as any).mutate === "function"
          ) {
            editReportApi.mutate(updatePayload);
          } else if (
            editReportApi &&
            typeof (editReportApi as any).mutate === "function"
          ) {
            (editReportApi as any).mutate(updatePayload);
          } else {
            console.error("Edit API mutate function not available");
          }
        } else {
          // If creating new, call create API
          if (
            createReportApi &&
            typeof (createReportApi as any).mutate === "function"
          ) {
            createReportApi.mutate(basePayload);
          } else if (
            createReportApi &&
            typeof (createReportApi as any).mutate === "function"
          ) {
            (createReportApi as any).mutate(basePayload);
          } else {
            console.error("Create API mutate function not available");
          }
        }
      }
    }
  };

  const handleScheduleClick = () => {
    if (!reportName.trim()) {
      setReportNameError("Report name is mandatory.");
      return;
    }

    if (selectedTemplate === "Custom") {
      let hasError = false;
      if (selectedDimensions.length === 0) {
        setDimensionsError("Please select at least one dimension");
        hasError = true;
      }
      if (hasError) return;
    }

    setDeliveryModalType("schedule");
    setDeliveryModalOpen(true);
  };

  // Fetch categories for Category dropdown
  useEffect(() => {
    setCategoryLoading(true);
    setCategoryError(null);
    const token = typeof window !== "undefined" ? localStorage.getItem("IDToken") || "" : "";
    fetch(
      process.env.NEXT_PUBLIC_USER_MANAGEMENT + Endpoint.REPORT_CATEGORY,
      {
        method: "GET",
        headers: {
          Authorization: token,
        },
      }
    )
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch categories");
        return res.json();
      })
      .then((data) => {
        // Try to support both array and object response
        let categories: string[] = [];
        if (Array.isArray(data)) {
          categories = data.map((item) => item.label || item.name || item.category || item);
        } else if (data && Array.isArray(data.categories)) {
          categories = data.categories.map((item) => item.label || item.name || item.category || item);
        }
        setCategoryOptions(categories);
        if (categories.length > 0) setCategory(categories[0]);
        setCategoryLoading(false);
      })
      .catch((err) => {
        setCategoryError("Could not load categories");
        setCategoryLoading(false);
      });
  }, []);

  return (
    <>
      {(createReportLoading || viewReportLoading || editReportLoading ) ? (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="p-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      ) : (
        <div className="bg-white p-6 m-2 shadow-md rounded-md">
           {toastData && (
          <ToastContent
            type={toastData.type}
            title={toastData.title}
            description={toastData.description}
            variant={toastData.variant}
          />
        )}
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-xl font-bold">
              {mode === "view"
                ? "View Report"
                : mode === "edit"
                  ? "Edit Report"
                  : "Generate New Report"}
            </h1>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-4 gap-4">
              {/* Report Name */}
              <div className="space-y-2">
                <Label>Report Name</Label>
                <Input
                  placeholder="Enter Report Name"
                  value={reportName}
                  onChange={(e) => {
                    setReportName(e.target.value);
                    setReportNameError(null); // Clear error on change
                  }}
                  disabled={mode === "view"}
                />
                {reportNameError && ( // Display error message
                  <p className="text-sm text-red-500">{reportNameError}</p>
                )}
              </div>

              {/* Template */}
              <div className="space-y-2">
                <Label>Template</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={handleTemplateChange}
                  disabled={mode === "view"}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose Template" />
                  </SelectTrigger>

                  <SelectContent>
                    {templateLoading ? (
                      <div className="flex justify-center items-center p-2">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : (
                      <>
                        {templateValue?.map((template) => (
                          <SelectItem key={template} value={template}>
                            {template}
                          </SelectItem>
                        ))}
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Dropdown */}
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={category}
                  onValueChange={setCategory}
                  disabled={mode === "view" || categoryLoading || !!categoryError}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={categoryLoading ? "Loading..." : categoryError ? categoryError : "Select Category"} />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryLoading ? (
                      <div className="flex justify-center items-center p-2">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    ) : categoryError ? (
                      <div className="px-2 py-1 text-sm text-red-500">{categoryError}</div>
                    ) : (
                      categoryOptions.map((cat) => (
                        <SelectItem key={cat} value={cat}>
                          {cat}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {selectedTemplate && selectedTemplate !== "none" && (
              <div className="grid grid-cols-2 gap-8">
                {/* Dimensions Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">Dimensions</h3>
                  </div>
                  {selectedTemplate === "Custom" && (
                    <Popover
                      open={openDimensionPopover}
                      onOpenChange={setOpenDimensionPopover}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="w-full justify-between"
                          disabled={mode === "view"}
                        >
                          Select Dimensions
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="ml-2 h-4 w-4 shrink-0 opacity-50"
                          >
                            <path d="m6 9 6 6 6-6" />
                          </svg>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <div className="p-2">
                          <Input
                            type="text"
                            placeholder="Search dimensions..."
                            className="mb-2"
                            value={dimensionSearch}
                            onChange={(e) => setDimensionSearch(e.target.value)}
                          />
                          <div className="max-h-[300px] overflow-y-auto">
                            {dimensions
                              .map((group) => ({
                                ...group,
                                items: group.items.filter((item) =>
                                  item.label
                                    .toLowerCase()
                                    .includes(dimensionSearch.toLowerCase())
                                ),
                              }))
                              .filter((group) => group.items.length > 0)
                              .map((group) => (
                                <div key={group.label} className="mb-4">
                                  <div className="mb-2 px-2 text-sm font-medium text-gray-700">
                                    {group.label}
                                  </div>
                                  {group.items.map((item) => (
                                    <div
                                      key={item.id}
                                      className="flex cursor-pointer items-center justify-between rounded-md px-2 py-1.5 hover:bg-gray-100"
                                    >
                                      <div className="flex items-center gap-2">
                                        {selectedTemplate === "Custom" ? (
                                          <>
                                            <Checkbox
                                              id={`dimension-${item.id}`}
                                              checked={(
                                                selectedDimensions || []
                                              ).includes(item.id)}
                                              onCheckedChange={() =>
                                                handleDimensionSelect(item.id)
                                              }
                                            />
                                            <Label
                                              htmlFor={`dimension-${item.id}`}
                                              className="cursor-pointer"
                                              onClick={() =>
                                                handleDimensionSelect(item.id)
                                              }
                                            >
                                              {item.label}
                                            </Label>
                                          </>
                                        ) : (
                                          <Label>{item.label}</Label>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ))}
                            {dimensions
                              .map((group) => ({
                                ...group,
                                items: group.items.filter((item) =>
                                  item.label
                                    .toLowerCase()
                                    .includes(dimensionSearch.toLowerCase())
                                ),
                              }))
                              .filter((group) => group.items.length > 0)
                              .length === 0 && (
                              <>
                                {CustomTemplateFieldsLoading ? (
                                  <div className="flex items-center justify-center p-6">
                                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                  </div>
                                ) : (
                                  <div className="p-2 text-sm text-gray-500 text-center">
                                    No dimensions found
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}

                  {dimensionsError && (
                    <p className="text-sm text-red-500">{dimensionsError}</p>
                  )}

                  {/* Display selected dimensions */}
                  {selectedTemplate === "Custom" &&
                  (selectedDimensions || []).length === 0 ? (
                    <div className="mt-2 flex h-20 items-center justify-center rounded-md border border-dashed border-gray-300">
                      <p className="text-sm text-gray-500">
                        Select dimensions to view them here
                      </p>
                    </div>
                  ) : (
                    <>
                      {templateFieldsLoading ? (
                        <p className="text-xs text-gray-500">
                          Loading Dimensions...
                        </p>
                      ) : (
                        <div className="mt-2 max-h-[200px] overflow-y-auto rounded-md border border-gray-300 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                          {dimensions.map((group) => {
                            const groupItems =
                              selectedTemplate === "Custom"
                                ? group.items.filter((item) =>
                                    (selectedDimensions || []).includes(item.id)
                                  )
                                : group.items;

                            if (groupItems.length === 0) return null;

                            return (
                              <div key={group.label} className="space-y-2 p-2">
                                <div className="sticky top-0 bg-white text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">
                                  {group.label}
                                </div>

                                {groupItems.map((item) => (
                                  <div
                                    key={item.id}
                                    className="flex items-center justify-between"
                                  >
                                    <div
                                      className={`flex items-center gap-2 ${
                                        selectedTemplate !== "Custom"
                                          ? "justify-between w-full"
                                          : ""
                                      }`}
                                    >
                                      {selectedTemplate === "Custom" ? (
                                        <>
                                          <Checkbox
                                            id={item.id}
                                            checked={true}
                                            onClick={() =>
                                              handleDimensionSelect(item.id)
                                            }
                                          />
                                          <Label htmlFor={item.id}>
                                            {item.label}
                                          </Label>
                                        </>
                                      ) : (
                                        <>
                                          <Label htmlFor={item.id}>
                                            {item.label}
                                          </Label>
                                          <Filter
                                            className="h-4 w-4 cursor-pointer text-primary"
                                            onClick={() =>
                                              handleFilterClick(item)
                                            }
                                          />
                                        </>
                                      )}
                                    </div>

                                    {selectedTemplate === "Custom" && (
                                      <Filter
                                        className="h-4 w-4 cursor-pointer text-primary"
                                        onClick={() => handleFilterClick(item)}
                                      />
                                    )}
                                  </div>
                                ))}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Metrics Section - Commented out as not required
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">Metrics</h3>
                  </div>

                  {selectedTemplate === "Custom" && (
                    <Popover
                      open={openMetricPopover}
                      onOpenChange={setOpenMetricPopover}
                    >
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="w-full justify-between"
                          disabled={mode === "view"}
                        >
                          Select Metrics
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="ml-2 h-4 w-4 shrink-0 opacity-50"
                          >
                            <path d="m6 9 6 6 6-6" />
                          </svg>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <div className="p-2">
                          <Input
                            type="text"
                            placeholder="Search metrics..."
                            className="mb-2"
                            value={metricSearch}
                            onChange={(e) => setMetricSearch(e.target.value)}
                          />
                          <div className="max-h-[300px] overflow-y-auto">
                            {metrics
                              .map((group) => ({
                                ...group,
                                items: group.items.filter((item) =>
                                  item.label
                                    .toLowerCase()
                                    .includes(metricSearch.toLowerCase())
                                ),
                              }))
                              .filter((group) => group.items.length > 0)
                              .map((group) => (
                                <div key={group.label} className="mb-4">
                                  <div className="mb-2 px-2 text-sm font-medium text-gray-700">
                                    {group.label}
                                  </div>
                                  {group.items.map((item) => (
                                    <div
                                      key={item.id}
                                      className="flex cursor-pointer items-center justify-between rounded-md px-2 py-1.5 hover:bg-gray-100"
                                    >
                                      <div className="flex items-center gap-2">
                                        {selectedTemplate === "Custom" ? (
                                          <>
                                            <Checkbox
                                              id={`metric-${item.id}`}
                                              checked={(
                                                selectedMetrics || []
                                              ).includes(item.id)}
                                              onCheckedChange={() =>
                                                handleMetricSelect(item.id)
                                              }
                                            />
                                            <Label
                                              htmlFor={`metric-${item.id}`}
                                              className="cursor-pointer"
                                              onClick={() =>
                                                handleMetricSelect(item.id)
                                              }
                                            >
                                              {item.label}
                                            </Label>
                                          </>
                                        ) : (
                                          <>
                                            <Label>{item.label}</Label>
                                          </>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ))}
                            {metrics
                              .map((group) => ({
                                ...group,
                                items: group.items.filter((item) =>
                                  item.label
                                    .toLowerCase()
                                    .includes(metricSearch.toLowerCase())
                                ),
                              }))
                              .filter((group) => group.items.length > 0)
                              .length === 0 && (
                              <>
                                {CustomTemplateFieldsLoading ? (
                                  <div className="flex items-center justify-center p-6">
                                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                  </div>
                                ) : (
                                  <div className="p-2 text-sm text-gray-500 text-center">
                                    No metrics found
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  )}

                  {metricsError && (
                    <p className="text-sm text-red-500">{metricsError}</p>
                  )}

                  {selectedTemplate === "Custom" &&
                  (selectedMetrics || []).length === 0 ? (
                    <div className="mt-2 flex h-20 items-center justify-center rounded-md border border-dashed border-gray-300">
                      <p className="text-sm text-gray-500">
                        Select metrics to view them here
                      </p>
                    </div>
                  ) : (
                    <>
                      {templateFieldsLoading ? (
                        <p className="text-xs text-gray-500">
                          Loading Metrics...
                        </p>
                      ) : (
                        <div className="mt-2 max-h-[200px] overflow-y-auto rounded-md border border-gray-300 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                          {metrics.map((group) => {
                            const groupItems =
                              selectedTemplate === "Custom"
                                ? group.items.filter((item) =>
                                    (selectedMetrics || []).includes(item.id)
                                  )
                                : group.items;

                            if (groupItems?.length === 0) return null;

                            return (
                              <div key={group.label} className="space-y-2 p-2">
                                <div className="sticky top-0 bg-white text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">
                                  {group.label}
                                </div>
                                {groupItems.map((item) => (
                                  <div
                                    key={item.id}
                                    className="flex items-center justify-between"
                                  >
                                    <div
                                      className={`flex items-center gap-2 ${
                                        selectedTemplate !== "Custom"
                                          ? "justify-between w-full"
                                          : ""
                                      }`}
                                    >
                                      {selectedTemplate === "Custom" ? (
                                        <>
                                          <Checkbox
                                            id={item.id}
                                            checked={true}
                                            onClick={() =>
                                              handleMetricSelect(item.id)
                                            }
                                          />
                                          <Label htmlFor={item.id}>
                                            {item.label}
                                          </Label>
                                        </>
                                      ) : (
                                        <>
                                          <Label>{item.label}</Label>
                                          <Settings
                                            className="h-4 w-4 cursor-pointer text-primary"
                                            onClick={() =>
                                              handleSettingsClick(item)
                                            }
                                          />
                                        </>
                                      )}
                                    </div>
                                    {selectedTemplate === "Custom" && (
                                      <Settings
                                        className="h-4 w-4 cursor-pointer text-primary"
                                        onClick={() =>
                                          handleSettingsClick(item)
                                        }
                                      />
                                    )}
                                  </div>
                                ))}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </>
                  )}
                </div>
                */}
              </div>
            )}

            {/* File Type and Report Category */}
            <div className="grid grid-cols-3 gap-4">
              {/* File Type */}
              <div className="space-y-2">
                <Label>File Type</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="csv"
                      name="fileType"
                      value="csv"
                      checked={fileType === "csv"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view"}
                    />
                    <Label htmlFor="csv">CSV</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="xls"
                      name="fileType"
                      value="xls"
                      checked={fileType === "xls"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view"}
                    />
                    <Label htmlFor="xls">XLS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="tsv"
                      name="fileType"
                      value="tsv"
                      checked={fileType === "tsv"}
                      onChange={(e) => setFileType(e.target.value)}
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view"}
                    />
                    <Label htmlFor="tsv">TSV</Label>
                  </div>
                </div>
              </div>

              {/* Report Category */}
              <div className="space-y-2">
                <Label>Report Category</Label>
                <div className="flex gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="summary"
                      name="reportCategory"
                      value="summary"
                      checked={reportCategory === "summary"}
                      onChange={(e) =>
                        setReportCategory(e.target.value as "summary")
                      }
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view"}
                    />
                    <Label htmlFor="summary">Summary</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="transactional"
                      name="reportCategory"
                      checked={reportCategory === "transactional"}
                      value="transactional"
                      onChange={(e) =>
                        setReportCategory(e.target.value as "transactional")
                      }
                      className="h-4 w-4 border-gray-300 accent-primary focus:ring-primary"
                      disabled={mode === "view"}
                    />
                    <Label htmlFor="transactional">Transactional</Label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              onClick={() => {
                router.push("/web-analytics/reportingtool/report");
              }}
              className="text-white bg-primary hover:bg-primary"
            >
              Cancel
            </Button>
            <Button
              onClick={handleScheduleClick} // Updated to use the new function
              className="text-white bg-primary hover:bg-primary"
            >
              Schedule
            </Button>
            <Button
              onClick={handleDownloadClick} 
              className="text-white bg-primary hover:bg-primary"
            >
              Download
            </Button>
          </div>

          <ConfirmationDialog
            isOpen={confirmationDialogOpen}
            onClose={() => setConfirmationDialogOpen(false)}
            onConfirm={handleConfirmation}
          />

          <ThresholdModal
            isOpen={thresholdModalOpen}
            onClose={() => {
              setThresholdModalOpen(false);
              setSelectedItemForThreshold(null);
            }}
            selectedItem={selectedItemForThreshold}
            onSave={handleSaveThreshold}
            metricsthresholds={metricsThresholds}
            mode={mode}
          />

          <FilterModal
            isOpen={filterModalOpen}
            onClose={() => {
              setFilterModalOpen(false);
              setSelectedItemForFilter(null);
            }}
            selectedItem={selectedItemForFilter}
            onSave={handleFilterSave}
            filterData={filterData}
            filterloading={filterLoading}
            savedFilters={dimensionsFilters}
            mode={mode}
          />

          <DeliveryOptionsModal
            category={category}
            isOpen={deliveryModalOpen}
            onClose={() => {
              setDeliveryModalOpen(false);
              setDeliveryData(null);
            }}
            type={deliveryModalType}
            onSubmit={handleModalSubmit}
            defaultData={deliveryData}
            mode={mode}
            frequency={frequency}
            onFrequencyChange={setFrequency}
          />
        </div>
      )}
    </>
  );
};

export default GenerateReportPage;