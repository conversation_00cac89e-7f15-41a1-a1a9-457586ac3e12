// lib/apiClient
import axios, { AxiosError } from "axios";

// Handle session expiration and redirect
const handleUnauthorized = () => {
  const currentPath = window.location.pathname;
  if (currentPath !== '/') {
    localStorage.setItem('redirectPath', currentPath);
  }
  localStorage.clear();
  localStorage.clear();
  window.location.href = '/';
};

const apiClient = axios.create();

apiClient.interceptors.request.use((config) => {
  const token = typeof window !== "undefined" ? localStorage.getItem("IDToken") : "";
  if (token) {
    config.headers.Authorization =  token;
  }
  
  return config;
});

apiClient.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      handleUnauthorized();
    }
    return Promise.reject(error);
  }
);

export default apiClient;
