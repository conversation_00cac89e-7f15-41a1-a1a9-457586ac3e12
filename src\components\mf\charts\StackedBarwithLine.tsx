import * as React from "react";
import { useFullscreen } from "@/hooks/use-fullscreen";
import { useRef, useEffect, useState } from "react";
import { Bar, CartesianGrid, XAxis, YAxis, Line, Composed<PERSON><PERSON>, LabelList,ResponsiveContainer } from "recharts";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartLegend,ChartTooltip,ChartTooltipContent } from "@/components/ui/chart";
import { getXAxisAngle, formatNumber } from '@/lib/utils';
import { Loader2 } from "lucide-react";
import { CustomTick } from "@/components/ui/chart";

interface XAxisConfig {
  dataKey?: string;
  tickLine?: boolean;
  tickMargin?: number;
  axisLine?: boolean;
  tickFormatter?: (value: string) => string;
  angle?: number;
  textAnchor?: string;
  dy: number;
}
interface YAxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface YAxis2 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
}
interface TrafficTrendData {
  month?: string;
  Invalid?: number;
  Valid?: number;
  "Invalid %"?: number;
  
}


interface StackBarLineProps {
  chartData?: TrafficTrendData[];
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  keys?:string;
  handleExport?: () => void;
  onExpand: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  handleFrequencyChange?: (value: string) => void; 
  selectedType?: string;
  selectoptions?:string[];
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  placeholder?:string;
  isHorizontal?:boolean;
  xAxisConfig?: XAxisConfig;
  YAxis1?: YAxis1;
  YAxis2?: YAxis2;
  isLoading?:boolean;
  selectedFrequency?:string;
  isLegend?:boolean;
  truncateLength?: number;
  truncateLengthx?: number;
  dy?:number;
  fullscreenDy?:number;

}

const 
StackedBarWithLine: React.FC<StackBarLineProps> = ({
  chartData = [],
    chartConfig = {},
    keys="",
    handleTypeChange,
    visitEventOptions,
    selectedType,
    selectedFrequency,
    handleFrequencyChange,
    selectoptions=[],
    handleExport,
    truncateLength = 8,
    truncateLengthx = 8,
    dy=10,
    fullscreenDy=10,
    onExport,
    onExpand,
    isLoading,
    title ,
    isSelect= false,
    isRadioButton =false,
    placeholder="",
    isHorizontal=false,
    isLegend=true,
    xAxisConfig = {
    dataKey: "month", 
    tickLine: true,
    tickMargin: 10,
    axisLine: true,
    angle: 0,
    textAnchor: "middle",
    dy: 10,
    fullscreenDy: 10,

  },
  YAxis1 = {
    yAxisId: "left",
    orientation: "left",
    stroke: "hsl(var(--chart-1))",
    tickFormatter: (value: number) => formatNumber(value)
  },
  YAxis2 = {
    yAxisId: "right",
    orientation: "right",
    stroke: "hsl(var(--chart-3))",
    tickFormatter: (value: number) => `${value}%`,
  }, }) => {
       const isFullscreen = useFullscreen();

   // Calculate responsive chart dimensions based on fullscreen state
   const getChartDimensions = () => {
     if (isFullscreen) {
       return {
         height: 400,
         barSize: 80,
         barGap: 30,
         dotRadius: 3,
         strokeWidth: 3
       };
     }
     return {
       height: 160,
       barSize: 60,
       barGap: 20,
       dotRadius: 1,
       strokeWidth: 2
     };
   };

   const chartDimensions = getChartDimensions();

   const maxVisiblePoints = 7;
   const barWidth = 80; // Keep original barWidth logic

//console.log(chartConfig , "jkasdkanskdnas")
const chartWidth =
  chartData.length > maxVisiblePoints
    ? chartData.length * barWidth
    : undefined; 
  const labels = Object.values(chartConfig || {}).map(item => item.label);
  const colors = Object.values(chartConfig || {}).map(item => item.color);
  const months = chartData?.map(item => item.month).filter((month): month is string => month !== undefined) || [];
  const xAxisAngle = getXAxisAngle(months);
  const mergedXAxisConfig = { ...xAxisConfig };
  const mergedYAxis1Props = { ...YAxis1 };
  const mergedYAxis2Props = { ...YAxis2 };

  const CustomLegendContent = ({ labels, colors }: { labels: string[], colors: string[] }) => {
    return (
         <div className={`flex flex-wrap space-x-4 justify-start sm:justify-start md:justify-start lg:justify-center ${isFullscreen ? 'gap-6' : 'gap-4'}`}>
        {labels?.map((labelText: string, index) => (
          <div className="flex items-center space-x-2" key={index}>
               <span style={{ backgroundColor: colors[index] }} className={`rounded-full ${isFullscreen ? 'w-5 h-5' : 'w-4 h-4'}`}></span>
               <span className={`${isFullscreen ? 'text-base' : 'text-xs sm:text-xs md:text-xs'}`}>{labelText}</span>
          </div>
        ))}
      </div>
    );
  };
  return (
<Card className="border-none w-full p-0">
  {isLoading ? (
    <div className="flex items-center justify-center h-[260px]">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  ) : (
    <CardContent className={`sm:w-full p-0 ${isFullscreen ? 'h-[600px] overflow-hidden' : ''}`}>
      <ChartContainer config={chartConfig} className={`relative ${isFullscreen ? 'h-[550px]' : 'h-[210px]'} sm:w-full lg:w-full p-0`}>
        {/* Legend positioned outside the scrollable area */}
        
        {/* Scrollable chart area */}
        <React.Fragment>
          <div className={`${isFullscreen ? 'overflow-x-auto' : 'overflow-x-auto'} sm:overflow-x-auto md:overflow-x-auto`}>
          <div
            style={{
                height: isFullscreen ? '500px' : '160px', // Reduced height to account for legend
              ...(chartWidth ? { minWidth: `${chartWidth}px` } : { width: '100%' }),
            }}
            className="sm:overflow-x-auto md:overflow-x-auto"
          >
            <ResponsiveContainer width="100%" height="100%">
              {chartData.length > 0 ? (
                <ComposedChart
                  accessibilityLayer
                  data={chartData}
                  margin={{ top: 5, right: 2, left: 2, bottom: 20 }}
                    barGap={isFullscreen ? chartDimensions.barGap : 20}
                >
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                  <CartesianGrid vertical={false} />
                  <XAxis className="text-small-font" {...mergedXAxisConfig}
                    angle={xAxisAngle} 
                    interval={0}
                   tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} dy={dy} fullscreenDy={fullscreenDy}  axisType="x" truncateLength={truncateLengthx} textAnchor='end' xOffset={15} />} 
                  />
                  <YAxis className="text-small-font" {...mergedYAxis1Props}
                    tickFormatter={(value: number) => formatNumber(value)}
                  />
                  <YAxis className="text-small-font"
                    {...mergedYAxis2Props}
                    tickFormatter={(value) => `${value}%`}
                  />
                  
                  {chartData && chartConfig && Object.keys(chartConfig).map((key, index) => {
                    if (key !== keys) {
                      return (
                        <Bar
                          key={key}
                          dataKey={key}
                          stackId="a"
                          fill={chartConfig[key].color}
                          radius={index % 2 === 0 ? [0, 0, 4, 4] : [4, 4, 0, 0]}
                          yAxisId="left"
                            barSize={chartDimensions.barSize}
                        />
                      );
                    }
                    return (
                      <Line
                        key={keys}
                        type="monotone"
                        dataKey={keys}
                        stroke={chartConfig[keys].color}
                          strokeWidth={chartDimensions.strokeWidth}
                          dot={{ fill: chartConfig[key].color, r: chartDimensions.dotRadius }}
                        yAxisId="right"
                      />
                    );
                  })}
                </ComposedChart>
              ) : (
                <div className="flex items-center justify-center h-full w-full">
                  <span className="text-small-font font-medium">No Data Found!</span>
                </div>
              )}
            </ResponsiveContainer>
          </div>
        </div>
        {/* Only show legend if there is data and isLegend is true */}
        {isLegend && chartData.length > 0 && (
            <div className={` bottom-0 z-10 pb-2 sm:overflow-visible md:overflow-visible ${isFullscreen ? 'pt-4' : 'pt-0'}`}>
            <CustomLegendContent labels={labels} colors={colors} />
          </div>
        )}
        </React.Fragment>
      </ChartContainer>
    </CardContent>
  )}
</Card>
  );
};

export default StackedBarWithLine;