// hooks/useWastageFilters.ts
import { useMemo, useState } from "react";
import {
  usePublishersFilter,
  usePublisherIdFilter,
  useCampaignsFilter,
  useChannelsFilter,
  usefraudcategoryFilter,
  useSubfraudcategoryFilter,
  useCreativeIdFilter,
  useCampaignIdFilter,
} from '../FilterApi'; // Adjust based on your actual file paths

import { buildFilter } from './buildFilters'; // or wherever your logic is

export function useWastageFilters(params: any, query: any) {
  const isFilterReady = !!params.package_name && !!params.start_date && !!params.end_date;
  const publishersQuery = usePublishersFilter(params,isFilterReady);
  const publisherIdQuery = usePublisherIdFilter(params,isFilterReady);
  const campaignsQuery = useCampaignsFilter(params,isFilterReady);
  const channelsQuery = useChannelsFilter(params,isFilterReady);
  const fraudcategoryQuery = usefraudcategoryFilter(params,isFilterReady);
  const subfraudcategoryQuery = useSubfraudcategoryFilter(params,isFilterReady);
  const creativeIdQuery = useCreativeIdFilter(params,isFilterReady);
  const campaignIdQuery = useCampaignIdFilter(params,isFilterReady);
  

  const filters = useMemo(
    () => ({
      Publishers: buildFilter({
        data: publishersQuery.data,
        queryKey: "publishers",
        query,
        loading: publishersQuery.isLoading,
      }),
      Campaigns: buildFilter({
        data: campaignsQuery.data,
        queryKey: "campaigns",
        query,
        loading: campaignsQuery.isLoading,
      }),
      Channels: buildFilter({
        data: channelsQuery.data,
        queryKey: "channel",
        query,
        loading: channelsQuery.isLoading,
      }),
      "Publisher ID": buildFilter({
        data: publisherIdQuery.data,
        queryKey: "sub_publishers",
        query,
        loading: false,
      }),
      "Campaigns ID": buildFilter({
        data: campaignIdQuery.data,
        queryKey: "campaign_id",
        query,
        loading: false,
      }),
      "Creative ID": buildFilter({
        data: creativeIdQuery.data,
        queryKey: "creative_id",
        query,
        loading: false,
      }),
      "Wastage Category": buildFilter({
        data: fraudcategoryQuery.data,
        queryKey: "fraudcategory",
        query,
        loading: false,
      }),
      "Wastage Sub-Category": buildFilter({
        data: subfraudcategoryQuery.data,
        queryKey: "subfraudcategory",
        query,
        loading: false,
      }),
    }),
    [
      publishersQuery.data,
      publisherIdQuery.data,
      campaignsQuery.data,
      channelsQuery.data,
      fraudcategoryQuery.data,
      subfraudcategoryQuery.data,
      creativeIdQuery.data,
      campaignIdQuery.data,
      query.publisher,
      query.campaign,
      query.campaign_id,
      query.channels,
      query.fraudcategory,
      query.subfraudcategory,
      query.creative_id,
      query.publisher_ids,
    ]
  );

 
  return filters;
}
