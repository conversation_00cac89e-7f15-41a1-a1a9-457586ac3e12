"use client"
import React, { useEffect, useRef, useState } from "react"
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from "@/components/ui/table"
import {
  MdEdit,
  MdDelete,
  MdVisibility,
  MdFileDownload,
  MdArrowDropDown,
  MdSearch,
  MdArrowDownward,
  MdArrowUpward,
  MdPause,
  MdPlayArrow,
  MdClose,
  MdUnfoldMore,
} from "react-icons/md"
import { FiRefreshCw } from "react-icons/fi"
import { FaClone } from "react-icons/fa"
import { IoIosSend } from "react-icons/io"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import Pagination from "../ui/pagination"
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { But<PERSON> } from "../ui/button"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { CalendarIcon ,CircleX } from "lucide-react"
import JSZip from "jszip"
import { useRouter } from "next/navigation"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import EllipsisTooltip from "@/components/mf/EllipsisTooltip"

export type Column<T = void> =
  | { title: string; key: string }
  | { title: string; key: string; render: (data: T) => React.ReactNode }

export interface ActionButtonConfig {
  title: string
  action: () => void
  variant?: "default" | "secondary" | "destructive" | "outline" | "ghost" | "link"
  className?: string
  icon?: React.ReactNode
  disabled?: boolean
  size?: "default" | "sm" | "lg" | "icon"
}

interface ResizableTableProps<T> {
  buttonTextName?: string
  columns: Column<T>[]
  data: T[]
  headerColor?: string
  isEdit?: boolean
  isDelete?: boolean
  isClone?: boolean
  isUserTable?: boolean
  isUserPackage?: boolean
  isMappingPackage?: boolean
  isgenerateTable?: boolean
  isRefetch?: boolean
  isSend?: boolean
  isView?: boolean
  isTableDownload?: boolean
  isDownload?: boolean
  onRefetch?: (params?: { startDate?: Date; endDate?: Date }) => void
  isPaginated?: boolean
  isSearchable?: boolean
  isSelectable?: boolean
  isCount?: boolean
  isLoading?: boolean
  isFile?: boolean
  SearchTerm?: string
  setSearchTerm?: (term: string) => void
  actionButtons?: ActionButtonConfig[]
  actionButton?: React.ReactNode | React.ReactNode[]
  onEdit?: (item: T) => void
  onDownloadAll?: (item: T[]) => void
  handleAddUser?: () => void
  handleAddPackage?: () => void
  handleAddProductMapping?: () => void
  handleProductMapping?: () => void
  onDelete?: (item: T) => void
  onView?: (item: T) => void
  onDownload?: (item: T) => void
  onRefresh?: () => void
  onSelect?: (selectedItems: T[]) => void
  itemCount?: (count: number) => void
  isPause?: boolean
  isPlay?: boolean
  onPause?: (item: T) => void
  onPlay?: (item: T) => void
  onClone?: (item: T) => void
  onSend?: (item: T) => void
  onGenerateReport?: () => void
  height?: number
  emptyStateMessage?: string
  onPageChangeP?: (page: number) => void
  onLimitChange?: (limit: number) => void
  pageNo?: number
  totalPages?: number
  totalRecords?: number
  marginTop?: string;
  row_height?: number;
  row_count?: number; // Back to 10 rows

}


const ColumnToggleMenu: React.FC<{
  columns: Column<Record<string, string | number>>[]
  onToggle: (key: string) => void
  visibleColumns: Column<Record<string, string | number>>[]
}> = ({ columns, onToggle,
  visibleColumns }) => {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="flex items-center justify-between gap-2 h-9 px-3">
            <span>Columns</span>
            <div className="flex items-center">
              <span className="text-xs text-primary">
                {columns.length === visibleColumns.length ? "All" : visibleColumns.length}
              </span>
              <MdArrowDropDown className="ml-1" />
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0">
          <div className="max-h-[300px] overflow-auto">
            {columns.map((column) => (
              <div key={column.key} className="flex items-center px-4 py-2 hover:bg-muted">
                <Checkbox
                  checked={visibleColumns.some((col) => col.key === column.key)}
                  onCheckedChange={() => onToggle(column.key)}
                  id={`column-${column.key}`}
                />
                <Label htmlFor={`column-${column.key}`} className="ml-2 cursor-pointer flex-1">
                  {column.title}
                </Label>
              </div>
            ))}
          </div>
        </PopoverContent>
      </Popover>
    )
  }

const ResizableTable: React.FC<ResizableTableProps<Record<string, string | number>>> = ({
  buttonTextName = "New Report",
  columns,
  data,
  row_count = 10,
  headerColor = "#ccc",
  isEdit = false,
  isDelete = false,
  isClone = false,
  isUserTable = true,
  isUserPackage = false,
  isMappingPackage = false,
  isgenerateTable = false,
  isRefetch = false,
  onRefetch,
  isSend = false,
  isView = false,
  isPaginated = true,
  isDownload = false,
  isTableDownload = false,
  isSearchable = false,
  isSelectable = false,
  isCount = false,
  isLoading = false,
  actionButtons = [],
  actionButton,
  onEdit,
  onDelete,
  onView,
  onDownload,
  handleAddUser,
  handleAddPackage,
  handleAddProductMapping,
  handleProductMapping,
  SearchTerm = "",
  setSearchTerm,
  onSelect,
  onDownloadAll,
  onRefresh,
  itemCount,
  isPause = false,
  isPlay = false,
  onPause,
  onPlay,
  onClone,
  onSend,
  onGenerateReport,
  height,
  emptyStateMessage = "No Data Found!",
  onPageChangeP,
  onLimitChange,
  pageNo = 1,
  totalPages = 1,
  totalRecords = 0,
  marginTop = "10px",
  row_height = 25
}) => {

  // Constants for fixed height calculation
  const ROW_HEIGHT = row_height; // Decreased row height for more compact rows
  const HEADER_HEIGHT = 56; // Height of table header
  const PAGINATION_HEIGHT = 36; // Height of pagination controls
  const TABLE_CONTROLS_HEIGHT = 64; // Height of search/filter controls
  const FIXED_ROW_COUNT = row_count; // Back to 10 rows
  const [selectedItems, setSelectedItems] = useState<Record<string, string | number>[]>([])
  const [isMounted, setIsMounted] = useState(false)
  const [visibleColumns, setVisibleColumns] = useState<Column<Record<string, string | number>>[]>(columns)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [columnWidths, setColumnWidths] = useState<{ [key: string]: number }>({})
  const tableRef = useRef<HTMLTableElement>(null)
  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: "asc" | "desc"
  } | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [isRefetchModalOpen, setIsRefetchModalOpen] = useState(false)
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const router = useRouter()

  // Calculate total height needed for the table
  const tableHeight = HEADER_HEIGHT + (ROW_HEIGHT * FIXED_ROW_COUNT)
  const containerHeight = TABLE_CONTROLS_HEIGHT + tableHeight + (isPaginated ? PAGINATION_HEIGHT : 0)

  // Calculate maximum available height to prevent page scrollbars
  const maxAvailableHeight = typeof window !== 'undefined' ? window.innerHeight - 300 : 600 // 300px for other page elements
  const finalContainerHeight = Math.min(containerHeight, maxAvailableHeight)

  useEffect(() => {
    setIsMounted(true)
    const initialWidths: { [key: string]: number } = {}
    columns.forEach((col) => {
      initialWidths[col.key] = 150
    })
    setColumnWidths(initialWidths)
  }, [columns])

  useEffect(() => {
    if (pageNo && pageNo !== currentPage) {
      setCurrentPage(pageNo)
    }
  }, [pageNo])

  useEffect(() => {
    // console.log('itemsPerPage changed:', itemsPerPage)
  }, [itemsPerPage])

  useEffect(() => {
    // console.log('currentPage changed:', currentPage)
  }, [currentPage])

  const handleColumnToggle = (key: string) => {
    const newVisibleColumns = visibleColumns.some((col) => col.key === key)
      ? visibleColumns.filter((col) => col.key !== key)
      : [...visibleColumns, columns.find((col) => col.key === key)!]
    setVisibleColumns(newVisibleColumns)
  }

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc"
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc"
    }
    setSortConfig({ key, direction })
  }

  const sortedData = React.useMemo(() => {
    if (!Array.isArray(data)) {
      console.error("Data is not an array:", data)
      return []
    }

    const sortableData = [...data]
    if (sortConfig !== null) {
      sortableData.sort((a, b) => {
        const aValue = a[sortConfig.key]
        const bValue = b[sortConfig.key]

        if (aValue == null && bValue == null) return 0
        if (aValue == null) return sortConfig.direction === "asc" ? 1 : -1
        if (bValue == null) return sortConfig.direction === "asc" ? -1 : 1

        const aStr = String(aValue) // Remove .toLowerCase() for display
        const bStr = String(bValue) // Remove .toLowerCase() for display
        // Use lowercased values only for sorting logic
        const aSort = aStr.toLowerCase()
        const bSort = bStr.toLowerCase()

        if (aSort < bSort) {
          return sortConfig.direction === "asc" ? -1 : 1
        }
        if (aSort > bSort) {
          return sortConfig.direction === "asc" ? 1 : -1
        }
        return 0
      })
    }
    return sortableData
  }, [data, sortConfig])

  const filteredData = React.useMemo(() => {
    if (!SearchTerm.trim()) return sortedData

    return sortedData.filter((item) => {
      return visibleColumns.some((column) => {
        const cellValue = String(item[column.key] || "") // Remove .toLowerCase() for display
        return cellValue.toLowerCase().includes(SearchTerm.toLowerCase()) // Only use lower for search
      })
    })
  }, [sortedData, visibleColumns, SearchTerm])

  useEffect(() => {
    if (onPageChangeP) {
      onPageChangeP(currentPage)
    }
  }, [currentPage])

  useEffect(() => {
    if (onLimitChange) {
      onLimitChange(itemsPerPage)
    }
  }, [itemsPerPage])

  const handleCheckboxChange = (item: Record<string, string | number>) => {
    if (selectedItems.includes(item)) {
      const items = selectedItems.filter((i) => i !== item)
      setSelectedItems(items)
      if (onSelect) onSelect(items)
    } else {
      const items = [...selectedItems, item]
      setSelectedItems(items)
      if (onSelect) onSelect(items)
    }
  }

  const handleMouseDown = (e: React.MouseEvent, key: string) => {
    const startX = e.clientX
    const startWidth = columnWidths[key]
    const minWidth = 100

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const newWidth = Math.max(
        minWidth,
        startWidth + moveEvent.clientX - startX
      )
      setColumnWidths((prevWidths) => ({
        ...prevWidths,
        [key]: newWidth,
      }))
    }

    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }

    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)
  }

  useEffect(() => {
    if (typeof itemCount === "function") itemCount(selectedItems.length)
  }, [selectedItems.length])

  const handlePageChange = (newPage: number) => {
    const validPage = Math.max(1, Math.min(newPage, totalPages))
    setCurrentPage(validPage)
    if (onPageChangeP) {
      onPageChangeP(validPage)
    }
  }

  const handleRefetch = () => {
    if (startDate && endDate) {
      onRefetch?.({ startDate, endDate })
      setIsRefetchModalOpen(false)
    }
  }

  const downloadTableAsCSV = async () => {
    try {
      const zip = new JSZip()
      const response = await fetch("/dummy.csv")
      const csvData = await response.blob()
      zip.file("dummy.csv", csvData)
      const zipContent = await zip.generateAsync({ type: "blob" })
      const link = document.createElement("a")
      const currentDate = format(new Date(), "yyyyMMdd")
      const fileName = `web.mfilterit.cpv_${currentDate}.zip`
      link.href = URL.createObjectURL(zipContent)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    } catch (error) {
      console.error("Error downloading file:", error)
    }
  }

  const handleBack = () => {
    router.back()
  }

  if (!isMounted) return null

  const colSpan =
    visibleColumns.length +
    (isSelectable ? 1 : 0) +
    (isEdit || isDelete || isView || isDownload || isPause || isPlay || isRefetch || isSend || isClone ? 1 : 0)

  return (
    <div className={`w-full mt-$[marginTop] flex flex-col h-full overflow-hidden`}>
      {/* Table Controls - Top Bar */}
      <div className="flex flex-col md:flex-row w-full gap-2 rounded-lg border bg-card p-2 text-body flex-shrink-0">
        {/* Left Side Controls */}
        <div className="flex flex-1 flex-wrap md:flex-nowrap items-center gap-2">
          {/* Search Bar - Responsive */}
          {isSearchable && (
            <div
              className={cn(
                "flex items-center space-x-2 p-2 border rounded-md",
                isSearchExpanded ? "w-full" : "w-full md:flex-1",
              )}
            >
              {isSearchExpanded ? (
                <>
                  <MdSearch className="text-xl text-card-foreground" />
                  <input
                    type="text"
                    placeholder="Search"
                    value={SearchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-card text-card-foreground outline-none"
                    autoFocus
                  />
                  <button onClick={() => {
                    setIsSearchExpanded(false)
                    setSearchTerm("");
                  }
                  } className="md:hidden">

                    <MdClose className="text-xl" />
                  </button>
                </>
              ) : (
                <>
                  <MdSearch
                    className="text-xl text-card-foreground md:hidden"
                    onClick={() => {
                      setIsSearchExpanded(true)
                      setSearchTerm("");
                    }}
                  />
                  <span className="md:hidden">Search</span>
                  <div className="hidden md:flex w-full items-center">
                    <MdSearch className="text-xl text-card-foreground" />
                    <input
                      type="text"
                      placeholder="Search"
                      value={SearchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full bg-card text-card-foreground outline-none"
                    />
                    <Button title="Clear"
                      className="cursor-pointer px-0 py-0  text-xs w-7 h-7 text-white "
                      onClick={() => {
                        setSearchTerm("");

                        // if (onRefresh) onRefresh();
                      }}
                    >
                      {/* Clear */}
                      <CircleX size={30}/>
                    </Button>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Column Toggle */}
          <ColumnToggleMenu columns={columns} onToggle={handleColumnToggle} visibleColumns={visibleColumns} />

          {/* Selected Count */}
          {isCount && (
            <div
              title="Total Selected Rows"
              onClick={() => (typeof onDownloadAll === "function" ? onDownloadAll(data) : null)}
              className="rounded-lg bg-purple-100 p-2 text-primary text-center min-w-[40px] cursor-pointer"
            >
              <span>{selectedItems.length}</span>
            </div>
          )}

          {/* Download Button */}
          {isDownload && (
            <Button
              variant="outline"
              size="icon"
              onClick={downloadTableAsCSV}
              title="Download Table Data as CSV"
              className="h-9 w-9"
            >
              <MdFileDownload className="h-4 w-4" />
            </Button>
          )}

          {/* Table Download Button */}
          {isTableDownload && (
            <Button
              variant="outline"
              size="icon"
              onClick={onDownload}
              title="Download Table Data as CSV"
              className="h-9 w-9"
            >
              <MdFileDownload className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="flex md:justify-end gap-2 flex-wrap">
          <div className="hidden md:flex gap-2 flex-wrap justify-end">
            {isgenerateTable && (
              <Button
                variant="default"
                className="bg-primary text-white hover:bg-secondary h-9"
                onClick={() => onGenerateReport?.()}
              >
                {buttonTextName}
              </Button>
            )}

            {isUserTable && (
              <>
                <Button
                  variant="default"
                  className="bg-primary text-white hover:bg-secondary h-9"
                  onClick={handleAddUser}
                >
                  Create User
                </Button>
                <Button
                  variant="default"
                  className="bg-primary text-white hover:bg-secondary h-9"
                  onClick={handleProductMapping}
                >
                  Product Mapping
                </Button>
              </>
            )}

            {isUserPackage && (
              <Button
                variant="default"
                className="bg-primary text-white hover:bg-secondary h-9"
                onClick={() => handleAddPackage?.()}
              >
                Add Package
              </Button>
            )}

            {isMappingPackage && (
              <>
                <Button variant="default" className="bg-primary text-white hover:bg-secondary h-9" onClick={handleBack}>
                  Back
                </Button>
                <Button
                  variant="default"
                  className="bg-primary text-white hover:bg-secondary h-9"
                  onClick={() => handleAddProductMapping?.()}
                >
                  Add Product Mapping
                </Button>
              </>
            )}
            {actionButton}
          </div>

          {/* Action Buttons from Parent Configuration - Mobile */}
          <div className="md:hidden w-full">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="default" className="h-9 w-full">
                  Actions
                  <MdArrowDropDown className="ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                {actionButtons.map((buttonConfig, index) => (
                  <DropdownMenuItem
                    key={index}
                    onClick={buttonConfig.action}
                    className="cursor-pointer"
                    disabled={buttonConfig.disabled}
                  >
                    {buttonConfig.icon && <span className="mr-2">{buttonConfig.icon}</span>}
                    {buttonConfig.title}
                  </DropdownMenuItem>
                ))}

                {isgenerateTable && (
                  <DropdownMenuItem onClick={onGenerateReport} className="cursor-pointer">
                    {buttonTextName}
                  </DropdownMenuItem>
                )}
                {isUserTable && (
                  <>
                    <DropdownMenuItem onClick={handleAddUser} className="cursor-pointer">
                      Add User
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleProductMapping} className="cursor-pointer">
                      Product Mapping
                    </DropdownMenuItem>
                  </>
                )}
                {isUserPackage && (
                  <DropdownMenuItem onClick={handleAddPackage} className="cursor-pointer">
                    Add Package
                  </DropdownMenuItem>
                )}
                {isMappingPackage && (
                  <>
                    <DropdownMenuItem onClick={handleBack} className="cursor-pointer">
                      Back
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleAddProductMapping} className="cursor-pointer">
                      Add Product Mapping
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Table Container with fixed height */}
      <div
        className="relative flex flex-col border border-t-0 rounded-b-lg overflow-hidden bg-card"
        style={{
          width: '100%',
          maxWidth: '100%',
          height: height ? `${height}px` : '700px',
        }}
      >
        {/* Scrollable Table Body */}
        <div className="overflow-auto w-full bg-card" style={{
          height: height ? `${height}px` : '700px',
        }}>
          <Table ref={tableRef} className="min-w-full">
            <TableHeader className="sticky top-0 z-10" style={{ height: `${HEADER_HEIGHT}px` }}>
              <TableRow>
                {isSelectable && (
                  <TableHead
                    className="border-r"
                    style={{
                      width: "50px",
                      minWidth: "50px",
                      maxWidth: "50px",
                      backgroundColor: headerColor,
                    }}
                  >
                    <Checkbox
                      onCheckedChange={(checked) => {
                        const allItems = checked ? filteredData : []
                        setSelectedItems(allItems)
                        if (onSelect) {
                          onSelect(allItems)
                        }
                      }}
                    />
                  </TableHead>
                )}
                {visibleColumns.map((column) => (
                  <TableHead
                    key={column.key}
                    className="relative border-r"
                    style={{
                      backgroundColor: headerColor,
                      color: "black",
                      width: `${columnWidths[column.key]}px`,
                      whiteSpace: "nowrap",
                    }}
                  >
                    <div className="flex items-center justify-center px-2">
                      <div className="flex-1 overflow-hidden">
                        <span className="block truncate text-sm font-bold" title={column.title}>
                          {column.title}
                        </span>
                      </div>

                      <div className="flex items-center ml-2">
                        <button
                          onClick={() => handleSort(column.key)}
                          className="cursor-pointer p-1 hover:bg-gray-200 rounded"
                          title={`Sort by ${column.title}`}
                        >
                          {sortConfig?.key === column.key ? (
                            sortConfig.direction === "asc" ? (
                              <MdArrowUpward className="text-primary text-sm" />
                            ) : (
                              <MdArrowDownward className="text-primary text-sm" />
                            )
                          ) : (
                            <MdUnfoldMore className="text-gray-400 text-sm hover:text-gray-600" />
                          )}
                        </button>
                      </div>

                      <div
                        onMouseDown={(e) => handleMouseDown(e, column.key)}
                        className="absolute right-0 top-0 h-full w-2 cursor-col-resize hover:bg-gray-400"
                        style={{ backgroundColor: "transparent" }}
                      />
                    </div>
                  </TableHead>
                ))}

                {(isEdit ||
                  isDelete ||
                  isView ||
                  isDownload ||
                  isPause ||
                  isPlay ||
                  isRefetch ||
                  isSend ||
                  isClone) && (
                    <TableHead
                      className="border-r text-center"
                      style={{
                        backgroundColor: headerColor,
                        color: "black",
                        width: "100px",
                        minWidth: "100px",
                        whiteSpace: "nowrap",
                        fontWeight: "bold",
                      }}
                    >
                      Action
                    </TableHead>
                  )}
              </TableRow>
            </TableHeader>

            <TableBody
              className="border border-border"
              style={{
                height: height ? `${height}px` : '700px',
                overflowY: 'auto'
              }}
            >
              {isLoading ? (
                <TableRow style={{ height: `${ROW_HEIGHT * FIXED_ROW_COUNT}px` }}>
                  <TableCell colSpan={colSpan} className="h-full text-center">
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredData.length === 0 ? (
                <TableRow style={{ height: `${ROW_HEIGHT * FIXED_ROW_COUNT}px` }}>
                   {/* <TableRow style={{ height: `${ROW_HEIGHT}px` }}> */}
                  <TableCell colSpan={colSpan} className="h-full text-left">
                    <div className="flex justify-center items-center h-full">
                      <span className="text-small-font text-muted-foreground">
                        {SearchTerm.trim() ? "No matching results found" : emptyStateMessage}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item, index) => (
                  <TableRow
                    key={index}
                    className="hover:bg-background transition-colors duration-150"
                    style={{ height: `${ROW_HEIGHT}px` }}
                  >
                    {isSelectable && (
                      <TableCell
                        className="border-r px-1"
                        style={{
                          width: "20px",
                          minWidth: "20px",
                          maxWidth: "20px",
                          height: `${ROW_HEIGHT}px`,
                          lineHeight: `${ROW_HEIGHT}px`,
                        }}
                      >
                        <Checkbox
                          checked={selectedItems.includes(item)}
                          onCheckedChange={() => handleCheckboxChange(item)}
                        />
                      </TableCell>
                    )}
                    {visibleColumns.map((column) => (
                      <TableCell
                        key={column.key}
                        className="  border-r text-left dark:text-white px-6 text-xs"
                        style={{
                          maxWidth: `${columnWidths[column.key]}px`,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          height: `${ROW_HEIGHT}px`,
                           lineHeight: `${ROW_HEIGHT}px`,
                        }}
                      >
                        {"render" in column ? (
                          <div className="flex  block truncate justify-start items-center h-full">
                            {column.render(item)}
                          </div>
                        ) : (
                          <EllipsisTooltip
                            content={
                              typeof item[column.key] === "number"
                                ? item[column.key].toLocaleString()
                                : String(item[column.key] || "")
                            }
                            className="max-w-full"
                          />
                        )}
                      </TableCell>
                    ))}
                    {(isEdit ||
                      isDelete ||
                      isView ||
                      isDownload ||
                      isPause ||
                      isPlay ||
                      isRefetch ||
                      isSend ||
                      isClone) && (
                        <TableCell
                          className="border-r dark:text-white p-2 text-left text-xs"
                          style={{
                            height: `${ROW_HEIGHT}px`,
                            lineHeight: `${ROW_HEIGHT}px`
                          }}
                        >
                          <div className="flex space-x-2 justify-center">
                            {isClone && (
                              <button onClick={() => onClone?.(item)} className="text-primary hover:text-gray-500">
                                <FaClone size={18} />
                              </button>
                            )}
                            {isView && (
                              <button onClick={() => onView?.(item)} className="text-primary hover:text-gray-500">
                                <MdVisibility size={18} />
                              </button>
                            )}
                            {isEdit && (
                              <button onClick={() => onEdit?.(item)} className="text-primary hover:text-gray-500">
                                <MdEdit size={18} />
                              </button>
                            )}
                            {isDelete && (
                              <button onClick={() => onDelete?.(item)} className="text-primary hover:text-gray-500">
                                <MdDelete size={18} />
                              </button>
                            )}
                            {isRefetch && (
                              <button
                                onClick={() => setIsRefetchModalOpen(true)}
                                className="text-primary hover:text-gray-500"
                              >
                                <FiRefreshCw size={18} />
                              </button>
                            )}
                            {isSend && (
                              <button onClick={() => onSend?.(item)} className="text-primary hover:text-gray-500">
                                <IoIosSend size={18} />
                              </button>
                            )}
                            {isDownload && (
                              <button onClick={() => onDownload?.(item)} className="text-primary hover:text-gray-500">
                                <MdFileDownload size={18} />
                              </button>
                            )}
                            {isPause && (
                              <button onClick={() => onPause?.(item)} className="text-primary hover:text-gray-500">
                                <MdPause size={18} />
                              </button>
                            )}
                            {isPlay && (
                              <button onClick={() => onPlay?.(item)} className="text-primary hover:text-gray-500">
                                <MdPlayArrow size={18} />
                              </button>
                            )}
                          </div>
                        </TableCell>
                      )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination Bar - positioned below table body */}
      {isPaginated && data.length > 0 && (
        <div className="mt-1 flex flex-col sm:flex-row items-center justify-between gap-2 p-4 rounded-lg shadow-md bg-card">
          <div className="flex items-center gap-2">
            <Select
              value={String(itemsPerPage)}
              onValueChange={(value) => {
                setItemsPerPage(Number(value))
                setCurrentPage(1)
              }}
            >
              <SelectTrigger className="w-[70px] h-[30px] outline-primary focus:ring-0 text-small-font  border-primary shadow-md dark:text-white">
                <SelectValue placeholder="Rows" />
              </SelectTrigger>
              <SelectContent className="border-primary outline focus:ring-0">
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            showFirstLast={true}
          />
        </div>
      )}

      {/* Date Range Modal */}
      <Dialog open={isRefetchModalOpen} onOpenChange={setIsRefetchModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Select Date Range</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("w-full justify-start text-left font-normal", !startDate && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid gap-2">
              <Label>End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={endDate} onSelect={setEndDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <Button onClick={handleRefetch} disabled={!startDate || !endDate} className="w-full sm:w-auto">
              Refetch Data
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ResizableTable