"use client"
import React, { useState, useEffect } from 'react';
import { useFullscreen } from "@/hooks/use-fullscreen";
import { CartesianGrid, Line, LineChart, XAxis, YAxis, Tooltip, Label, LabelList, ResponsiveContainer } from "recharts"
import { formatValue, cn } from "@/lib/utils";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  CustomTick,
} from "@/components/ui/chart";
import HeaderRow from "../HeaderRow";
import { InformationCard } from "../InformationCard";
import { Loader2 } from "lucide-react";
import { DynamicLabel } from '../Dynamic labels';
import { useTheme } from "@/components/mf/theme-context";
import CustomLegendContent from "@/components/ui/CustomLegends";


interface YAxis1 {
  yAxisId?: "left" | "right";
  orientation?: "left" | "right";
  stroke?: string,
  tickFormatter?: (value: string) => string;
  title?: string;

}
interface YLeftAxis {
  title: string
}
interface DoubleLineProps {
  chartData?:
  {
    label: string;
    [key: string]: string | number;
  }[]
  chartConfig?: {
    [key: string]: {
      label: string;
      color: string;
    };
  };
  rightAxisKeys?: string[];
  InformCard?: { title: string, desc: string }[];
  isInformCard?: boolean;
  handleExport?: () => void;
  onExpand?: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  LinechartTitle?: string;
  AxisLabel?: string;
  isLoading?: boolean;
  selectoptions?: string[];
  selectedFrequency?: string;
  placeholder?: string;
  isPercentage?: boolean;
  handleFrequencyChange?: (value: string) => void;
  yAxisXOffsetFullscreen?: number;
  yAxisXOffset?: number;
  YAxis1?: YAxis1;
  titley1_color?: string;
  titley_color?: string; 
  YLeftAxis?: YLeftAxis;
  datalength?: number;
  leftmargin?: number;
  rightmargin?: number;
  yAxisPercentage?: boolean;
  PercentageLabel?: string;
  axisliney?:boolean;
  axisliney1?:boolean;
  isexportcsv?:boolean;
  isLegend?:boolean
  CustomLegend?:boolean;
}

const DoubleLineChart: React.FC<DoubleLineProps> = ({
  chartData = [],
  chartConfig = {},
  isLegend=true,
  handleExport,
  onExport,
  onExpand,
  LinechartTitle,
  selectoptions,
  selectedFrequency,
  isPercentage,
  isexportcsv=false,
  handleFrequencyChange,
  isLoading,
  placeholder,
  title,
  isSelect = false,
  isRadioButton = false,
  AxisLabel = "Value",
  InformCard = [],
  isInformCard = false,
  yAxisXOffsetFullscreen,
  yAxisXOffset,
  YAxis1 = {
    yAxisId: "right",
    orientation: "right",
    stroke: "hsl(var(--chart-3))",
    title: "",
    tickFormatter: (value: number) => `${value}%`,

  },
  axisliney=false,
  axisliney1=false,
  datalength = 3,
  YLeftAxis = {
    title: ""
  },
  titley1_color = "",
  titley_color="",
  rightAxisKeys = [],
  leftmargin = 12,
  rightmargin = 20,
  yAxisPercentage = false,
  PercentageLabel = "",
  CustomLegend=false,
}) => {
   const { isDarkMode } = useTheme();
  const isFullscreen = useFullscreen();
   const labels = Object.values(chartConfig || {}).map(item => item.label);
  const colors = Object.values(chartConfig || {}).map(item => item.color);

  // Calculate responsive chart height based on fullscreen state
  const getChartHeight = () => {
    if (isFullscreen) {
      return 550; // Fixed height for fullscreen to prevent overflow
    }
    return Math.min(chartData.length * 10, 400); // Normal height
  };

  const chartHeight = getChartHeight();
  // Determine the minimum height based on the number of entries
  const mergedYAxis1Props = { ...YAxis1 };

  return (
    <Card className="border-none">
      <HeaderRow
        title={title}
        onExpand={onExpand}
        handleExport={handleExport}
        isRadioButton={isRadioButton}
        isSelect={isSelect}
        handleFrequencyChange={handleFrequencyChange}
        selectoptions={selectoptions}
        selectedFrequency={selectedFrequency}
        onExport={onExport}
        placeholder={placeholder}
        isexportcsv={isexportcsv}
      />
      {isInformCard && (
        <div className={`flex-1 px-4 flex flex-row ${isFullscreen ? 'gap-6 w-full' : ''}`}>
          {InformCard?.map((item, index) => (
            <InformationCard
              key={index}
              InformTitle={item.title}
              informDescription={item.desc}
              isFullscreen={isFullscreen}
            />
          ))}
        </div>
      )}
      <CardHeader className="items-center pt-0">
        <CardTitle className="text-body font-semibold">{LinechartTitle}</CardTitle>
      </CardHeader>
      <CardContent className={`${isFullscreen ? 'h-[600px]' : 'h-[280px]'} w-full ${isFullscreen ? '' : 'overflow-x-auto scrollbar'} chart-card-content`} >
        {isLoading ? (
          <div className="flex items-center justify-center h-[250px]">
            <Loader2 className=" h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
        
            <ChartContainer config={chartConfig} style={{ height: isFullscreen ? '550px' : '250px', width: '100%' }} >
              {chartData.length > 0 ? (
                  <div
      style={{
        overflowX: "auto",
        width: "100%",
      }}
    >
      <div
        style={{
          minWidth: `${chartData.length * 120}px`, // Dynamically stretch chart width
          height: isFullscreen ? "550px" : "250px",
        }}
      >
                <ResponsiveContainer height={isFullscreen ? 550 : 250} width={"100%"}>
                  <LineChart
                   width={chartData?.length * 120}
                    accessibilityLayer
                    data={chartData}
                    margin={{
                      left: leftmargin,
                      right: rightmargin,
                      top: 20,
                      bottom: 10,
                    }}
                    height={chartHeight}
                    barGap={10}
                  >
                    <CartesianGrid vertical={false} />
                    <XAxis
                      dataKey="label"
                      tickLine={false}
                      axisLine={false}
                      interval={0}
                      tickMargin={20}
                      angle={270}
                      tickFormatter={(value) => {
                        return value.length > datalength ? value.slice(0, datalength) + "..." : value;
                      }}
                      className="text-body"
                      style={{ fontSize: isFullscreen ? '16px' : '10px' }}
                      tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} Fullscreen={yAxisXOffsetFullscreen} />}
                    />

                    <YAxis
                      tickLine={false}
                      axisLine={axisliney}
                      tickMargin={8}
                      textAnchor='middle'
                      tickFormatter={(value) => formatValue(value as number, AxisLabel)}
                      className="text-body mt-2"
                      style={{ fontSize: isFullscreen ? '16px' : '10px' }}
                      tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" textAnchor="middle" truncateLength={10} yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />}
                      yAxisId="left">
                      <Label style={{ fontSize: isFullscreen ? '18px' : '12px' ,fill:titley_color}} className={`font-semibold`} value={YLeftAxis.title} textAnchor="middle"angle={-90} position={{ x: -10, y: 70 }} offset={-20} />
                    </YAxis>


                    {YAxis1 && (
                      <YAxis className="text-body mt-2 p-2"
                        {...mergedYAxis1Props}
                        tickLine={false}
                        axisLine={axisliney1}
                        tickMargin={15}
                        //tickFormatter={(value) => `${value}%`}
                        style={{ fontSize: isFullscreen ? '16px' : '10px', padding: '3px' }}
                        tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" yAxisPercentage={yAxisPercentage} Fullscreen={yAxisXOffsetFullscreen} />} >
                        <Label style={{ fontSize: isFullscreen ? '18px' : '12px', fill: titley1_color }} className={`font-semibold`} value={YAxis1.title} angle={-90} textAnchor="middle" position={{ x: 60, y: 70 }} offset={-20} />
                      </YAxis>


                    )}
                    <ChartTooltip cursor={false} content={<ChartTooltipContent isPercentage={isPercentage} isFullscreen={isFullscreen} />} />
                   {isLegend &&(
                    <ChartLegend
                      content={<ChartLegendContent isFullscreen={isFullscreen} />}
                    />
                    )}
                    {Object.keys(chartConfig).map((key, index) => {
                      const useRightAxis = rightAxisKeys?.includes(key);
                      return (
                        <Line
                          key={index}
                          dataKey={key}
                          type="linear"
                          stroke={chartConfig[key].color}
                          strokeWidth={isFullscreen ? 3 : 2}
                          dot={{
                            fill: chartConfig[key].color, // Just pass the color string directly here
                            r: isFullscreen ? 4 : 3, // Larger dots in fullscreen
                          }}
                          yAxisId={useRightAxis ? "right" : "left"}
                        >
                          <LabelList
                            position="top"
                            offset={isFullscreen ? 16 : 12}
                            content={(props) => DynamicLabel({ ...props,isDarkMode, isPercentageLabel: key === PercentageLabel, })}
                          />


                        </Line>
                      );
                    })}
                  </LineChart>
                </ResponsiveContainer>
                 </div>
            </div>
              ) : (<div className="flex items-center justify-center h-[400px]">
                <span className="text-small-font">No Data Found.!</span>
              </div>)}
             
            </ChartContainer>
        )}
      </CardContent>
       {CustomLegend && (
            <div className={`bottom-0 z-10  pt-1 pb-2 sm:overflow-visible md:overflow-visible ${isFullscreen ? 'pt-4' : 'pt-0'}`}>
            <CustomLegendContent labels={labels} colors={colors} />
          </div>
        )}
    </Card>
  )
}
export default DoubleLineChart;
