
import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
});

export const useRnfTable = (params: any, enabled = true) =>
  useAppQuery(
    ['rnfTable', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.RNF_TABLE}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
export const useRnfUniqueCountsDispersion = (params: any, enabled = true) =>
  useAppQuery(
    ['rnfUniqueCountsDispersion', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.RNF_UNIQUE_COUNTS}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  export const useRnfImpressionCountsDispersion =(params: any, enabled = true) =>
  useAppQuery(
    ['rnfImpressionCountsDispersion', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.RNF_IMPRESSION_COUNTS}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );