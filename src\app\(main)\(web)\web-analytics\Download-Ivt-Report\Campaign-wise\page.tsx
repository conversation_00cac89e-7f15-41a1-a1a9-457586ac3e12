"use client";
import ResizableTable from '@/components/mf/TableComponent';
import React from 'react';
import { Card } from '@/components/ui/card';
import ChartAreaGradient from '@/components/ui/Areachart';
import { useCallback ,useRef,useState} from 'react';
import { onExpand, downloadURI } from "@/lib/utils";
import domToImage from "dom-to-image";




interface ColumnGCR {
    title: any,
    key: keyof userDataCR,
  }
interface userDataCR {
    "Inserted Date": string;
    "Campaign Name / ID": string;
    "Publisher": string;
    "Sub-Publisher": string;
    "Page Id": string;
    IP:string;
    "User Agent":string;
    Placement:string;
    "IVT Category":string;
    "IVT Sub Category":string;
  }
const CampaignReportData =[
    {
        "Inserted Date": "2023-09-25",
        "Campaign Name / ID": "123456789",
        "Publisher": "Publisher1",
        "Sub-Publisher": "Sub-Publisher1",
        "Page Id": "123456789",
        IP:"***************",
        "User Agent":"Chrome",
        Placement:"Placement1",
        "IVT Category":"IVT Category1",
    },
    {
        "Inserted Date": "2023-09-25",
        "Campaign Name / ID": "123456789",
        "Publisher": "Publisher1",
        "Sub-Publisher": "Sub-Publisher1",
        "Page Id": "123456789",
        IP:"***************",
        "User Agent":"Chrome",
        Placement:"Placement1",
    },
    {
        "Inserted Date": "2023-09-25",
        "Campaign Name / ID": "123456789",
        "Publisher": "Publisher1",
        "Sub-Publisher": "Sub-Publisher1",
        "Page Id": "123456789",
        IP:"***************",
        "User Agent":"Chrome",
        Placement:"Placement1",
    },
    {
        "Inserted Date": "2023-09-25",
        "Campaign Name / ID": "123456789",
        "Publisher": "Publisher1",
        "Sub-Publisher": "Sub-Publisher1",
        "Page Id": "123456789",
        IP:"***************",
        "User Agent":"Chrome",
        Placement:"Placement1",
    }
]
const RealTimeColumn: ColumnGCR[] = [
    { title: "Inserted Date", key: "Inserted Date" },
    { title: "Campaign Name / ID", key: "Campaign Name / ID" },
    { title: "Publisher", key: "Publisher" },
    { title: "Sub-Publisher", key: "Sub-Publisher" },
    { title: "Page Id", key: "Page Id" },
    { title: "IP", key: "IP" },
    { title: "User Agent", key: "User Agent" },
    { title: "Placement", key: "Placement" },
    { title: "IVT Category", key: "IVT Category" },
    { title: "IVT Sub Category", key: "IVT Sub Category" },
  
  ]
// const chartData = [
//   { month: "January", desktop: 186, mobile: 80 },
//   { month: "February", desktop: 305, mobile: 200 },
//   { month: "March", desktop: 237, mobile: 120 },
//   { month: "April", desktop: 73, mobile: 190 },
//   { month: "May", desktop: 209, mobile: 130 },
//   { month: "June", desktop: 214, mobile: 140 },
// ]

// const chartConfig = {
//   desktop: {
//     label: "Desktop",
//     color: "#00028c",

//   },
//   mobile: {
//     label: "Mobile",
//     color: "#F87B1B",

//   },
// } satisfies ChartConfig

function CampaignReport() {
    const cardRefs = useRef<Record<string, HTMLElement | null>>({});
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
    
  
    const onExport = useCallback(
      async (s: string, title: string, key: string) => {
        const ref = cardRefs.current[key];
        if (!ref) return;
  
        switch (s) {
          case "png":
            const screenshot = await domToImage.toPng(ref);
            downloadURI(screenshot, title + ".png");
            break;
          default:
        }
      },
      []
    );
  
    const handleExpand = (key: string) => {
      onExpand(key, cardRefs, expandedCard, setExpandedCard);
    };
  return (
    <div className="min-h-[calc(100vh-4rem)]">
      <ResizableTable
      isPaginated={true}
      data={CampaignReportData}
      columns={RealTimeColumn}
      isSearchable={true}
      isSelectable={true}
      isDownload={true}

      />
  </div>

// <div className='grid grid-cols-2 gap-2 p-2 w-full'>
//   <div>
//   <Card  ref={(el) => (cardRefs.current["area_chart"] = el!)}
//   className='  gap-2 p-2 '>
// <ChartAreaGradient
// chartData={chartData}
// chartConfig={chartConfig}
// XaxisLine={true}
// Xdatakey="month"
// CartesianGridVertical={true}
//  title="Area chart"
//                 onExport={() => onExport("png", "Area Chart", "area_chart")}
//                 onExpand={() => handleExpand("area_chart")}

// />
//   </Card>
//   {/* <Card className='max-h-[500px]  gap-2 p-2'>

//   </Card> */}
//   </div>
// </div>
 )
}

 export default CampaignReport