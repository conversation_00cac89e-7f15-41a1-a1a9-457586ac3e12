'use client'
import React from 'react';
import SummaryCardGroup from '../commonmodule/page';
import { Filter } from "@/components/mf/Filters";
import { useChannelsFilter, useCampaignsFilter, usePublishersFilter } from '../../FilterApi';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import AttractiveCard from '@/components/ui/attractive-card';
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from "../../Filters/buildFilters";
import { onExpand, downloadURI, debounce, handleExportData } from "@/lib/utils";
import domToImage from "dom-to-image";
import { useCallback, useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import RadialMetricsDesign from '@/components/ui/radial_metric';
import { useWastageFilters } from '../../Filters/useFilters'

const Attention_metric = () => {
  const cardRefs = useRef<Record<string, HTMLElement | null>>({});
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const { selectedPackage } = usePackage();
  const { startDate, endDate } = useDateRange();

  const params = {
    package_name: selectedPackage,
    start_date: startDate,
    end_date: endDate,
  };
  const [query, setQuery] = useState({
    publishers: ["all"],
    campaigns: ["all"],
    channels: ["all"],
    fraudcategory:["all"],
    subfraudcategory:["all"],
    creative_id:["all"],
    publisher_ids:["all"],
  });
  const [loadedFilter, setLoadedFilter] = useState<FilterState>({});

  const onExport = useCallback(
    async (s: string, title: string, key: string) => {
      const ref = cardRefs.current[key];
      if (!ref) return;

      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    []
  );
  const handleExpand = (key: string) => {
    onExpand(key, cardRefs, expandedCard, setExpandedCard);
  };
const filter = useWastageFilters(params, query);

  const handleFilterChange = useFilterChangeHandler(
    loadedFilter,
    setQuery,
    setLoadedFilter
  );
  const CardNamevalue = [
    { title: "PIP", percentage: 12,color: "#739072" },
    { title: "Screen Orientation", percentage: 15, color: " #015551" },
    { title: "On-Page /Off-Page", percentage: 23, color: " #9BE8D8" },
    { title: "Volume Mute", percentage: 50, color: " #093FB4" },
    { title: "Engagement(CTR)", percentage: 30, color: " #E7CEA6" },
    { title: "Completion Rate", percentage: 60, color: " #06923E" },
    { title: "Viewability", percentage: 40, color: " #E8988A"},
    { title: "In View Time", percentage: 90, color: " #D6D85D",showtime : true},
    { title: "Start to Complete Ratio", percentage: 10, color: " #06923E" },
  ];
  return (
    <div className=" w-full grid grid-col p-2 gap-2">
      <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
        <Filter filter={filter} onChange={handleFilterChange} />
      </div>
      <SummaryCardGroup />
      <div className="gap-1 w-full">
  <div className="w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2">
    Attention Metrics
  </div>

  {/* Outer Grid: 2 Uneven Columns */}
  <div className="grid grid-cols-1 lg:grid-cols-[1fr_3fr] gap-2 w-full">
    {/* First Column */}
    <div className="shadow-md p-2 border-2">
      <AttractiveCard
             title="Attention Score"
              circularPercentage={76}
              width="w-full"
              height="h-full"
              sizeRadial={200} 
              />
    </div>

    {/* Second Column with 3 Cards Per Row */}
   <Card className="w-full dark:bg-background">
          <h3 className='font-semibold p-2'>Attention Signals (Proxy Variables)</h3>
        <Card className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3  lg:grid-cols-3 xl:grid-cols-5 gap-2 p-2 dark:bg-background">
          {CardNamevalue.map((card, idx) => (
            <RadialMetricsDesign
            key={idx}
            Radialname={card.title}
            color={card.color}
            value={card.percentage}
            showTime={card.showtime}
           
            />
           ))}
        </Card>
     
    </Card>
  </div>
</div>
          </div>
          
  );
}
export default Attention_metric;
