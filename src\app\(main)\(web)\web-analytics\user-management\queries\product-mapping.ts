"use client";

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
} from '@tanstack/react-query';
import axios, { AxiosError } from 'axios';
import Endpoint from '../../../common/endpoint';
import { UnauthorizedError } from '@/common/errors';
import apiClient from '../../../ApiClient/page';

// ---------- Types ----------
type RoleData = {
  Role: string;
  Product: string;
  Package?: string | string[];
  Packages?: string[]; // ✅ Add this to handle backend shape
  // EmbeddedLabel?: string;
  // URL?: string;
};
export interface PaginatedUserListResponse {
  data: UserData[];
  total_pages: number;
}
export interface UserData {
  _id?: string;
  Name?: string;
  Email?: string;
  Phone?: string;
  //Status?:boolean;
  LastLogin?:string;
  Products?: string[];
  Roles?: RoleData[];
  Packages?: string[];
  Gender?:string;
}
export interface UserListFilters {
  email?: string;
  page?: number;
  limit?: number;
}
export interface packageFilters {
  package_name?: string;
  page?: number;
  limit?: number;
}
export interface AddUserRolePayload {
  email: string;
  add_role: Array<{
    Role: string;
    Product: string;
    Packages: string[];
    // EmbeddedLabel?: string; // Not needed
    // URL?: string; // Not needed
  }>;
}
interface EditPackagePayload {
  PackageName: string;
  ProductName: string[];
}
export interface UpdateUserPayload {
  email: string;
  name: string;
}
export interface ProductPackageResponse {
  data: ProductPackageEntry[];
  total_pages: number;
}

export interface ProductPackageEntry {
  ProductName: string;
  PackageName: string[];
}

export interface SelectOption {
  value: string;
  label: string;
}
interface UserEmailResponse {
  user_emails: string[];
  status_code: number;
}

// ---------- Utility Functions ----------
const getToken = () =>
  typeof window !== 'undefined' ? localStorage.getItem('IDToken') || '' : '';

const handleUnauthorized = () => {
  const currentPath = window.location.pathname;
  if (currentPath !== '/') localStorage.setItem('redirectPath', currentPath);
  localStorage.clear();
  localStorage.clear();
  window.location.href = '/';
};

// ---------- API Wrapper ----------
const apiCall = async ({
  url,
  method,
  data,
  headers = {},
  params,
}: {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
  params?: any;
}) => {
  const token = getToken();
  try {
   const response = await apiClient({ 

      url,
      method,
      data,
      params,
      headers: {
        Authorization: token, 
        'Content-Type': 'application/json',
        ...headers,
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError && error.response?.status === 401) {
      handleUnauthorized();
      throw new UnauthorizedError('Session expired');
    }
    throw error;
  }
};

// ---------- Query & Mutation Hooks ----------
export const useAppQuery = <TData = any>(
  key: string[],
  fetchFn: () => Promise<TData>,
  options?: Partial<UseQueryOptions<TData>>
) => {
  return useQuery<TData>({
    queryKey: key,
    queryFn: fetchFn,
    retry: (failureCount, error: any) => {
      if (error instanceof AxiosError && error.response?.status === 401) return false;
      return failureCount < 3;
    },
    ...options,
  });
};

export const useAppMutation = <TData = any, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: Partial<UseMutationOptions<TData, unknown, TVariables>>
) => {
  return useMutation<TData, unknown, TVariables>({
    mutationFn,
    ...options,
  });
};

// ---------- Queries ----------
export const useUserListQuery = ({ email="", page = 1, limit = 10 }: UserListFilters) =>
  useAppQuery<PaginatedUserListResponse>(
    ['userList', email, String(page), String(limit)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.USER_LIST}`,
        method: 'GET',
        params: {
          email,
          page,
          limit,
        },
      })
  );

export const usePackageMappingQuery = ({ package_name = '', page = 1, limit = 10 }: packageFilters) =>
  useAppQuery<ProductPackageResponse>(
    ['packagelist', package_name, String(page), String(limit)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.PACKAGE_MAPPING}`,
        method: 'GET',
        params: {
          package_name,
          page,
          limit,
        },
      })
  );

export const useListUserQuery = () =>
  useAppQuery<UserEmailResponse>(
    ['listUser'],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.LIST_USER}`,
        method: 'GET',
      }),
    {
      enabled: false,
      retry: (failureCount, error) => {
        if (error instanceof AxiosError && error.response?.status === 401) return false;
        return failureCount < 3;
      },
      select: (res) => (Array.isArray(res) ? res : res),
    }
  );

export const useProductListQuery = () =>
  useAppQuery<SelectOption[]>(
    ['productList'],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.PRODUCT}`,
        method: 'GET',
      }).then((res) =>
        res.map((product: string) => ({ value: product, label: product }))
      ),
    {
      enabled: false
    }
  );

export const useRoleListQuery = (productName: string, enabled: boolean = true) =>{
const query = useAppQuery<SelectOption[]>(    ['roleList', productName],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.ROLE_LIST}`,
        method: 'POST',
        data: { product_name: productName },
      }).then((res) =>
        res.map((role: { _id: string; Alias: string }) => ({
          value: role._id,
          label: role.Alias,
        }))
      ),
    {
      enabled: !!productName && enabled,
    }
  );
   // ✅ Custom safe refetch with fresh value
   const refetchWithRole = async (prodName: string) => {
    const response = await apiCall({
      url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.ROLE_LIST}`,
      method: 'POST',
      data: { product_name: prodName },
    });

    return response.map((role: { _id: string; Alias: string }) => ({
      value: role._id,
      label: role.Alias,
    }));
  };

  return { ...query, refetchWithRole };
};

export const usePackageListQuery = (productName: string, enabled: boolean = true) =>{
const query = useAppQuery<SelectOption[]>(
    ['packagelist', productName],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.PACKAGE}`,
        method: 'POST',
        data: { product_name: productName },
      }).then((res) =>
        res.map((pkg: { PackageName: string; PackageTitle?: string }) => ({
          value: pkg.PackageName,
          label: pkg.PackageTitle && pkg.PackageTitle.trim() !== '' ? pkg.PackageTitle : pkg.PackageName
        }))
      ),
    {
      enabled: !!productName && enabled,
    }
  );

  const refetchWithPackage = async (prodName: string) => {
    const response = await apiCall({
      url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.PACKAGE}`,
      method: 'POST',
      data: { product_name: prodName },
    });

    return response.map((pkg: { PackageName: string; PackageTitle?: string }) => ({
      value: pkg.PackageName,
      label: pkg.PackageTitle && pkg.PackageTitle.trim() !== '' ? pkg.PackageTitle : pkg.PackageName
    }));
  };

  return { ...query, refetchWithPackage };
};
// ---------- Mutations ----------
export const useAddUserRoleMutation = () => {
  const queryClient = useQueryClient();
  return useAppMutation(
    (payload: AddUserRolePayload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.ADD_USER}`,
        method: 'POST',
        data: payload,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        //queryClient.invalidateQueries({ queryKey: ['listUser'] });
      },
      meta: { mutationName: 'addUserRole' }
    }
  );
};

export const useEditUserRoleMutation = () => {
  const queryClient = useQueryClient();
  return useAppMutation(
    (payload: AddUserRolePayload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.EDIT_USERROLES}`,
        method: 'PUT',
        data: payload,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        //queryClient.invalidateQueries({ queryKey: ['listUser'] });
      },
    }
  );
};

export const useDeleteUserProductMutation = () => {
  const queryClient = useQueryClient();
  return useAppMutation(
    (userId: string) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.DELETE_PRODUCTUSER}/${userId}?product_name=Web%20Performance`,
        method: 'DELETE',
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        queryClient.invalidateQueries({ queryKey: ['listUser'] });
      },
    }
  );
};
export const useDeleteUserMutation = () => {
  const queryClient = useQueryClient();
  return useAppMutation(
    (userId: string) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.DELETE_USER}/${userId}?product_name=Web%20Performance`,
        method: 'DELETE',
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        queryClient.invalidateQueries({ queryKey: ['listUser'] });
      },
    }
  );
};

export const useUpdateUserMutation = () => {
  const queryClient = useQueryClient();
  return useAppMutation(
    (payload: UpdateUserPayload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}/access_control/editUser?product_name=Web%20Performance`,
        method: 'PUT',
        data: payload,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
        queryClient.invalidateQueries({ queryKey: ['listUser'] });
      },
    }
  );
};

//User -config api
export const useCreateUserMutation = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    (data: any) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.CREATE_USER}`,
        method: 'POST',
        data,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
      },
    }
  );
};
export const useEditUserMutation = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    ({ id, payload }: { id: string; payload: any }) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.EDIT_USER}/${id}`, // ✅ correct
        method: 'PUT',
        data: payload,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
      },
    }
  );
};
export const useDeletePackageMutation = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    (packageName: string) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.DELETE_PACKAGE}/${packageName}?product_name=Web%20Performance`,
        method: 'DELETE',
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['packagelist'] });
      },
    }
  );
};

export const useCreatePackageMutation = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    (data: any) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.CREATE_USER}`,
        method: 'POST',
        data,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['userList'] });
      },
    }
  );
};
export const useCreatePackage = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    (data: any) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.CREATE_PACKAGE}`,
        method: 'POST',
        data,
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['packagelist'] });
      },
    }
  );
};

export const useEditPackageMutation = () => {
  const queryClient = useQueryClient();

  return useAppMutation(
    ({ PackageName, ProductName }: EditPackagePayload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.EDIT_PACKAGE}/${PackageName}`,
        method: 'PUT',
        data: { PackageName, ProductName }, // or just ProductName if PackageName is only needed in URL
      }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['packagelist'] });
      },
    }
  );
};