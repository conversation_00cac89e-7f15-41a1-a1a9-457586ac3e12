interface ColorConfig {
  [key: string]: {
    color: string;
    label: string;
  };
}

interface OverallSummaryProps {
  colorConfig?: ColorConfig;
}

const OverallSummary: React.FC<OverallSummaryProps> = ({
  colorConfig = {}
}) => {
  const getColorForFraudType = (fraudType: string) => {
    // Early return if colorConfig is empty
    if (!colorConfig || Object.keys(colorConfig).length === 0) {
      return '#000000';
    }

    // Normalize both the input and the keys for comparison
    const normalizeString = (str: string) => {
      return str
        .toLowerCase() // Convert to lowercase
        .replace(/\s+/g, '') // Remove all spaces
        .replace(/[^a-z0-9]/g, ''); // Remove special characters
    };

    const normalizedInput = normalizeString(fraudType);
    
    // First try: Exact normalized match
    let matchingKey = Object.keys(colorConfig).find(key => {
      const normalizedKey = normalizeString(key);
      return normalizedKey === normalizedInput;
    });

    // Second try: Partial match (for cases like "Behavior Fraud" vs "Behavior")
    if (!matchingKey) {
      const inputWords = fraudType.toLowerCase().split(/\s+/).filter(word => word.length > 0);
      
      matchingKey = Object.keys(colorConfig).find(key => {
        const keyWords = key.toLowerCase().split(/\s+/).filter(word => word.length > 0);
        
        // Check if any word from input matches any word from key
        return inputWords.some(inputWord => 
          keyWords.some(keyWord => 
            inputWord.includes(keyWord) || keyWord.includes(inputWord)
          )
        );
      });
    }

    // Third try: Fuzzy match using similarity
    if (!matchingKey) {
      const similarityThreshold = 0.7; // 70% similarity threshold
      
      matchingKey = Object.keys(colorConfig).find(key => {
        const normalizedKey = normalizeString(key);
        const similarity = calculateSimilarity(normalizedInput, normalizedKey);
        return similarity >= similarityThreshold;
      });
    }

    // If found, return the color, otherwise return black
    return matchingKey ? colorConfig[matchingKey].color : '#000000';
  };

  // Helper function to calculate string similarity (Levenshtein distance based)
  const calculateSimilarity = (str1: string, str2: string): number => {
    if (str1 === str2) return 1.0;
    if (str1.length === 0) return 0.0;
    if (str2.length === 0) return 0.0;

    const matrix = [];
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return (maxLength - matrix[str2.length][str1.length]) / maxLength;
  };

  // ... rest of the component code ...
  // Use getColorForFraudType where you need to apply colors
  // For example:
  // style={{ color: getColorForFraudType(fraudType) }}

  return null; // Placeholder return - replace with actual JSX when implementing
}; 