import * as React from "react"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface SelectNormalProps {
  options: string[]
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function SelectNormal({
  options,
  value,
  onChange,
  placeholder,
  className,
  disabled = false
}: SelectNormalProps) {
  console.log("SelectNormal - Received placeholder:", placeholder);
  console.log("SelectNormal - Received value:", value);
  
  // Check if the value is in the options list
  const isValidValue = options.includes(value || "");
  
  return (
    <Select 
      value={isValidValue ? value : ""} 
      onValueChange={onChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {options.map((option, index) => (
            <SelectItem 
              key={index} 
              value={option}
              className="cursor-pointer"
            >
              {option}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  )
} 