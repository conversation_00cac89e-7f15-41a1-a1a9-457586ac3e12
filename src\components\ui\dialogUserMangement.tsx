"use client"

import type React from "react"
import { useEffect, use<PERSON>em<PERSON>, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, DialogDescription } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { useForm, Controller } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import PasswordInput from "@/components/ui/PasswordInput"
import { cn } from "@/lib/utils"
import { Switch } from "@/components/ui/switch"
import { MultiSelect } from "@/components/ui/multi-select"
import { Loader2 } from "lucide-react"
import { useFormContext } from "react-hook-form";

type FieldType = "input" | "select" | "password" | "delete" | "switch";

interface Field {
  name: string;
  label: string;
  type: FieldType;
  disabled?: boolean;
  dependsOn?: string;
onDependencyChange?: (value: string | string[]) => void;

  options?: SelectOption[];
  className?: string;
  validation?: {
    pattern?: {
      value: RegExp;
      message: string;
    };
    required?: string;
  };
  isMultiple?: boolean;
  description?: string;
}

interface SelectOption {
  value: string;
  label: string;
}

interface Props {
  open?: boolean;
  setOpen: (val: boolean) => void;
  fields?: Field[];
  onSubmit: (data: Record<string, string>) => void;
  DialogTitles?: string;
  description?: string;
defaultValues?: Record<string, string | string[]>;
  dialogMode?: string;
  isViewMode: boolean;
  onDelete?: () => void;
  viewProduct?: boolean;
  setViewProduct?: (val: boolean) => void;
  isSubmitting?: boolean;
  buttonClass?: string;
  saveButtonText?: string;
}

const ReusableDialog: React.FC<Props> = ({
  open,
  setOpen,
  fields = [],
  onSubmit,
  DialogTitles,
  description,
  defaultValues,
  dialogMode,
  isViewMode,
  onDelete,
  viewProduct = false,
  setViewProduct,
  isSubmitting,
  buttonClass = "bg-primary text-white hover:bg-primary/90",
  saveButtonText = "Save"
}) => {
  // Create schema from fields
 const schema = z.object(
  fields.reduce((acc, field) => {
    if (["select", "input", "password"].includes(field.type)) {
      let fieldSchema: z.ZodTypeAny;

      if (field.isMultiple) {
        // For multiselect, expect an array of strings
        fieldSchema = z.array(z.string());

        if (field.validation?.required) {
          fieldSchema = fieldSchema.min(1, field.validation.required); // At least one value required
        }
      } else {
        // Single select/input
        fieldSchema = z.string();

        if (field.validation?.required) {
          fieldSchema = fieldSchema.min(1, field.validation.required);
        }

        if (field.validation?.pattern) {
          fieldSchema = fieldSchema.regex(
            field.validation.pattern.value,
            field.validation.pattern.message
          );
        }
      }

      acc[field.name] = fieldSchema;
    }

    return acc;
  }, {} as Record<string, z.ZodTypeAny>)
);

const computedDefaultValues = useMemo(() => {
  return fields.reduce((acc, field) => {
    const defaultVal = defaultValues?.[field.name];

    if (field.isMultiple) {
      acc[field.name] = Array.isArray(defaultVal)
        ? defaultVal
        : typeof defaultVal === "string" && defaultVal.length > 0
        ? [defaultVal]
        : [];
    } else {
      acc[field.name] = defaultVal ?? "";
    }

    return acc;
  }, {} as Record<string, string | string[]>);
}, [fields, defaultValues]);


  // Set up form
type FormData = z.infer<typeof schema>;

const {
  register,
  handleSubmit,
  reset,
  control,
  watch,
  setValue,
  getValues,
  formState: { errors, isValid },
} = useForm<FormData>({
  resolver: zodResolver(schema),
  mode: "onChange",
  defaultValues: computedDefaultValues,
});
  // Watch all fields for dependencies
  const formValues = watch()

  // Handle field changes (for dependencies)

const handleFieldChange = (
  field: Field,
  e: React.ChangeEvent<HTMLSelectElement>,
  fields: Field[],
  setValue: (name: string, value: any) => void,
  getValues: () => Record<string, any>
) => {
  const value = e.target.value;

  // Update current field value
  setValue(field.name, value);

  // Find and clear all fields that depend on this field
  const dependentFields = fields.filter(f => f.dependsOn === field.name);

  dependentFields.forEach(depField => {
    setValue(depField.name, ""); // Clear dependent field only
  });

  // Trigger dependency update callback (e.g. to fetch new options)
  if (field.onDependencyChange) {
    field.onDependencyChange(value);
  }
};



  // Handle dialog close
  const handleDialogChange = (newOpen: boolean) => {
    if (!newOpen) {
      // Reset form to initial state
      reset({});
      // Call parent close handler
      setOpen(newOpen);
    } else {
      setOpen(newOpen);
    }
  }

  // Reset form when defaultValues change or dialog opens
  useEffect(() => {
    if (open) {
      reset(computedDefaultValues);
    }
  }, [open, reset, computedDefaultValues]);

  // Handle form submission
  const onFormSubmit = async (data: Record<string, string>) => {
    if (isSubmitting) return; // Prevent multiple submissions
    onSubmit(data);
  }
//  useEffect(() => {
//   console.log("Watch values:", watch());
//   console.log("Form errors:", errors);
//   console.log("Form validity:", isValid);
// }, [watch(), errors, isValid]);

  const renderField = (field: Field) => {
    const isFieldDisabled = field.disabled || isViewMode;
    const error = errors[field.name];

    // Common input styles for view mode
    const viewModeStyles = isViewMode 
      ? "text-foreground bg-muted text-small-font cursor-default hover:cursor-default focus:outline-none border-none" 
      : "";

    switch (field.type) {
      case "password":
        return (
            <Controller
              name={field.name}
              control={control}
              render={({ field: passwordField }) => (
                <PasswordInput
                  id={passwordField.name}
                value={passwordField.value || ""}
                  onChange={passwordField.onChange}
                placeholder={`Enter ${field.label.toLowerCase()}`}
                readOnly={isFieldDisabled}
                />
              )}
            />
        )
      case "input":
        return (
            <Input
              id={field.name}
              {...register(field.name)}
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled={isFieldDisabled}
              className={cn(
              "w-full h-10 text-small-font",
                viewModeStyles,
                isViewMode && " text-small-font text-foreground opacity-100",
                field.className,
                error && "border-destructive"
              )}
            />
        )
      case "select":
       if (field.isMultiple === true) {
  return (
    <div className="space-y-1">
     <Controller 
  name={field.name}
  control={control}
  render={({ field: { onChange, value } }) => (
    <MultiSelect 
      value={Array.isArray(value) ? value : []}
      onValueChange={(values: string[]) => {
        onChange(values); // ✅ this will now work
        if (field.onDependencyChange) {
          field.onDependencyChange(values);
        }
      }}
      options={field.options?.map(opt => ({
        label: opt.label,
        value: opt.value
      })) || []}
      placeholder={`Select ${field.label}`}
      className={cn(
        "w-full text-small-font placeholder:text-small-font",
        viewModeStyles,
        isViewMode && "text-foreground opacity-100 text-small-font",
        field.className,
        error && "border-destructive"
      )}
      disabled={isFieldDisabled}
    />
  )}
/>
      {field.description && (
        <p className="text-small-font text-muted-foreground">{field.description}</p>
      )}
    </div>
  );
}

        return (
          <div className="space-y-1">
            <select
              id={field.name}
              {...register(field.name)}
              disabled={isFieldDisabled}
             onChange={(e) => handleFieldChange(field, e, fields, setValue, getValues)}

              className={cn(
                "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-small-font ring-offset-background file:border-0 file:bg-transparent file:text-small-font file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                viewModeStyles,
                isViewMode && "text-foreground opacity-100 text-small-font",
                field.className,
                error && "border-destructive"
              )}
            >
              <option  className="text-small-font" value="">Select {field.label}</option>
              {field.options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {field.description && (
              <p className="text-small-font text-muted-foreground">{field.description}</p>
            )}
          </div>
        )
      case "switch":
        return (
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <Switch
                id={field.name}
                {...register(field.name)}
                disabled={isFieldDisabled}
                className={cn(
                  isViewMode && "data-[state=checked]:bg-muted-foreground",
                  field.className
                )}
              />
              {field.description && (
                <Label htmlFor={field.name} className="text-small-font font-normal">
                  {field.description || "Active"}
                </Label>
              )}
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent className={cn(
        "rounded-lg text-small-font",
        fields.length > 6 ? "sm:max-w-[1000px]" : "sm:max-w-[500px]"
      )}>
        <DialogHeader>
          <DialogTitle className="text-body font-semibold">
            {DialogTitles}
          </DialogTitle>
          {description && (
           <DialogDescription className="text-small-font">
                     {description || ""}
                   </DialogDescription>
          )}
        </DialogHeader>
        <form   key={fields.map(f => f.name).join("-")} onSubmit={handleSubmit(onFormSubmit)}>
          <div className="grid gap-6 py-4">
            <div className={cn(
              "grid gap-x-8 gap-y-4",
              fields.length > 6 ? "grid-cols-2" : "grid-cols-1"
            )}>
              {fields.map((field) => {
                const isFieldDisabled = field.disabled || isViewMode;
                return (
                  <div key={field.name} className="flex flex-col space-y-2">
                    <Label htmlFor={field.name} className="text-left  text-small-font pl-2 font-semibold">
                  {field.label}
                </Label>
                    <div className="w-full">
                      {field.type === "switch" ? (
                        <div className="flex items-center space-x-2 h-10">
                          <Switch
                            id={field.name}
                            {...register(field.name)}
                            disabled={isFieldDisabled}
                            className={cn(
                              isViewMode && "data-[state=checked]:bg-muted-foreground",
                              field.className
                            )}
                          />
                          <Label htmlFor={field.name} className="text-small-font font-normal">
                            {field.description || "Active"}
                          </Label>
                        </div>
                      ) : (
                        renderField(field)
                      )}
                  {errors[field.name] && (
                        <span className="text-small-font font-medium text-destructive mt-1">
                      {errors[field.name]?.message}
                    </span>
                  )}
                </div>
              </div>
                );
              })}
              </div>
          </div>
          <DialogFooter className="gap-2 sm:gap-0">
            {!isViewMode && (
              <Button 
                type="submit" 
                className={cn(
                  buttonClass,
                  "min-w-[100px] bg-primary text-small-font text-white hover:bg-primary/300"
                )}
                disabled={isSubmitting || !isValid}
              >
                {isSubmitting ? (
                  <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  saveButtonText
                )}
              </Button>
            )}
            <Button
              type="button"
              variant={isViewMode ? "default" : "default"}
              onClick={() => setOpen(false)}
              className="min-w-[100px] text-small-font text-white"
              disabled={isSubmitting}
            >
              {isViewMode ? "Close" : "Cancel"}
            </Button>
            {onDelete && !isViewMode && (
              <Button
                type="button"
                variant="destructive"
                onClick={onDelete}
                className="min-w-[100px] text-small-font text-white"
                disabled={isSubmitting}
              >
                Delete
              </Button>
            )}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default ReusableDialog