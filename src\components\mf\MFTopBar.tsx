"use client";
import { useTheme } from "./theme-context";
import {
  Sun,
  Moon,
  PanelLeftClose,
  PanelLeftOpen,
  User,
  Settings,
  ActivitySquare,
  BarChart2,
  Settings2,
  ShieldAlert,
  Timer,
  ScanLine,
  PackageSearch,
} from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import Link from "next/link";
import { Button } from "../ui/button";
import { MFSingleSelect } from "./MFSingleSelect";
import { MFDateRangePicker } from "./MFDateRangePicker";
import SignOutButton from "./SignOut";
import { usePathname } from "next/navigation";
import { getToken ,getIDToken} from "@/lib/token";
import { useEffect, useState } from "react";
// import { useApiCall } from "@/app/(main)/(web)/web-analytics/queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import Endpoint from "@/app/(main)/(web)/common/endpoint";
import { useRouter } from "next/navigation";
import { fetchMenuWithPackage,findFirstSubMenuRoute }  from "@/lib/menu-utils";
 
// Helper to recursively find the first submenu route
// function findFirstSubMenuRoute(menus: any[]): string | null {
//   for (const menu of menus) {
//     if (menu.Route && menu.Route !== "") {
//       return menu.Route;
//     }
//     if (menu.SubMenus && menu.SubMenus.length > 0) {
//       const subRoute = findFirstSubMenuRoute(menu.SubMenus);
//       if (subRoute) return subRoute;
//     }
//   }
//   return null;
// }
 
 
type ErrorResponse = {
  message: string;
};
 
type PackageResponse = string[];
 
type MFTopBarType = {
  isExpanded: boolean;
  onToggle: () => void;
  isCalender?: boolean;
  isToggle?:boolean;
};
 
const enable: string[] = [
  "app/dashboard/install",
  "/web-analytics/dashboard/overall-summary",
  "/web-analytics/dashboard/analysis-insights",
  "/web-analytics/dashboard/traffic-insights",
  "/web-analytics/dashboard/actionable-insights",
  "/web-analytics/configuration/whitelisting-ivt-category",
  "/web-analytics/configuration/real-time-protection",
  "/web-analytics/Download-Ivt-Report/LandingPage-wise",
  "/web-analytics/Download-Ivt-Report/Campaign-wise",
  "/web-analytics/configuration/call-recommendation",
  //"/web-analytics/User-Management/Users-Config",
  //"/web-analytics/User-Management/Package-Config",
  //"/web-analytics/User-Management/User-Package-Config",
  //"/web-analytics/user-Management/product_Mapping",
  "/web-analytics/reportingtool/report",
  "/web-analytics/reportingtool/generate",
  "/web-analytics/reportingtool/mail",
  "/web-analytics/configuration/ad-manager-apiAccess",
  "/web-brand/video/attention_metrics",
  "/web-brand/video/quartile_progression",
  "/web-brand/video/summary",
  "/web-brand/video/placement_level_report",
  "/web-brand/video/geo_distribution",
  "/web-brand/video/brand_safety",
  "/web-brand/video/reach_frequency",
  "/web-brand/display/summary",
  "/web-brand/display/placement_report",
  "/web-brand/display/geo_distribution",
  "/web-brand/display/brand_safety",
  "/web-brand/display/attention_metrics",
  "/web-brand/brand-safety/brand_safety",
  "/web-brand/reportingtool/report",
  "/web-brand/reportingtool/mail",
  "/web-brand/reportingtool/generate",

];
const routeTitleMap: Record<string, string> = {
  "/web-brand/video/summary": " Video Summary",
  "/web-brand/video/placement_level_report": "Placement Level Report",
  "/web-brand/video/geo_distribution": "Geo Distribution",
  "/web-brand/video/brand_safety": "Brand Safety",
  "/web-brand/video/reach_frequency": "Reach & Frequency",
  "/web-brand/video/attention_metrics": "Attention Metrics",
  "/web-brand/video/quartile_progression": "Quartile Progression",
  "/web-brand/display/summary": " Display Summary",
  "/web-brand/display/placement_report": "Placement  Level Report",
  "/web-brand/display/geo_distribution": "Geo Distribution",
  "/web-brand/display/Brand_safety": "Brand Safety",
  "/web-brand/display/attention_metrics": "Attention Metrics",
  "/web-brand/brand-safety/brand_safety": "Brand Safety Dashboard",
  "/web-brand/reportingtool/report": "Report",
   "/web-brand/reportingtool/generate": " Generate Report",
  "/web-brand/reportingtool/mail": "Mail",
};
export function MFTopBar({
  isExpanded,
  onToggle,
  isCalender = true,
  isToggle=true
}: MFTopBarType) {
  const pathname = usePathname();
  const { isDarkMode, toggleTheme } = useTheme();
  const dynamicTitle = routeTitleMap[pathname] || "";
 
 
  // Add console.log to debug the actual pathname
  // console.log('Current pathname:', pathname);
 
 
 
  // Check specifically for WhiteListing-IVT-Category page
  const isWhiteListingPage =
    pathname === "/web-analytics/configuration/whiteListing-ivt-category";
  const isRealtimeProtection =
    pathname === "/web-analytics/configuration/real-time-protection";
  const isCallRecommendationPage =
    pathname === "/web-analytics/configuration/call-recommendation";
  const isReportPage = pathname === "/web-analytics/reportingtool/report";
  const isGeneratePage=pathname === "/web-analytics/reportingtool/generate";
  const isMailPage=pathname === "/web-analytics/reportingtool/mail";
  const isBrandReport = pathname === "/web-brand/reportingtool/report";
  const isBrandGenerate = pathname === "/web-brand/reportingtool/generate";
  const isBrandMail = pathname === "/web-brand/reportingtool/mail";

  // Check if the current path is enabled
  const isEnabled = enable.some((path) => pathname.includes(path));
 
  return (
    <div className="shadow-blue-gray-900/5 col-span-2 h-auto bg-background dark:bg-gray-900 dark:text-white w-full p-2">
      <div className="flex flex-col sm:flex-row items-center gap-2 w-full">
        {/* {isToggle && ( */}
        <Button
          title="Toggle Menu"
          variant="ghost"
          className="w-full sm:w-14 rounded-md border text-center dark:bg-gray-900 dark:text-white"
          size="icon"
          onClick={onToggle}
        >
          {isExpanded ? <PanelLeftOpen /> : <PanelLeftClose />}
        </Button>
        {/* )} */}
 
        {isEnabled && (
          <div className="flex flex-col sm:flex-row gap-2 w-full">
            {/* Package Select - Always show for enabled pages */}
            <div className="w-full sm:w-auto">
              <PackageSelect />
            </div>
 
            {/* Date Range Picker - Hide for WhiteListing and CallRecommendation pages */}
            {!isWhiteListingPage && !isCallRecommendationPage && !isGeneratePage && !isRealtimeProtection && !isReportPage && !isMailPage && !isBrandGenerate && !isBrandMail &&  !isBrandReport && (
              <div className="w-full sm:w-auto">
                <MFDateRangePicker className="rounded-md  border text-body dark:bg-background  w-full" />
              </div>
            )}
           <div className=" flex flex-grow text-black justify-center items-center font-semibold text-header dark:text-white">{dynamicTitle}</div>
          </div>
        )}
 
        <div className="ml-auto flex items-center gap-2 w-full sm:w-auto justify-between">
          {/* Theme Toggle Button */}
          <Button
            onClick={toggleTheme}
            variant="ghost"
            size="icon"
            title={isDarkMode ? "Switch to Light Mode" : "Switch to Dark Mode"}
            className="rounded-md border"
          >
            {isDarkMode ? <Moon /> : <Sun />}
          </Button>
          <KebabMenu />
          {/* User PopUp */}
          <UserPopUp />
        </div>
      </div>
    </div>
  );
}
const handleMoreProductClick = (product: any) => {
  if (product.redirect_link) {
    window.open(product.redirect_link, '_blank');
  }
};
 
function UserPopUp() {
  const [Uname, setUname] = useState("");
const [username, setUsername] = useState<string | null>(null);
 
  useEffect(() => {
    
  }, []);
  useEffect(() => {
    const{name} = getIDToken();
    console.log("user name",name);
    setUname(name);
    const storedUsername = localStorage.getItem("username");
    setUsername(storedUsername);
  
  }, []);
  // console.log("user value", Uname,email);
  
 
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="ml-auto mr-2 rounded-md border"
          variant="ghost"
          size="icon"
          title="User"
        >
          <User />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="mr-4 w-fit overflow-clip p-0">
        <div className="flex flex-col">
          <div className="bg-slate-200 p-4 dark:bg-slate-700">
            {/* <p className="text-header">{Uname}</p> */}
            <p className="text-header"> {`Hello, ${Uname}`}</p>
               <p className="text-small-font">{username}</p>          
               </div>
          <ul className="flex justify-between gap-2 px-4 py-2">
            <li>
              <Link href="/user-details/security">
                <Button title="Settings" variant="ghost" className="text-xs flex justify-center items-center">
                  {/* <Settings /> */}
                  {/* Change Password */}
                  <Settings size={30} />
                </Button>
              </Link>
            </li>
            <li className="hover:text-red-500">
              <SignOutButton />
            </li>
          </ul>
        </div>
      </PopoverContent>
    </Popover>
  );
}
 
interface PackageType {
  PackageName: string;
  PackageTitle: string;
}
 
function PackageSelect({ compact = false }: { compact?: boolean }) {
  const [packages, setPackages] = useState<PackageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { selectedPackage, setSelectedPackage } = usePackage();
  const [searchQuery, setSearchQuery] = useState("");
  
  const router = useRouter();
 
  // useEffect(() => {
  //   const fetchPackages = async () => {
  //     setIsLoading(true);
  //     try {
  //       const token = localStorage.getItem("IDToken");
  //       const response = await fetch(
  //         process.env.NEXT_PUBLIC_AUTH_DOMAIN +  "api/v1/access_control/user_packages?product_name=Web%20Performance",
  //         {
  //           method: "POST",
  //           headers: {
  //             "Content-Type": "application/json",
  //             Authorization: token || "",
  //           },
  //           body: JSON.stringify({
  //             "product_name": "Web Performance"
  //           }),
  //         }
  //       );
  //       const data = await response.json();
  //       if (Array.isArray(data)) {
  //         const hardcodedPackage = {
  //           PackageName: "com.vast_Vtest",
  //           PackageTitle: "com.vast_Vtest"
  //         };
 
  //         // Add the hardcoded package if not already present
  //         const uniquePackages = data.some(pkg => pkg.PackageName === hardcodedPackage.PackageName)
  //           ? data
  //           : [...data];
 
  //         setPackages(uniquePackages);
 
  //         if (!selectedPackage) {
  //           const savedPackage = localStorage.getItem("selectedPackage");
  //           const packageToSelect = savedPackage && uniquePackages.some(pkg => pkg.PackageName === savedPackage)
  //             ? savedPackage
  //             : uniquePackages[0]?.PackageName;
 
  //           if (packageToSelect) {
  //             setSelectedPackage(packageToSelect);
  //           }
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error fetching packages:", error);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };
 
  //   fetchPackages();
  // }, []);
 
   const pathname = usePathname();
 
  useEffect(() => {
    const fetchPackages = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("IDToken");
       
        // Get the current product name from the pathname
        let productName = "Webw eb Performance"; // fallback
        if (pathname.startsWith("/app")) {
          productName = "App Performance";
        } else if (pathname.startsWith("/web")) {
          productName = "Web Performance";
        } else if (pathname.startsWith("/brand-infringement")) {
          productName = "Brand Infringement";
        }
       
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}access_control/user_packages?product_name=${encodeURIComponent(productName)}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: token || "",
            },
            body: JSON.stringify({
              "product_name": productName
            }),
          }
        );
        const data = await response.json();
        if (Array.isArray(data)) {
          // Filter out packages with empty PackageName values
          const validPackages = data.filter((pkg: PackageType) => pkg.PackageName && pkg.PackageName.trim() !== "");
 
          setPackages(validPackages);
 
          if (!selectedPackage) {
            const savedPackage = localStorage.getItem("selectedPackage");
            const packageToSelect = savedPackage && validPackages.some(pkg => pkg.PackageName === savedPackage)
              ? savedPackage
              : validPackages[0]?.PackageName;
 
            if (packageToSelect) {
              setSelectedPackage(packageToSelect);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching packages:", error);
      } finally {
        setIsLoading(false);
      }
    };
 
    fetchPackages();
  }, [pathname, selectedPackage]);
  const items = packages.map((pkg) => ({
    title: pkg.PackageTitle || pkg.PackageName,
    value: pkg.PackageName,
  }));
 
//  const handlePackageChange = async (value: string) => {
//     console.log("Package changed to:", value);
//     setSelectedPackage(value);
//     localStorage.setItem("selectedPackage", value);
   
//     // Add a small delay to ensure the package context is updated
//     await new Promise(resolve => setTimeout(resolve, 100));
   
//     // After package change, fetch menu and navigate to first submenu
//     try {
//       const token = localStorage.getItem("IDToken");
//       if (!token) {
//         console.error("No token available");
//         return;
//       }
     
//       // Get the current product name from the pathname
//       let productName = "App Performance"; // fallback
//       let productRoute = "/app"; // fallback
//       if (pathname.startsWith("/app")) {
//         productName = "App Performance";
//         productRoute = "/app";
//       } else if (pathname.startsWith("/web")) {
//         productName = "Web Performance";
//         productRoute = "/web";
//       } else if (pathname.startsWith("/brand-infringement")) {
//         productName = "Brand Infringement";
//         productRoute = "/brand-infringement";
//       }
     
//       console.log("Fetching menu for product:", productName, "with package:", value);
     
//       // Fetch menu with the new package
//       const menuData = await fetchMenuWithPackage(token, productName, value);
//       console.log("Menu data received:", menuData);
     
//       const firstSubMenuRoute = findFirstSubMenuRoute(menuData);
//       console.log("First submenu route:", firstSubMenuRoute);
     
//              if (firstSubMenuRoute) {
//          // Construct the final route by combining product route with first submenu route
//          let finalRoute;
         
//                    // For App Performance, keep the route exactly as it comes from the API
//           if (productName === "Web Performance") {
//             // Use the route exactly as it comes from the API, without any modifications
//             finalRoute = firstSubMenuRoute;
//           } else {
//            // For other products, use the original logic
//            if (firstSubMenuRoute.startsWith("/")) {
//              // If the submenu route starts with /, combine directly
//              finalRoute = `${productRoute}${firstSubMenuRoute}`;
//            } else {
//              // If the submenu route doesn't start with /, add it
//              finalRoute = `${productRoute}/${firstSubMenuRoute}`;
//            }
//          }
       
//                  console.log("Final route to navigate:", finalRoute);
//          console.log("Product route:", productRoute);
//          console.log("First submenu route:", firstSubMenuRoute);
//          console.log("Current pathname:", pathname);
//          console.log("Product name:", productName);
         
//          // Navigate to the constructed route using Next.js router for better integration
//          console.log("About to navigate to:", finalRoute);
//          try {
//            router.push(finalRoute);
//            console.log("Navigation triggered with router");
//          } catch (error) {
//            console.log("Router navigation failed, using window.location.href");
//            window.location.href = finalRoute;
//          }
//       } else {
//         console.log("No first submenu route found");
//       }
//     } catch (error) {
//       console.error("Error fetching menu after package change:", error);
//     }
//   };
 

const handlePackageChange = async (value: string) => {
  console.log("Package changed to:", value);
  setSelectedPackage(value);
  localStorage.setItem("selectedPackage", value);

  await new Promise(resolve => setTimeout(resolve, 100));

  try {
    const token = localStorage.getItem("IDToken");
    if (!token) {
      console.error("No token available");
      return;
    }

    let productName = "App Performance";
    let productRoute = "/app";
    if (pathname.startsWith("/app")) {
      productName = "App Performance";
      productRoute = "/app";
    } else if (pathname.startsWith("/web")) {
      productName = "Web Performance";
      productRoute = "/web";
    } else if (pathname.startsWith("/brand-infringement")) {
      productName = "Brand Infringement";
      productRoute = "/brand-infringement";
    }

    console.log("Fetching menu for product:", productName, "with package:", value);

    const menuData = await fetchMenuWithPackage(token, productName, value);
    console.log("Menu data received:", menuData);

    // Use the new helper to decide whether to stay or redirect
    const targetRoute = getRouteAfterPackageChange(menuData, pathname);

    if (targetRoute) {
      let finalRoute = targetRoute;

      // Apply your existing product route logic for non-Web Performance
      if (productName !== "Web Performance") {
        if (targetRoute.startsWith("/")) {
          finalRoute = `${productRoute}${targetRoute}`;
        } else {
          finalRoute = `${productRoute}/${targetRoute}`;
        }
      }

      console.log("Final route to navigate:", finalRoute);

      try {
        router.push(finalRoute);
        console.log("Navigation triggered with router");
      } catch (error) {
        console.log("Router navigation failed, using window.location.href");
        window.location.href = finalRoute;
      }
    } else {
      console.log("No valid route found in menu");
    }
  } catch (error) {
    console.error("Error fetching menu after package change:", error);
  }
};

  const selectedPackageTitle = packages.find(pkg => pkg.PackageName === selectedPackage)?.PackageTitle || selectedPackage;
 
  return (
    <MFSingleSelect
      items={items}
      placeholder={isLoading ? "Loading..." : "Select Package"}
      className="max-w-40 h-9"
      value={selectedPackage}
      onValueChange={handlePackageChange}
      searchQuery={searchQuery}
      setSearchQuery={setSearchQuery}
    />
  );
}
 
function KebabMenu() {
  // Simulated API response (replace with actual API call in production)
  const [availableProducts, setAvailableProducts] = useState<Array<{
    icon: string;
    label: string;
    route: string;
    name?: string;
    redirect_link?: string;
  }>>([]);
  const [moreProducts, setMoreProducts] = useState<Array<{
    icon: string;
    label: string;
    route?: string;
    name?: string;
    redirect_link?: string;
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMenuLoading, setIsMenuLoading] = useState(false);
  const [selectedProductIdx, setSelectedProductIdx] = useState<number>(0); // Track selected product index
  const router = useRouter();
  const pathname = usePathname();
 
  // Function to fetch menu data and redirect with first submenu route
  const fetchMenuAndRedirect = async (productRoute: string, productName: string) => {
    setIsMenuLoading(true);
    try {
      const token = localStorage.getItem("IDToken");
      
      console.log("Calling menu API with product name:", productName);
      
      // Call the menu API with the product name in the POST body
      // const menuResponse = await fetch(
      //   process.env.NEXT_PUBLIC_AUTH_DOMAIN + "api/v1/access_control/menus",
      //   {
      //     method: "POST",
      //     headers: {
      //       Authorization: token || "",
      //       "Content-Type": "application/json",
      //     },
      //     body: JSON.stringify({ product_name: productName }),
      //   }
      // );
      
      // if (!menuResponse.ok) {
      //   throw new Error(`Menu API failed with status: ${menuResponse.status}`);
      // }
      
      const menuData = await fetchMenuWithPackage(token || "", productName);
      console.log("Menu API response:", menuData);
 
      let firstSubMenuRoute = findFirstSubMenuRoute(menuData) || "";
      console.log("First submenu route found:", firstSubMenuRoute);
      
      // Remove the first segment if it starts with a slash and has more than one segment
      // if (firstSubMenuRoute.startsWith("/")) {
      //   const parts = firstSubMenuRoute.split("/");
      //   if (parts.length > 2) {
      //     // parts[0] is '', parts[1] is the first segment to remove
      //     firstSubMenuRoute = "/" + (2).join("/");
      //   }
      // }

      const finalRoute = firstSubMenuRoute
        ? `${productRoute}${firstSubMenuRoute.startsWith("/") ? "" : "/"}${firstSubMenuRoute}`
        : productRoute;
      
      // console.log("Final route to navigate:", finalRoute);
      
        //router.push("/web-analytics/dashboard/overall-summary");
      window.location.href =`${finalRoute}`
    } catch (error) {
      console.error("Error fetching menu data:", error);
      // fallback to just the product route if menu API fails
      router.push(productRoute);
    } finally {
      setIsMenuLoading(false);
    }
  };
 
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const token = localStorage.getItem("IDToken");
        // Replace with your actual API endpoint
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.PRODUCT}?config=true`,
          {
            method: "GET",
            headers: {
              Authorization: token || "",
            },
          }
        );
        const data = await response.json();
        // Expecting data to have available_products and more_products arrays
        setAvailableProducts(Array.isArray(data.available_products) ? data.available_products : []);
        setMoreProducts(Array.isArray(data.more_products) ? data.more_products : []);
        setSelectedProductIdx(0); // Always select the first product after fetch
      } catch (error) {
        setAvailableProducts([]);
        setMoreProducts([]);
        setSelectedProductIdx(0);
      } finally {
        setIsLoading(false);
      }
    };
    fetchProducts();
  }, []);
 
  useEffect(() => {
    // Find the index of the product whose route matches the current pathname
    const idx = availableProducts.findIndex((app) =>
      pathname.startsWith(app.route)
    );
    if (idx !== -1 && idx !== selectedProductIdx) {
      setSelectedProductIdx(idx);
    }
    // Optionally, if you have a context for selected product, update it here too
  }, [pathname, availableProducts]);
 
  // Helper to render SVG from string
  const renderSVG = (svgString: string) => (
    <span
      className="mb-1"
      dangerouslySetInnerHTML={{ __html: svgString }}
    />
  );


   const fetchPackages1 = async (productName: string) => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem("IDToken");
      if (!token) {
        console.error("No token available");
        return;
      }
     
      // Get the product name from the available products
      const product = availableProducts.find(p => p.name === productName || p.label === productName);
      if (!product) {
        console.error("Product not found:", productName);
        return;
      }
     
      // Use the new utility function that fetches package name first
      const menuData = await fetchMenuWithPackage(token, product.label);
      const firstSubMenuRoute = findFirstSubMenuRoute(menuData) || "";
     
      const productRoute = product.route || "/app-analytics/dashboard/overall-summary";
     
      const finalRoute = firstSubMenuRoute
        ? `${productRoute}${firstSubMenuRoute.startsWith("/") ? "" : "/"}${firstSubMenuRoute}`
        : productRoute;
     
      console.log("Final route to navigate:", finalRoute);
      window.location.href = finalRoute;
    } catch (error) {
      console.error("Error fetching packages:", error);
    } finally {
      setIsLoading(false);
    }
  };
 
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className=" rounded-md border"
          variant="ghost"
          size="icon"
          title="Select Product"
        >
          <PackageSearch />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[320px] p-4 bg-white dark:bg-gray-900 rounded-2xl shadow-lg">
        {/* Section 1: Subscribed/Available Products */}
        <div>
          <div className="text-xs font-semibold text-black dark:text-white mb-2 px-1">
            {isLoading ? "Loading..." : "✅ Your Subscriptions"}
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            {availableProducts.map((app, idx) => {
              const isSelected = idx === selectedProductIdx;
              return (
                <div
                  key={idx}
                  className={`flex flex-col items-center justify-center p-3 rounded-xl transition-colors ${
                    isSelected
                      ? 'bg-blue-100 dark:bg-secondary-900 border-2 border-secondary cursor-not-allowed opacity-70' // Highlighted and disabled
                      : 'hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer'
                  } ${isMenuLoading ? 'opacity-50' : ''}`}
                  style={isSelected ? { pointerEvents: 'none' } : {}}
                  onClick={async () => {
                    if (!isSelected && app.route && !isMenuLoading) {
                      setSelectedProductIdx(idx); // Highlight and disable the clicked product
                      // Use the product name if available, otherwise use the label as fallback
                      const productName = app.name || app.label;
                      await fetchPackages1(productName);
                    }
                  }}
                >
                  {renderSVG(app.icon)}
                  <span className="text-xs text-center mt-1">{app.label}</span>
                </div>
              );
            })}
          </div>
        </div>
        {/* Divider */}
        <div className="border-t border-gray-200 dark:border-gray-700 my-2" />
        {/* Section 2: More Products */}
        <div>
          <div className="text-xs font-semibold text-black dark:text-white mb-2 px-1">
            {isLoading ? "Loading..." : "➕ Available Add-ons"}
          </div>
          <div className="grid grid-cols-2 gap-4">
            {moreProducts.map((app, idx) => (
              <div
                key={idx}
                className={`flex flex-col items-center justify-center p-3 rounded-xl ${app.redirect_link ? 'hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer' : 'opacity-60 cursor-not-allowed'} transition-colors ${isMenuLoading ? 'opacity-50' : ''}`}
                onClick={() => {
                  if (app.redirect_link) {
                    handleMoreProductClick(app);
                  }
                }}
              >
                {renderSVG(app.icon)}
                <span className="text-xs text-center mt-1">{app.label}</span>
              </div>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}