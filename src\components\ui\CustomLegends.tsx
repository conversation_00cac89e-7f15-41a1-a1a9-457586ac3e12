"use client"
import React from "react";
import { useFullscreen } from "@/hooks/use-fullscreen";

  const CustomLegendContent = ({ labels, colors }: { labels: string[], colors: string[] }) => {
      const isFullscreen = useFullscreen();   
  return (
         <div className={`flex flex-wrap space-x-4 justify-start  sm:justify-start md:justify-start lg:justify-center ${isFullscreen ? 'gap-6' : 'gap-2'}`}>
        {labels?.map((labelText: string, index) => (
          <div className="flex items-center space-x-2" key={index}>
               <span style={{ backgroundColor: colors[index] }} className={`rounded-full ${isFullscreen ? 'w-5 h-5' : 'w-3 h-3'}`}></span>
               <span className={`${isFullscreen ? 'text-base' : 'text-xs sm:text-xs md:text-xs'}`}>{labelText}</span>
          </div>
        ))}
      </div>
    );
  };
export default CustomLegendContent;