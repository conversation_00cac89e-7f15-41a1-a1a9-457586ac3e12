"use client"

import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
   ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart"
import HeaderRow from "../mf/HeaderRow"

export const description = "An area chart with gradient fill"
interface chartData {
  label: string;
  [key: string]: string | number;
}

interface chartconfig {
  [key: string]: {
    label: string;
    color: string;
  };
}
interface AreagradientChart {
  chartData?: chartData[];
  chartConfig?: chartconfig;
  XaxisLine?:boolean;
  Xdatakey?:string;
  CartesianGridVertical?:boolean;
  isSelect?:boolean;
  isRadioButton?:boolean;
  title?:string;
   handleExport?: () => void;
  onExpand: () => void;
  onExport?: (s: string, title: string, index: number) => void;
    handleFrequencyChange?: (value: string) => void;
    selectoptions?:string[];
  selecteedFrequency?:string;
  placeholder?:string;

}

const ChartAreaGradient: React.FC<AreagradientChart> = ({
chartData,
chartConfig,
Xdatakey=false,
CartesianGridVertical=false,
XaxisLine=false,
  isSelect,
  isRadioButton,
  title,
  onExpand,
  handleExport,
  handleFrequencyChange,
  onExport,
  placeholder,
  selectoptions,
  selecteedFrequency,

})=>
{
    return (
    <Card className="border-none">
         <HeaderRow
                title={title}
                onExpand={onExpand}
                handleExport={handleExport}
                isRadioButton={isRadioButton}
                isSelect={isSelect}
                onExport={onExport}
                selectoptions={selectoptions}
                handleFrequencyChange={handleFrequencyChange}
                selectedFrequency={selecteedFrequency}
                placeholder={placeholder||""}
              />
      <CardHeader>
        {/* <CardTitle>Area Chart - Gradient</CardTitle>
        <CardDescription>
          Showing total visitors for the last 6 months
        </CardDescription> */}
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 22,
              right: 22,
              
            }}
          >
            <CartesianGrid vertical={CartesianGridVertical} />
            <XAxis
              dataKey={Xdatakey}
              tickLine={false}
              axisLine={XaxisLine}
              tickMargin={8}
              tickFormatter={(value) => (value || '').toString().slice(0, 3)}  />
               <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickCount={3}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
             <ChartLegend content={<ChartLegendContent />} />
            
            <defs>
               {/*  y2 means the gradient runs vertically (from top to bottom) x2 for horizontal. */}

              <linearGradient id="fillDesktop" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-desktop)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-desktop)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillMobile" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-mobile)"
                  stopOpacity={0.6}
                />
              </linearGradient>
            </defs>
            <Area
              dataKey="mobile"
              type="natural"
              fill="url(#fillMobile)"
              fillOpacity={0.7}
              stroke="var(--color-mobile)"
              stackId="a"
            />
            <Area
              dataKey="desktop"
              type="natural"
              fill="url(#fillDesktop)"
              fillOpacity={0.7}
              stroke="var(--color-desktop)"
              stackId="a"
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
export default ChartAreaGradient;