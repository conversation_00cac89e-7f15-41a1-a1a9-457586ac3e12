
import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
});

export const useQuartileProgression = (params: any, enabled = true) =>
  useAppQuery(
    ['quartileProgression', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.QUARTILE_PROGRESSION}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  export const useFullFunnelJourney = (params: any, enabled = true) =>
  useAppQuery(
    ['fullFunnelJourney', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.FULL_FUNNEL_JOURNEY}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  