"use client"

import { useToast } from "@/hooks/use-toast"
import { useUserListQuery, useProductListQuery, useRoleListQuery, useListUserQuery, usePackageListQuery, useAddUserRoleMutation, useEditUserRoleMutation, useDeleteUserProductMutation } from '../queries/product-mapping'
import ToastContent from "@/components/mf/ToastContent"
import ResizableTable, { type Column } from "@/components/mf/ReportingToolTable"
import { useState, useEffect, useCallback, useMemo } from "react"
import DeleteDialog from "@/components/ui/deletedialog"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import DynamicFormDialog from "@/components/mf/RowAddingDialog"
import ReusableDialog from "@/components/ui/dialogUserMangement"
import { EditUserDialog } from "./EditUserDialog"
import { useQueryClient } from "@tanstack/react-query";
import { log } from "console"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Pencil } from "lucide-react"
import { TableBody, TableCell, TableRow } from "@/components/ui/table"
import MultiValueShowMore from '@/components/mf/MultiValueShowMore';
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

interface TableDataType {
  _id: string;
  Name: string;
  Email: string;
  Products: string;
  Role: string;
  Packages: string;
  EmbeddedLabel: string;
  URL: string;
  [key: string]: string;
}

interface ToastData {
  type: "success" | "error";
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
}

interface RoleResponse {
  Role: string;
  Alias: string;
  _id: string;
}

interface SelectOption {
  value: string;
  label: string;
}

// Update product query type to match actual response
type ProductQueryData = SelectOption[];

const editFormSchema = z.object({
  _id: z.string().optional(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email"),
  Products: z.string().min(1, "Product is required"),
  Role: z.string().min(1, "Role is required"),
  Packages: z.array(z.string()).optional(),
});

interface RowState {
  productSelected: boolean;
  roleSelected: boolean;
  packageSelected: boolean;
  isLoading: boolean;
}

interface RowData {
  product: string;
  roleData: any[];
  packageData: any[];
}

interface Field {
  name: string;
  label: string;
  type: "input" | "select" | "delivery";
  options?: SelectOption[];
  value?: string | string[];
  onChange?: (value: string | string[]) => void | Promise<void>;
  loading?: boolean;
  disabled?: boolean;
}
type EditFormValues = z.infer<typeof editFormSchema>;

const Product_Mapping = () => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [rowToDelete, setRowToDelete] = useState<string | null>(null)
  const [addUserOpen, setAddUserOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [toastData, setToastData] = useState<ToastData | null>(null)
  const [selectedProduct, setSelectedProduct] = useState("")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<EditFormValues>({
_id: "",
name: "",
email: "",
Products: "",
Role: "",
Packages: []
});
  const [dialogMode, setDialogMode] = useState<"create" | "edit" | "view">("create")
  const [rowData, setRowData] = useState<Record<number, { Products: string }>>({ 0: { Products: "" } })
  const [currentRowIndex, setCurrentRowIndex] = useState<number | null>(null)
  const [selectedProducts, setSelectedProducts] = useState<Record<number, string>>({})
  const [rowQueryData, setRowQueryData] = useState<Record<number, { roles: any[], packages: any[] }>>({})
  const [isLoadingRolePackage, setIsLoadingRolePackage] = useState(false)
  const [searchText, setSearchText] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [debouncedEmail, setDebouncedEmail] = useState('');
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editUserData, setEditUserData] = useState<any>(null)
  const [pendingPrefill, setPendingPrefill] = useState(false);
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    product: "",
    role: "",
    packages: [] as string[]
  });

  // Query hooks
  const userListQuery = useUserListQuery({
    email: debouncedEmail,
    page,
    limit,
  });
  const { data: userList, refetch, isLoading } = useUserListQuery({
    email: debouncedEmail,
    page,
    limit,
  });
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedEmail(searchText);
    }, 500);

    return () => clearTimeout(handler);
  }, [searchText]);

  const listUserQuery = useListUserQuery()
  const productListQuery = useProductListQuery()
  const roleListQuery = useRoleListQuery(selectedProduct, !!selectedProduct)
  const packageListQuery = usePackageListQuery(selectedProduct, !!selectedProduct)
  const addUserRoleMutation = useAddUserRoleMutation()
  const editUserRoleMutation = useEditUserRoleMutation()
  const deleteUserMutation = useDeleteUserProductMutation()


  const tableDatas = useMemo(() => {
    return (userList?.data ?? []).flatMap((user) => {
      if (!user.Roles || user.Roles.length === 0) {
              return [{
        _id: String(user._id || '-'),
        Name: user.Name || '-',
        Email: user.Email || '-',
        Products: '-',
        Role: '-',
        Packages: '-',
        EmbeddedLabel: '-',
        URL: '-'
      }];
      }

      return user.Roles.map((role) => ({
        _id: String(user._id || '-'),
        Name: user.Name || '-',
        Email: user.Email || '-',
        Products: role.Product || '-',
        Role: role.Role || '-',
        Packages:
          Array.isArray(role.Packages)
            ? role.Packages.length > 0 && !role.Packages.every(p => p === '-' || p === '')
              ? role.Packages.filter(p => p !== '-' && p !== '').join(', ')
              : '-'
            : role.Packages && role.Packages !== '-'
              ? role.Packages
              : '-',
        // EmbeddedLabel and URL are not needed and have been removed
      }));
    });
  }, [userList]);

 // console.log("packages", tableDatas);





  // Effect to store role and package data per row
  useEffect(() => {
    if (currentRowIndex !== null && selectedProducts[currentRowIndex]) {
      if (roleListQuery.data || packageListQuery.data) {
        setRowQueryData(prev => ({
          ...prev,
          [currentRowIndex]: {
            roles: roleListQuery.data || [],
            packages: packageListQuery.data || []
          }
        }));
      }
    }
  }, [currentRowIndex, roleListQuery.data, packageListQuery.data]);

  const handleDelete = (item: TableDataType) => {
    setRowToDelete(item._id)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!rowToDelete) return

    try {
      const result = await deleteUserMutation.mutateAsync(rowToDelete)
      setToastData({
        type: "success",
        title: "Success",
        description: result.message,
        variant: "default"
      })
      setDeleteDialogOpen(false)
      setRowToDelete(null)
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: error.response?.data?.message || error.message
      })
    }
  }

  const editForm = useForm<z.infer<typeof editFormSchema>>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      _id: "",
      name: "",
      email: "",
      Products: "",
      Role: "",
      Packages: []
    }
  });

  const { watch, register, setValue } = editForm;
  const productValue = watch("Products");
  const roleValue = watch("Role");
  const packageValue = watch("Packages");

  const handleEdit = (item: TableDataType) => {
    console.log();
    
    // Convert TableDataType to EditFormValues format
    const editFormData: EditFormValues = {
      _id: item._id,
      name: item.Name,
      email: item.Email,
      Products: item.Products,
      Role: item.Role, // This will be the alias, we'll need to find the ID later
      Packages: item.Packages && item.Packages !== '-' 
        ? item.Packages.split(',').map((pkg: string) => pkg.trim())
        : []
    };
    
    setSelectedUser(editFormData);
    productListQuery.refetch();
    setEditDialogOpen(true);
  };

useEffect(() => {
  if (selectedUser?.Products) {
    const product = selectedUser.Products;
    setValue("Products", product); // from react-hook-form

    setSelectedProduct(product);
    setIsLoadingRolePackage(true);

    Promise.all([
      roleListQuery.refetchWithRole(product),
      packageListQuery.refetchWithPackage(product),
    ])
      .then(([rolesRes, packagesRes]) => {
        const roles = rolesRes?.data || [];
        const packages = packagesRes?.data || [];

        setRowQueryData((prev) => ({
          ...prev,
          [0]: { roles, packages }, // you can replace `0` with dynamic index
        }));
      })
      .finally(() => setIsLoadingRolePackage(false));
  }
}, [selectedUser?.Products, setValue]);


const handleProductChanges = async (
  value: string,
  index: number,
  formSetValue: (field: string, value: any) => void
) => {
  const prevProduct = watch("Products");
  if (prevProduct === value) return;

  setIsLoadingRolePackage(true);
  setPendingPrefill(true);

  try {
    formSetValue("Products", value);
    formSetValue("Role", "");
    formSetValue("Packages", []);

    const [roles, packages] = await Promise.all([
      roleListQuery.refetchWithRole(value),
      packageListQuery.refetchWithPackage(value)
    ]);

    setRowQueryData((prev) => ({
      ...prev,
      [index]: { roles, packages }
    }));
  } catch (e) {
    console.error("❌ Error during product change:", e);
  } finally {
    setIsLoadingRolePackage(false);
  }
};



 const handleEditSubmit = async (data: Record<string, any>) => {
  setIsSubmitting(true);

  try {
    if (!selectedUser) {
      throw new Error("No user selected for editing");
    }

    // Get the roleId from the selected value
    const roleId = data.role;

    if (!roleId) {
      throw new Error("Selected role is missing or invalid");
    }

    const payload = {
      email: data.email,
      add_role: [
        {
          Role: roleId, // Already an ID, no need to convert
          Product: data.product,
          Packages: Array.isArray(data.packages) ? data.packages : [],
        }
      ]
    };

    await editUserRoleMutation.mutateAsync(payload);
    await userListQuery.refetch();

    setToastData({
      type: "success",
      title: "Success",
      description: "User roles updated successfully",
      variant: "default",
    });

    setEditDialogOpen(false);

  } catch (error: any) {
    console.error("❌ Error in handleEditSubmit:", error);

    setToastData({
      type: "error",
      title: "Error",
      description:
        error?.response?.data?.message ||
        error.message ||
        "Failed to update user roles",
    });
  } finally {
    setIsSubmitting(false);
  }
};

  const userFieldConfig = {
    name: "user",
    label: "User",
    placeholder: "Select User",
    required: true,
    showOnlyForFirstRow: true,
    width: "w-full",
    disabled: true
  };

  // const productOptions = useMemo(() => {
  //   const options: SelectOption[] = [];

  //   if (productValue) {
  //     const existingLabel = productListQuery.data?.find(p => p.value === productValue)?.label;
  //     options.push({
  //       value: productValue,
  //       label: existingLabel || productValue
  //     });
  //   }

  //   if (productListQuery.data) {
  //     options.push(...productListQuery.data);
  //   }

  //   return options.filter((item, index, self) => index === self.findIndex(t => t.value === item.value));
  // }, [productListQuery.data, productValue]);

  // const roleOptions = useMemo(() => {
  //   const options: SelectOption[] = [];

  //   if (roleValue) {
  //     const existingLabel = roleListQuery.data?.find(r => r.value === roleValue)?.label;
  //     options.push({
  //       value: roleValue,
  //       label: existingLabel || roleValue
  //     });
  //   }

  //   if (roleListQuery.data) {
  //     options.push(...roleListQuery.data);
  //   }

  //   return options.filter((item, index, self) => index === self.findIndex(t => t.value === item.value));
  // }, [roleListQuery.data, roleValue]);

  // const packageOptions = useMemo(() => {
  //   const options: SelectOption[] = [];

  //   if (Array.isArray(packageValue)) {
  //     packageValue.forEach((val) => {
  //       const existingLabel = packageListQuery.data?.find(pkg => pkg.value === val)?.label;
  //       options.push({
  //         value: val,
  //         label: existingLabel || val
  //       });
  //     });
  //   }

  //   if (packageListQuery.data) {
  //     options.push(...packageListQuery.data);
  //   }

  //   // Remove duplicates by value
  //   return options.filter(
  //     (item, index, self) => index === self.findIndex(t => t.value === item.value)
  //   );
  // }, [packageListQuery.data, packageValue]);


  
  const handleProductChange = (value: string) => {
    console.log("🔵 Parent: Product changed to:", value);
    setSelectedProduct(value);
  };

  // Effect to handle product change side effects
  useEffect(() => {
    if (selectedProduct) {
      console.log("🔵 Effect: Updating form data for product:", selectedProduct);
      setFormData(prev => ({
        ...prev,
        product: selectedProduct,
        role: "",
        packages: []
      }));

      // Trigger refetch of roles and packages
      Promise.all([
        roleListQuery.refetch(),
        packageListQuery.refetch()
      ]).catch(error => {
        console.error("Failed to fetch roles/packages:", error);
      });
    }
  }, [selectedProduct]);

  useEffect(() => {
    if (watch("Products")) {
      setIsLoadingRolePackage(true);
      Promise.all([
        roleListQuery.refetch(),
        packageListQuery.refetch()
      ]).finally(() => setIsLoadingRolePackage(false));
    }
  }, [watch("Products")]);
  // Remove the separate useEffect for form reset - we're handling it in handleEdit
  useEffect(() => {
   
  }, [selectedProduct, roleListQuery, packageListQuery]);

  const handleView = (item: Record<string, string | number>) => {
    console.log("item", item);
    setDialogMode("view");
    setSelectedUser({
      _id: String(item._id || ""),
      name: String(item.Name || ""),
      email: String(item.Email || ""),
      Products: String(item.Products || ""),
      Role: String(item.Role || ""),
      Packages: String(item.Packages || "").split(',').map((pkg: string) => pkg.trim())
    });
    setDialogOpen(true)
  }


  const handleDynamicFieldChange = async (fieldName: string, value: string | string[], rowIndex: number, formSetValue: any) => {
    if (fieldName === "Products") {
      setCurrentRowIndex(rowIndex);

      // Update selected products
      setSelectedProducts(prev => {
        const newProducts = { ...prev };
        if (newProducts[rowIndex] !== value) {
          // Clear dependent fields
          formSetValue(`fields.${rowIndex}.Role`, "");
          formSetValue(`fields.${rowIndex}.Package`, []);

          // Update selected product
          newProducts[rowIndex] = value as string;

          // Fetch and store role and package data
          if (value) {
            setIsLoadingRolePackage(true);

            // Update selected product to trigger queries
            setSelectedProduct(value as string);

            // Wait for queries to complete
            Promise.all([
              roleListQuery.refetch(),
              packageListQuery.refetch()
            ]).then(([rolesResponse, packagesResponse]) => {
              // Store the data
              setRowQueryData(prev => ({
                ...prev,
                [rowIndex]: {
                  roles: rolesResponse.data || [],
                  packages: packagesResponse.data || []
                }
              }));
            }).catch(error => {
              console.error('Error fetching roles and packages:', error);
            }).finally(() => {
              setIsLoadingRolePackage(false);
            });
          }
        }
        return newProducts;
      });
    }
  };

  const getDynamicData = () => {
    const data: any = {
      Products: {},
      Role: {},
      Package: {}
    };

    // Gather all selected products except for the current row
    const selectedProductValues = Object.values(selectedProducts);

    Object.keys(rowData).forEach((rowIndex) => {
      const index = Number(rowIndex);
      const currentProduct = selectedProducts[index];

      // Exclude products already selected in other rows
      const excludedProducts = selectedProductValues.filter((p, i) => i !== index);
      data.Products[index] = (productListQuery.data?.filter(p => !excludedProducts.includes(p.value)) || []).map(p => ({
        value: p.value,
        label: p.label
      }));

      // Set role and package options based on selected product
      if (currentProduct) {
        // Use stored query data if available
        const storedData = rowQueryData[index];
        if (storedData) {
          data.Role[index] = storedData.roles?.map(r => ({
            value: r.value,
            label: r.label
          })) || [];
          data.Package[index] = storedData.packages?.map(p => ({
            value: p.value,
            label: p.label
          })) || [];
        } else {
          // If no stored data, use current query data
          data.Role[index] = roleListQuery.data?.map(r => ({
            value: r.value,
            label: r.label
          })) || [];
          data.Package[index] = packageListQuery.data?.map(p => ({
            value: p.value,
            label: p.label
          })) || [];
        }
      } else {
        // No product selected, empty arrays
        data.Role[index] = [];
        data.Package[index] = [];
      }
    });

    // Add data for new row
    const newRowIndex = Object.keys(rowData).length;
    // Exclude all selected products for the new row
    data.Products[newRowIndex] = (productListQuery.data?.filter(p => !selectedProductValues.includes(p.value)) || []).map(p => ({
      value: p.value,
      label: p.label
    }));
    data.Role[newRowIndex] = [];
    data.Package[newRowIndex] = [];

    return data;
  };

  const handleAddField = () => {
    const newIndex = Object.keys(rowData).length;

    setRowData(prev => ({
      ...prev,
      [newIndex]: { Products: "" }
    }));
    productListQuery.refetch();

  };
  const handleAddProductMapping = () => {
    listUserQuery.refetch();
    productListQuery.refetch();
    setAddUserOpen(true);
  }


  const totalPages = userList?.total_pages ?? 1;
  const handleSubmit = async (formData: any) => {
    try {
      const result = await addUserRoleMutation.mutateAsync({
        email: formData.user,
        add_role: formData.fields.map((field: any) => ({
          Role: field.Role,
          Package: Array.isArray(field.Package) ? field.Package : [field.Package],
          Product: field.Products,
          EmbeddedMenus: {
            package_name: {
              power: field.EmbeddedLabel || ""
            }
          }
        }))
      });

      await userListQuery.refetch();

      // ✅ Clear local UI state
      setSelectedProducts({});
      setRowData({ 0: { Products: "" } });
      setSelectedProduct("");

      setToastData({
        type: "success",
        title: "Success",
        description: result.message,
        variant: "default"
      });

      setAddUserOpen(false);
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: error.response?.data?.message || error.message
      });
    }
  };
  
  const getDialogFields = () => {
    if (dialogMode === "view") {
      return [
        {
          name: "name",
          label: "User Name",
          type: "input" as const,
          disabled: true
        },
        {
          name: "email",
          label: "Email",
          type: "input" as const,
          disabled: true
        },
        {
          name: "product",
          label: "Product",
          type: "input" as const,
          disabled: true
        },
        {
          name: "role",
          label: "Role",
          type: "input" as const,
          disabled: true
        },
        {
          name: "package",
          label: "Package",
          type: "input" as const,
          disabled: true
        }
      ];
    }

    return [];
  };


  const columns: Column<TableDataType>[] = [
    {
      title: "Name",
      key: "Name",
      render: (data: TableDataType) => <div className="text-left whitespace-normal break-words">{data.Name || "-"}</div>,
    },
    {
      title: "Email",
      key: "Email",
      render: (data: TableDataType) => <div className="text-left whitespace-normal break-words">{data.Email || "-"}</div>,
    },
    {
      title: "Products",
      key: "Products",
      render: (data: TableDataType) => <div className="text-left whitespace-normal break-words">{data.Products || "-"}</div>,
    },
    {
      title: "Role",
      key: "Role",
      render: (data: TableDataType) => <div className="text-left whitespace-normal break-words">{data.Role || "-"}</div>,
    },
    {
      title: "Package",
      key: "Packages",
      render: (data: TableDataType) => (
        <div className="text-left whitespace-normal break-words relative overflow-visible">
          {data.Packages && data.Packages !== '-' ? (
            (() => {
              const packages = data.Packages.split(',').map(pkg => pkg.trim()).filter(pkg => pkg && pkg !== '-');
              return (
                <MultiValueShowMore values={packages} showSearch={true} />
              );
            })()
          ) : (
            data.Packages
          )}
        </div>
      ),
    },
  ]

  const validationConfig = {
    userField: {
      name: "user",
      message: "Please select a user"
    },
    dynamicFields: {
      name: "fields",
      minItems: 1,
      message: "At least one field is required",
      fields: {
        Products: { message: "Select a product" },
        Role: { message: "Select a role" },
        Package: { message: "Select a package" },
      }
    }
  };

  const onDialogSubmit = (data: Record<string, string>) => {
    if (dialogMode === "edit") {
      return handleEditSubmit(data);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}

      <div className="p-3">
        <div className="min-h-[500px] relative">
          <ResizableTable
            columns={columns}
            data={tableDatas}
            isPaginated={true}
            isLoading={isLoading}
            headerColor="#DCDCDC"
            isSearchable={true}
            isUserTable={false}
            isDelete={true}
            onDelete={(item: Record<string, string | number>) => handleDelete(item as unknown as TableDataType)}
            isEdit={true}
            isView={true}
            setSearchTerm={setSearchText}
            SearchTerm={searchText}
            onView={handleView}
            onEdit={(item: Record<string, string | number>) => handleEdit(item as unknown as TableDataType)}
            isMappingPackage={true}
            handleAddProductMapping={handleAddProductMapping}
            //height={400}
            isTableDownload={false}
            onLimitChange={(newLimit: number) => {
              setLimit(newLimit);
            }}
            onPageChangeP={(newPage: number) => {
              setPage(newPage);
            }}
            totalPages={totalPages}
          />
        </div>
      </div>


      <DynamicFormDialog
        open={addUserOpen}
        onOpenChange={setAddUserOpen}
        title="Add Product Mapping"
        userFieldConfig={{
          name: "user",
          label: "User",
          placeholder: "Select User",
          required: true,
          showOnlyForFirstRow: true,
          width: "w-full",
          disabled: false
        }}
        dynamicFieldsConfig={[
          {
            name: "Products",
            label: "Products",
            placeholder: "Select Products",
            required: true,
            width: "w-full",
            disabled: false,
            dependsOn: "user"
          },
          {
            name: "Role",
            label: "Role",
            placeholder: "Select Role",
            required: true,
            width: "w-full",
            disabled: false,
            dependsOn: "Products"
          },
          {
            name: "Package",
            label: "Package",
            placeholder: "Select Package",
            required: true,
            width: "w-full",
            disabled: false,
            dependsOn: "Products",
            isMultiple: true
          },
          // {
          //   name: "EmbeddedLabel",
          //   label: "Embedded Label",
          //   placeholder: "Enter embedded label",
          //   required: true,
          //   width: "w-full",
          //   disabled: false,
          //   type: "input"
          // },
          // {
          //   name: "URL",
          //   label: "URL",
          //   placeholder: "Enter URL",
          //   required: true,
          //   width: "w-full",
          //   disabled: false,
          //   type: "input"
          // },
        ]}
        validationConfig={validationConfig}
        staticData={{
          user: Array.from(new Set(listUserQuery.data?.user_emails || [])).map((email: string) => ({
            value: email,
            label: email,
          })) || []
        }}
        dynamicData={getDynamicData()}
        onFieldChange={handleDynamicFieldChange}
        handleAddField={handleAddField}
        loadingStates={{}}
        globalLoading={listUserQuery.isLoading || productListQuery.isLoading}
        onSubmit={handleSubmit}
        labels={{
          cancel: "Cancel",
          submit: "Submit",
          loading: "Loading...",
          noDataFound: "No Data Found",
          actions: "Actions"
        }}
      />


      <ReusableDialog
        open={dialogOpen}
        setOpen={(open) => {
          setDialogOpen(open);
          if (!open) {
            //resetForm();
          }
        }}
        fields={getDialogFields()}
        DialogTitles={dialogMode === "edit" ? "Edit User" : "User Details"}
        defaultValues={selectedUser}
        dialogMode={dialogMode}
        isViewMode={dialogMode === "view"}
        onSubmit={onDialogSubmit}
        isSubmitting={isSubmitting}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Delete User"
        description="Are you sure you want to delete?"
      />

     
      <EditUserDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        selectedUser={selectedUser}
        productList={productListQuery.data || []}
        roleList={roleListQuery.data || []}
        packageList={packageListQuery.data || []}
        onProductChange={handleProductChange}
        onSubmit={handleEditSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  )
}

export default Product_Mapping