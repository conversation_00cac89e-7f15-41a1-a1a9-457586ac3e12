"use client";
import React, { useState } from "react";
import { useTheme } from "@/components/mf/theme-context";
import MFWebFraudAsideMenu from "@/components/mf/MFWebFraudAsideMenu";
import { MFTopBar } from "@/components/mf";
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from '@/lib/queryClient';
import { usePathname } from 'next/navigation';
import { SessionCheck } from "@/components/mf/SessionCheck";
export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isDarkMode } = useTheme();
  const [IsHover, setIsHover] = useState(false);
  const [Toggle, setToggle] = useState(false);
  const pathname = usePathname();

  const currentTheme = isDarkMode ? "dark" : "light";

  return (
    <div className="flex h-screen flex-col w-full dark:bg-black">
      {/* Header */}
      <MFTopBar
        isExpanded={Toggle || IsHover}
        onToggle={() => setToggle(!Toggle)}
        isCalender={true}
        //Dashboardtitle={Dashboardtitle}
      />

      {/* Main content area */}
      <div className="flex  h-full flex-1 overflow-hidden">
        <MFWebFraudAsideMenu
          isExpanded={Toggle || IsHover}
          onHover={setIsHover}
          theme={currentTheme}
        />
        <SessionCheck>
        <QueryClientProvider client={queryClient}>
          <div className="flex-1 overflow-auto bg-gray-100 dark:bg-background">
            {children}
          </div>
        </QueryClientProvider>
        </SessionCheck>
      </div>
    </div>
  );
}
