'use client';
import { useState, useEffect } from "react";
import { apiCall, useAppQuery } from '@/app/(main)/(web)/queries/useAppQuery';
import { downloadFileFromUrl } from "@/lib/utils"; // your file downloader

type UseExportCsvProps = {
  exportParams: Record<string, any>;
  queryParams?: Record<string, any>;
  exportType: string | null;
  setExportType: (type: string | null) => void;
  isExporting: boolean;
  setIsExporting: (val: boolean) => void;
  endpointMap: Record<string, string>; // key is exportType (fraud/device/source), value is endpoint path
  baseUrlMap: Record<string, string>;  // key is exportType, value is baseURL like process.env.XXX
};

export const useExportCsv = ({
  exportParams,
  queryParams = {},
  exportType,
  setExportType,
  isExporting,
  setIsExporting,
  endpointMap,
  baseUrlMap,
}: UseExportCsvProps) => {
  useEffect(() => {
    if (!isExporting || !exportType) return;

    const exportPayload = {
      ...exportParams,
      ...queryParams,
      export: true,
    };

    const endpoint = endpointMap[exportType];
    const baseUrl = baseUrlMap[exportType];

    if (!endpoint || !baseUrl) {
      console.error("Invalid export type or missing URL configuration");
      setIsExporting(false);
      return;
    }

    const fetchExport = async () => {
      try {
        const res = await apiCall({
          url: `${baseUrl}${endpoint}`,
          method: "POST",
          data: exportPayload,
        });

        if (res?.download_url) {
          downloadFileFromUrl(res.download_url, res.filename || "export.csv");
        } else {
          console.error("No download URL returned.");
        }
      } catch (err) {
        console.error("Export failed:", err);
      } finally {
        setIsExporting(false);
        setExportType(null);
      }
    };

    fetchExport();
  }, [isExporting, exportType]);
};
