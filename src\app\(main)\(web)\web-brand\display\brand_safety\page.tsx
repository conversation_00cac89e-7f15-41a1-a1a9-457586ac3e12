'use client'
import React from 'react'
import SummaryCardGroup from '../common_module/page';
import ResizableTable from '@/components/mf/ReportingToolTable';
import { useState } from 'react';
import { Filter } from "@/components/mf/Filters";
import {useChannelsFilter,useCampaignsFilter,usePublishersFilter} from '../../FilterApi';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from  '../Filters/buildFilters';

import { useWastageFilters } from '../../Filters/useFilters'


function Brand_safety() {
     const[sercterm,setSearchTerm] = useState('');
    const { selectedPackage } = usePackage();
    const { startDate, endDate } = useDateRange();
             
               const params = {
           package_name: selectedPackage,
           start_date: startDate,
           end_date: endDate,
         };
         const [query, setQuery] = useState({
           publishers: ["all"],
           campaigns: ["all"],
           channels: ["all"],
           fraudcategory:["all"],
           subfraudcategory:["all"],
           creative_id:["all"],
           publisher_ids:["all"],
         });
         const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
          const filter = useWastageFilters(params, query);
         const UnsafeHeader =[     
         { title: " Placement ID", key: "Video ID/ Placement ID"},
        //  { title: "Content Name", key: "Content Name" },
         { title: "Unsafe Category", key: "Unsafe Category" },
         { title: "Impressions(Descending Order)", key:"Impressions(Descending Order)" },
         ]
           const SafeHeader =[     
         { title: "Placement ID", key:"Placement ID"},
        //  { title: "Content Name", key: "Content Name" },
         { title: "Category", key: "Category" },
         { title: "Impressions Served(Descending Order)", key:"Impressions Served(Descending Order)" },
         ]
           const BrandHeader =[     
         { title: "Placement ID", key: "Placement ID" },
        //  { title: "Content Name", key:"Content Name"},
         { title: "Category", key: "Category" },
         { title: "Impressions Served(Descending Order)", key:"Impressions Served(Descending Order)" },
         ]

          const UnsafereportData =[
    {
        "Video ID/ Placement ID": "Video1",
        // "Content Name": "Showname",
        "Unsafe Category": "Debatable",
        "Impressions(Descending Order)": "7,323",
    },
     {
        "Video ID/ Placement ID": "Video2",
        // "Content Name": "Showname",
        "Unsafe Category": "Terroroism",
        "Impressions(Descending Order)": "6,723",
    },
    {
        "Video ID/ Placement ID": "Video3",
        // "Content Name": "Showname",
        "Unsafe Category": "CategoryX",
        "Impressions(Descending Order)": "5,423",
    },
      {
        "Video ID/ Placement ID": "Video4",
        // "Content Name": "Showname",
        "Unsafe Category": "Hate Speech",
        "Impressions(Descending Order)": "3,423",
    },
     {
        "Video ID/ Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Unsafe Category": "CategoryABC",
        "Impressions(Descending Order)": "423",
    },
]
      const safereportData =[
    {
        "Placement ID": "Video1",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impressions Served(Descending Order)": "2,323",
    },
     {
        "Placement ID": "Video2",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impressions Served(Descending Order)": "71,23",
    },
    {
        "Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impressions Served(Descending Order)": "6423",
    },
      {
        "Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": " Safe",
        "Impressions Served(Descending Order)": "3,423",
    },
     {
        "Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impressions Served(Descending Order)": "423",
    },
]
    const BrandreportData =[
    {
        "Placement ID": "Video1",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "9,323",
    },
     {
        "Placement ID": "Video2",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "8,023",
    },
    {
        "Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "7,423",
    },
      {
        "Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "6,523",
    },
     {
        "Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "5,423",
    },
     {
        "Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "4,423",
    },
      {
        "Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "3,423",
    },
     {
        "Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impressions Served(Descending Order)": "423",
    },
]

const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
  return (
    <div className=" w-full grid grid-col gap-2 p-2 ">
       <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                    <Filter filter={filter} onChange={handleFilterChange} />
                    </div>
        <SummaryCardGroup params={params} query={query} />
        <div className="gap-1 w-full"> 
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Unsafe Placement ID</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={UnsafeHeader}
     data={UnsafereportData}
     isSearchable={true}
     isUserTable={false}
     height={410}
      row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
       <div className="gap-1 w-full"> 
     <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 safe Placement ID</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={SafeHeader}
     data={safereportData}
     isSearchable={true}
     isUserTable={false}
     height={410}
      row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
    <div className="gap-1 w-full"> 
     <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Brand Unsuitability Placement ID</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={BrandHeader}
     data={BrandreportData}
     isSearchable={true}
     isUserTable={false}
     height={410}
      row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
    </div>
  )
}

export default Brand_safety;
