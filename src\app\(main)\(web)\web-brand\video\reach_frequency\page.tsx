'use client'
import React from 'react';
import ResizableTable from '@/components/mf/ReportingToolTable';
import SummaryCardGroup from '../commonmodule/page';
import { useState, useRef, useCallback } from 'react';
import { onExpand, downloadURI, debounce, handleExportData } from "@/lib/utils";
import domToImage from "dom-to-image";
import { Card } from '@/components/ui/card';
import DoubleLineChart from '@/components/mf/charts/DoubleLineChart';
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { useRnfTable ,useRnfUniqueCountsDispersion,useRnfImpressionCountsDispersion} from './apicallPage';
import { useWastageFilters } from '../../Filters/useFilters';

import { FilterState ,
  useFilterChangeHandler, 
} from '../../Filters/buildFilters';
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';




function Reach_frequency() {
    const [sercterm, setSearchTerm] = useState('');
    const cardRefs = useRef<Record<string, HTMLElement | null>>({});
    const [expandedCard, setExpandedCard] = useState<string | null>(null);
         const { selectedPackage } = usePackage();
            const { startDate, endDate } = useDateRange();
                const [isExporting, setIsExporting] = useState(false);
                const [exportType, setExportType] = useState<string | null>(null);

      const params = {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    };
   
    const [query, setQuery] = useState({
      publisher: ["all"],
      campaign: ["all"],
      channel: ["all"],
      fraud_category:["all"],
      fraud_sub_category:["all"],
      creative_id:["all"],
      sub_publisher:["all"],
      campaign_id:["all"],
    });
      const rnfparams={
        ...params,
        ...query,
    }
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
         
        const isReady =
  !!selectedPackage && !!startDate && !!endDate;
    const {data:rnfData,isLoading:rnfLoading,error:rnfError} = useRnfTable(rnfparams,isReady || exportType !== 'reachfrequency');
    const {data:uniquecounts,isLoading:uniqueLoading,error:uniqueerror} =useRnfUniqueCountsDispersion(rnfparams,isReady || exportType !== 'uniquecounts');
    const{data:impressioncounts,isLoading:impressionLoading,error:impressionerror} =useRnfImpressionCountsDispersion(rnfparams,isReady||  exportType !== 'impressioncounts');
    const onExport = useCallback(
        async (s: string, title: string, key: string) => {
            const ref = cardRefs.current[key];
            if (!ref) return;

            switch (s) {
                case "png":
                    const screenshot = await domToImage.toPng(ref);
                    downloadURI(screenshot, title + ".png");
                    break;
                default:
            }
        },
        []
    );
    const handleExpand = (key: string) => {
        onExpand(key, cardRefs, expandedCard, setExpandedCard);
    };
    const frequencyHeader = [
        { title: "Frequency", key: "Frequency" },
        { title: "Unique Counts", key: "Unique Counts" },
        { title: "% Unique Counts Distribution", key: "% Unique Counts Distribution" },
        { title: "Impressions", key: "Impressions" },
        { title: "Impression Distribution ", key: "Impression Distribution" },
    ]
    const frequencyreportData = rnfData?.map((item: any) => ({
        "Frequency": item.freq_of_the_period,
        "Unique Counts": item.distinct_deviceids,
        "% Unique Counts Distribution": `${item.deviceid_percentage}%`,
        "Impressions": item.impressions,
        "Impression Distribution": `${item.impressions_percentage}%`,
    })) ?? [];
    console.log("table value",frequencyreportData);
    
   
    const UniquechartData = uniquecounts?.data.map((item: any) => ({
        label: item.label,
        "deviceid_percentage": item.deviceid_percentage,
    })) ?? [];
   
    const UniqiuechartConfig = {
        "deviceid_percentage": {
            label: "Unique %",
            color: "#03045e",
        },
    }

    const ImpressionchartData =
    impressioncounts?.data.map((item: any) => ({
        label: item.label,
        "impressions_percentage": item.impressions_percentage,
    })) ?? []
       
    const ImpressionhartConfig = {
        "impressions_percentage": {
            label: "Impression %",
            color: "#FE5E41",
        },
    }

   const filter = useWastageFilters(params, query);
    const handleFilterChange = useFilterChangeHandler(
      loadedFilter,
      setQuery,
      setLoadedFilter
    );
    const handleExportClick = (type: string) => {
        setExportType(type);
        setIsExporting(true);
    };
    useExportCsv({
        exportParams: params,
        queryParams: query,
        exportType,
        setExportType,
        isExporting,
        setIsExporting,
        endpointMap: {
          reachfrequency: Endpoint.WebBrand.RNF_TABLE,
          uniquecounts: Endpoint.WebBrand.RNF_UNIQUE_COUNTS,
          impressioncounts: Endpoint.WebBrand.RNF_IMPRESSION_COUNTS,
        },
        baseUrlMap: {
          reachfrequency: process.env.NEXT_PUBLIC_WEB_BRAND!,
          uniquecounts: process.env.NEXT_PUBLIC_WEB_BRAND!,
          impressioncounts: process.env.NEXT_PUBLIC_WEB_BRAND!,
        },
      });
    return (
        <div className=" w-full grid grid-col p-2 gap-2">
             <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                          <Filter filter={filter} onChange={handleFilterChange} />
                          </div>
            <SummaryCardGroup params={params} query={query} />
            <div className="gap-1 w-full">
                <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Reach & Frequency Curve</div>
                <div className="grid grid-cols-1 w-full">
                    <ResizableTable
                        isPaginated={true}
                        columns={frequencyHeader}
                        data={frequencyreportData}
                        isSearchable={true}
                        isTableDownload={true}
                        onDownload={() => handleExportClick("reachfrequency")}
                        isUserTable={false}
                        height={410}
                         row_count={5}
                         row_height={10}
                        setSearchTerm={setSearchTerm}
                        SearchTerm={sercterm}
                        marginTop='0'
                        isLoading={rnfLoading}
                        row_height={20}
                    />
                </div>
                <div className="grid sm:grid-cols-1  xl:grid-cols-2 lg:grid-cols-2 md:grid-cols-2 w-full gap-2 pt-2 ">
                    <Card ref={(el) => (cardRefs.current["unique_dispersion"] = el!)} className='p-2'>
                        <DoubleLineChart
                            title="R&F Curve:Unique Counts Dispersion"
                            onExport={() => onExport("png", "R&F Unique Counts Dispersion", "unique_dispersion")}
                            onExpand={() => handleExpand("unique_dispersion")}
                            handleExport={() => handleExportClick("uniquecounts")}
                            isexportcsv={true}
                            chartConfig={UniqiuechartConfig}
                            chartData={UniquechartData}
                            isLoading={uniqueLoading}
                            yAxisXOffset={-20}
                            yAxisXOffsetFullscreen={-35}
                            datalength={10}
                            PercentageLabel="deviceid_percentage"
                        />
                    </Card>
                    <Card ref={(el) => (cardRefs.current["Impression_distribution"] = el!)} className='p-2'>
                        <DoubleLineChart
                            title="R&F Curve:Impression Counts Distribution"
                            onExport={() => onExport("png", "R&F Impression Counts Distribution", "Impression_distribution")}
                            onExpand={() => handleExpand("Impression_distribution")}
                            handleExport={() => handleExportClick("impressioncounts")}
                            isexportcsv={true}
                            chartConfig={ImpressionhartConfig}
                            chartData={ImpressionchartData}
                            isLoading={impressionLoading}
                            yAxisXOffset={-20}
                            yAxisXOffsetFullscreen={-35}
                            datalength={10}
                            PercentageLabel='impressions_percentage'
                           
                            
                        />
                    </Card>
                </div>
            </div>
        </div>
    );
}


export default Reach_frequency
