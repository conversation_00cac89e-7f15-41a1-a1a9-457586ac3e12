import Endpoint from '../../../common/endpoint';
import { apiCall, useAppMutation } from '../../../queries/useAppQuery';

type MailingListPayload = {
  mailing_list_name: string;
  status: boolean;
  emails: string[];
};

type MailingListResponse = {
  message: string;
  id: string;
};

type MailingListUpdatePayload = {
  mailing_list_name: string;
   page: number;
  page_size: number;
};

type MailingListUpdateResponse = {
   total: number;
    page: number;
    total_pages: number;
    mailing_lists: string[];
};
// Payload you send to API
export interface searchPayload {
  name: string;
}

// Response you get back from API
export interface searchResponse {
  mailing_list_name: string;
  emails: string[];
  status: boolean;
  created_at: string;   // ISO date string
  created_by: string;
  updated_at: string;
  updated_by: string;
}
export const useCreateMailingList = (options?: any) =>
  useAppMutation<MailingListResponse, Error, MailingListPayload>(
    (payload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_REPORTING_TOOL_API}${Endpoint.WebBrand.REPORT_CREATE_MAILING_LIST}`,
        method: "POST",
        data: payload,
      }),
    options
  );

  export const useUpdateMailingList = (options?: any) =>
  useAppMutation<MailingListUpdateResponse, Error, MailingListUpdatePayload>(
    (payload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_REPORTING_TOOL_API}${Endpoint.WebBrand.REPORT_LIST_MAILING_LIST}`,
        method: "POST",
        data: payload,
      }),
    options
  );

  export const useGetMailingListByName = (options?: any) =>
  useAppMutation<searchResponse, Error, searchPayload>(
    (payload) =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_REPORTING_TOOL_API}${Endpoint.WebBrand.REPORT_GET_MAILING_LIST_BY_NAME}`,
        method: "POST",
        data: payload,
      }),
    options
  );
