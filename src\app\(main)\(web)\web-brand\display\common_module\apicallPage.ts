import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


type FilterApiParams = {
  package_name: string;
  start_date: string;
  end_date: string;
};

type summaryApiParams = FilterApiParams & {
   package_name: string;
  start_date: string;
  end_date: string;
  publisher?: string[];
  campaign?: string[];
  channel?: string[];
  fraud_category?: string[];
  fraud_sub_category?: string[];
  creative_id?: string[];
  sub_publisher?: string[];
  campaign_id?: string[];
};

const getDefaultPayload = (params: FilterApiParams) => ({
   package_name: params.package_name,
  start_date: params.start_date,
  end_date: params.end_date,
  
});
const getSummaryPayload = (params: summaryApiParams) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
});
  export const useSummaryApi = (params: any, enabled = true) =>
  useAppQuery(
    ['summaryApi', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_SUMMARY}`,
        method: 'POST',
        data:getSummaryPayload(params),
      }),
    { enabled }
  );