import React from 'react';
import { <PERSON>,
    <PERSON><PERSON>ontent,
    
    <PERSON><PERSON><PERSON><PERSON>,
    CardTitle,

 } from '@/components/ui/card';

 interface InformationCardProps{
    InformTitle?:string;
    informDescription?:string;
    isFullscreen?:boolean;
 }

export const InformationCard :React.FC<InformationCardProps>= ({
    InformTitle,
    informDescription,
    isFullscreen = false
}) => {
  return (
    <>
    <Card className={`shadow-md rounded-md ${isFullscreen ? 'flex-1 h-20 xl:h-24' : 'w-[120px] xl:w-[200px] h-15 xl:h-20'} mb-2 dark:bg-background`}>
        <CardHeader className='p-1 bg-yellow-300'>
            <CardTitle className={`${isFullscreen ? 'text-base lg:text-lg' : 'text-tiny-font sm:text-tiny-font lg:text-small-font xl:text-small-font md:text-tiny-font'} font-semibold dark:text-black`}>
                {InformTitle}
            </CardTitle>
        </CardHeader>
        <CardContent className={`${isFullscreen ? 'text-sm lg:text-base' : 'text-tiny-font sm:text-tiny-font lg:text-small-font xl:text-small-font md:text-tiny-font'} mt-0`}>
            {informDescription}
        </CardContent>

    </Card>
    </>
  )
}
