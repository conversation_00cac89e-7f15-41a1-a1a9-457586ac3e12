"use client"

import ResizableTable, { type Column } from "@/components/mf/ReportingToolTable"
import type React from "react"
import { useState, useEffect, useMemo, useCallback } from "react"
import Endpoint from "../../../common/endpoint"
import { useRouter } from "next/navigation"
import ToastContent, { type ToastType } from "@/components/mf/ToastContent"
import DeleteDialog from '@/components/ui/deletedialog'
import { useUserListQuery, useProductListQuery, useRoleListQuery, usePackageListQuery, useCreateUserMutation, useEditUserMutation, useDeleteUserMutation } from '../queries/product-mapping'
import axios from "axios"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import { Button } from "@/components/ui/button"

type FieldType = "input" | "select" | "switch"
type FieldLayout = "half" | "full"

interface Field {
  name: string
  label: string
  type: FieldType
  disabled?: boolean
  options?: Array<{ value: string; label: string }>
  onDependencyChange?: (value: string | string[]) => void | Promise<void>
  dependsOn?: string
  layout?: FieldLayout
  className?: string
  validation?: {
    pattern?: {
      value: RegExp
      message: string
    }
    required?: string
  }
}

interface UserData {
  _id: string
  Name: string
  Email: string
  Phone?: string
  Gender?: string
  Status?: boolean
  LastLogin?: string
  Products?: Array<string | SelectOption>
  Roles?: Array<{
    Role: string | SelectOption
    Product: string
    Packages: Array<string | SelectOption>
  }>
}

interface AdGrpRowData {
  _id: string
  Name: string
  Email: string
  Phone?: string
  Gender: string
  LastLogin: string
  isLoading?: boolean
}

interface SelectOption {
  value: string
  label: string
}

interface TableDataType {
  _id: string
  Name: string
  Email: string
  Gender: string
  Status: string
  LastLogin: string
  Products: string
  Roles: string
  [key: string]: string
}

interface RoleResponse {
  _id: string
  Alias: string
}

const CampaignOverviewPage: React.FC = (): React.ReactNode => {
  const router = useRouter()
  const [rowToDelete, setRowToDelete] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<Record<string, string>>({})
  const [dialogMode, setDialogMode] = useState<"create" | "edit" | "view">("create")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [tableData, setTableData] = useState<AdGrpRowData[]>([])
  const [productList, setProductList] = useState<SelectOption[]>([])
  const [roleList, setRoleList] = useState<SelectOption[]>([])
  const [packageList, setPackageList] = useState<SelectOption[]>([])
  const [selectedEditProduct, setSelectedEditProduct] = useState("")
  const [viewProduct, setViewProduct] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [debouncedEmail, setDebouncedEmail] = useState('')
  const [searchText, setSearchText] = useState('')
  const [page, setPage] = useState(1)
  const [limit, setLimit] = useState(10)
  const [toastData, setToastData] = useState<{
    type: ToastType
    title: string
    description?: string
    variant?: "default" | "destructive" | null
  } | null>(null)
  const [isCreateUserOpen, setIsCreateUserOpen] = useState(false);

  const userListQuery = useUserListQuery({ 
    email: debouncedEmail,
    page,
    limit,
  })
  const { data: userList, refetch, isLoading } = useUserListQuery({
    email: debouncedEmail,
    page,
    limit,
  })

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedEmail(searchText)
    }, 500)

    return () => clearTimeout(handler)
  }, [searchText])

  const productListQuery = useProductListQuery()
  const roleListQuery = useRoleListQuery(selectedEditProduct)
  const packageListQuery = usePackageListQuery(selectedEditProduct)
  const editUserMutation = useEditUserMutation()
  const deleteUserMutation = useDeleteUserMutation()
  const createUserMutation = useCreateUserMutation()

  const [userData, setUserData] = useState<{
    users: UserData[]
    totalUsers: number
    hasError: boolean
    errorMessage: string
  }>({
    users: [],
    totalUsers: 0,
    hasError: false,
    errorMessage: ''
  })

  const handleAddUser = () => {
    setIsCreateUserOpen(true);
  }

  const handleProductChange = async (productName: string) => {
    if (!productName) return;

    try {
      const token = localStorage.getItem("IDToken") || "";

      const [roles, packages] = await Promise.all([
        axios.post(`${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${Endpoint.ROLE_LIST}`, { product_name: productName }, { headers: { Authorization: token } }),
        axios.post(`${process.env.NEXT_PUBLIC_USER_MANAGEMENT}${"access_control/packages?product_name=Web%20Performance"}`, { product_name: productName }, { headers: { Authorization: token } }),
      ]);

      const transformedRoles = roles.data.map((role: any) => ({
        value: role._id,
        label: role.Alias || role._id,
      }));

      const transformedPackages = packages.data.map((pkg: any) => ({
        value: typeof pkg === "string" ? pkg : pkg.Package_Name,
        label: typeof pkg === "string" ? pkg : pkg.Package_Name,
      }));

      setRoleList(transformedRoles);
      setPackageList(transformedPackages);
    } catch (error) {
      console.error("Failed to load dependencies:", error);
      setToastData({
        type: "error",
        title: "Load Error",
        description: "Failed to load role/package options",
      });
    }
  };

  useEffect(() => {
    if (roleListQuery.data) {
      const transformedRoles = (roleListQuery.data as unknown as RoleResponse[]).map(role => ({
        value: role._id || '',
        label: role.Alias || role._id || ''
      }))
      setRoleList(transformedRoles)
    } else {
      setRoleList([])
    }
  }, [roleListQuery.data])

  const processUserData = useCallback(() => {
    try {
      if (!userListQuery.data) {
        setUserData(prev => ({ ...prev, hasError: true }))
        setTableData([])
        return
      }

      const data = userListQuery.data

      if (!Array.isArray(data)) {
        setUserData(prev => ({ 
          ...prev, 
          hasError: true,
        }))
        setTableData([])
        return
      }

      const sortedData = [...data].sort((a, b) => {
        const timestampA = parseInt(a._id.substring(0, 8), 16)
        const timestampB = parseInt(b._id.substring(0, 8), 16)
        return timestampB - timestampA
      })

      const transformedData = sortedData.map(user => ({
        _id: user._id,
        Name: user.Name || "-",
        Email: user.Email || "-",
        Phone: user.Phone || "-",
        Gender: "-",
        Status: "false",
        LastLogin: "-"
      }))

      setTableData(transformedData)
      
      setUserData({
        users: sortedData,
        totalUsers: sortedData.length,
        hasError: false,
        errorMessage: ''
      })

    } catch (error) {
      setUserData(prev => ({
        ...prev,
        hasError: true,
      }))
      setTableData([])
    }
  }, [userListQuery.data])

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await userListQuery.refetch()
      } catch (error) {
        console.error("Error loading initial data:", error)
      }
    }

    loadInitialData()
  }, [])

  useEffect(() => {
    if (userListQuery.data) {
      processUserData()
    }
  }, [userListQuery.data, processUserData])

  useEffect(() => {
    if (isCreateUserOpen) {
      productListQuery.refetch();
    }
  }, [isCreateUserOpen]);

  const adgrpcolumns: Column<Record<string, string | number>>[] = [
    {
      title: "Name",
      key: "Name",
      render: (data) => <div className="text-left">{data.Name}</div>,
    },
    {
      title: "Email",
      key: "Email",
      render: (data) => <div className="text-left">{data.Email}</div>,
    },
    {
      title: "Phone",
      key: "Phone",
      render: (data) => <div className="text-left">{data.Phone || "-"}</div>,
    },
    {
      title: "Gender",
      key: "Gender",
      render: (data) => <div className="text-left">{data.Gender || "-"}</div>,
    },
    {
      title: "Last Login",
      key: "LastLogin",
      render: (data) => <div className="text-left">{data.LastLogin || "-"}</div>,
    }
  ]

  const handleDelete = (item: Record<string, string | number>) => {
    setRowToDelete(item._id as string)
    setDeleteDialogOpen(true)
  }

  const confirmDelete = async () => {
    if (!rowToDelete) return

    try {
      const result = await deleteUserMutation.mutateAsync(rowToDelete)
      
      setToastData({
        type: "success",
        title: "Success",
        description: result.message || "User deleted successfully",
        variant: "default"
      })

      setDeleteDialogOpen(false)
      setRowToDelete(null)
      
      await refetch()
    } catch (error: any) {
      setToastData({
        type: "error",
        title: "Error",
        description: error.message || "Failed to delete user"
      })
    }
  }


  const tableDatas = useMemo(() => {
    return (userList?.data ?? []).map((user) => ({
      _id: user._id || '',
      Name: user.Name || '-',
      Email: user.Email || '-',
      Phone: user.Phone || '-',
      Gender: user.Gender || '-',
      Products: user.Products?.join(', ') || '-',
      Package_Name: Array.isArray(user.Packages) ? user.Packages.join(', ') : (user.Packages || '-'),
      Roles: user.Roles?.map((r) => r.Role).join(', ') || '-'
    }))
  }, [userList])

  const totalPages = userList?.total_pages ?? 1


  const handleEdit = (item: Record<string, string | number>) => {
    setDialogMode("edit")
    
    const editData = {
      _id: item._id?.toString() || '',
      name: item.Name?.toString() || '',
      email: item.Email?.toString() || '',
      phone: item.Phone?.toString() || '',
      gender: item.Gender?.toString() || '',
    }

    setSelectedUser(editData)
    setDialogOpen(true)
  }

  const handleView = (item: Record<string, string | number>) => {
    setDialogMode("view")
    setViewProduct(false)
    
    const viewData = {
      _id: item._id?.toString() || '',
      name: item.Name?.toString() || '',
      email: item.Email?.toString() || '',
      phone: item.Phone?.toString() || '',
      gender: item.Gender?.toString() || '',
      lastLogin: item.LastLogin?.toString() || '-',
    }

    setSelectedUser(viewData)
    setDialogOpen(true)
  }

  useEffect(() => {
    if (viewProduct && selectedEditProduct) {
      handleProductChange(selectedEditProduct)
    }
  }, [viewProduct, selectedEditProduct])

  // Formik initial values and validation schema
  const initialValues = {
    userName: "",
    email: "",
    phone: "",
    gender: "",
    product: "",
    roles: [] as Array<{ roleId: string; product: string }>,
  };
const validationSchema = Yup.object({
  userName: Yup.string().required("User Name is required"),
  email: Yup.string().email("Invalid email").required("Email is required"),
  phone: Yup.string()
    .matches(/^[0-9]{10}$/, "Phone number must be 10 digits")
    .optional(), // or use `.notRequired()` instead of `.optional()`
  gender: Yup.string().required("Gender is required"),
  product: Yup.string().required("Select a product"),
  roles: Yup.array().min(1, "Select at least one role"),
});

  // Example options (replace with real data from your queries)
  const genderOptions = [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  return (
    <div className="flex flex-col h-full">
      <div className="p-3">
        <div className="min-h-[500px] relative">
          <ResizableTable
            columns={adgrpcolumns}
            data={tableDatas}
            isPaginated={true}
            isLoading={isLoading}
            headerColor="#DCDCDC"
            isSearchable={true}
            setSearchTerm={setSearchText}
            SearchTerm={searchText}
            isDelete={true}
            onDelete={handleDelete}
            isEdit={false}
            onLimitChange={(newLimit: number) => setLimit(newLimit)}
            onPageChangeP={(newPage: number) => setPage(newPage)}
            totalPages={totalPages}
            onEdit={handleEdit}
            isView={false}
            onView={handleView}
            isTableDownload={false}
            isUserTable={true}
            handleAddUser={handleAddUser}
            handleProductMapping={() => router.push('/webfraud/User-Management/Product_Mapping')}
           // height={400}
          />
        </div>
      </div>
      {toastData && (
        <ToastContent
          type={toastData.type}
          title={toastData.title}
          description={toastData.description}
          variant={toastData.variant}
        />
      )}
   
      <DeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDelete}
        title="Delete User"
        description="Are you sure you want to delete.?"
      />

      <Dialog open={isCreateUserOpen} onOpenChange={setIsCreateUserOpen}>
        <DialogContent className="max-w-3xl w-full">
          <DialogHeader>
            <DialogTitle>Create User</DialogTitle>
          </DialogHeader>
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting, resetForm }) => {
              setSubmitting(true);
              try {
                // Transform roles into the required format
                const transformedRoles = values.roles.map(role => ({
                  Role: role.roleId,
                  Package: [],
                  Product: role.product
                }));

                await createUserMutation.mutateAsync({
                  Name: values.userName,
                  Email: values.email,
                  Phone: values.phone,
                  Gender: values.gender,
                  Roles: transformedRoles
                });
                setIsCreateUserOpen(false);
                resetForm();
                refetch();
                setToastData({
                  type: "success",
                  title: "User Created",
                  description: "User has been created successfully.",
                });
              } catch (error: any) {
                const errorMessage = error.response?.data?.error?.message || error.message || "Failed to create user";
                setToastData({
                  type: "error",
                  title: "Error",
                  description: errorMessage,
                });
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ values, setFieldValue, isSubmitting, resetForm }) => (
              <Form>
                <div className="grid grid-cols-2 gap-6">
                  <div className="flex flex-col gap-4">
                    <label>User Name</label>
                    <Field name="userName" as={Input} placeholder="Enter user name" />
                    <ErrorMessage name="userName" component="div" className="text-red-500 text-xs" />

                    <label>Phone</label>
                    <Field name="phone" as={Input} placeholder="Enter phone" />
                    <ErrorMessage name="phone" component="div" className="text-red-500 text-xs" />

                    <label>Products</label>
                    <Select
                      value={values.product}
                      onValueChange={val => {
                        setFieldValue("product", val);
                        setFieldValue("roles", []);
                        setSelectedEditProduct(val);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Products" />
                      </SelectTrigger>
                      <SelectContent>
                        {(productListQuery.data || []).map(product => (
                          <SelectItem key={product.value} value={product.value}>{product.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="product" component="div" className="text-red-500 text-xs" />

                    <label>Roles</label>
                    <MultiSelect
                      options={roleListQuery.data || []}
                      value={values.roles.map(r => r.roleId)}
                      onValueChange={val => {
                        const newRoles = val.map(roleId => ({
                          roleId,
                          product: values.product
                        }));
                        setFieldValue("roles", newRoles);
                      }}
                      placeholder="Select Roles"
                      disabled={!values.product || !roleListQuery.data}
                    />
                    <ErrorMessage name="roles" component="div" className="text-red-500 text-xs" />

                 
                  </div>
                  <div className="flex flex-col gap-4">
                    <label>Email</label>
                    <Field name="email" as={Input} placeholder="Enter email" />
                    <ErrorMessage name="email" component="div" className="text-red-500 text-xs" />

                    <label>Gender</label>
                    <Select
                      value={values.gender}
                      onValueChange={val => setFieldValue("gender", val)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Gender" />
                      </SelectTrigger>
                      <SelectContent>
                        {genderOptions.map(opt => (
                          <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <ErrorMessage name="gender" component="div" className="text-red-500 text-xs" />
                  </div>
                </div>
                <DialogFooter className="mt-8 flex justify-end gap-4">
                  <Button
                    type="button"
                  variant="default" 
                  className="bg-primary text-white hover:bg-secondary"
                    onClick={() => {
                      setIsCreateUserOpen(false);
                      resetForm();
                    }}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                variant="default"
                 className="bg-primary text-white hover:bg-secondary"
                    disabled={isSubmitting}
                  >
                    Save
                  </Button>
                </DialogFooter>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default CampaignOverviewPage