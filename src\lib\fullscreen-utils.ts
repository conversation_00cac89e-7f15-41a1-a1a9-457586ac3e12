/**
 * Utility function to detect if the document is in fullscreen mode
 * Supports all major browsers (Chrome, Firefox, Safari, Edge)
 * Safe for SSR - returns false on server side
 */
export function isFullscreen(): boolean {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return false;
  }

  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
}

/**
 * Hook-like function to listen for fullscreen changes
 * Returns a cleanup function to remove event listeners
 * Safe for SSR - returns no-op function on server side
 */
export function listenToFullscreenChanges(callback: (isFullscreen: boolean) => void): () => void {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    // Return no-op function for SSR
    return () => {};
  }

  const handleFullscreenChange = () => {
    callback(isFullscreen());
  };

  // Add event listeners for all browser prefixes
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);

  // Return cleanup function
  return () => {
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
  };
}

/**
 * Utility function to safely set fullscreen state in components
 * Use this instead of direct document.fullscreenElement access
 * @param setStateFunction - The state setter function
 */
export function setFullscreenStateSafely(setStateFunction: (value: boolean) => void): void {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    setStateFunction(isFullscreen());
  } else {
    setStateFunction(false);
  }
} 