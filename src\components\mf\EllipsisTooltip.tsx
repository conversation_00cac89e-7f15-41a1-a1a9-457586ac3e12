"use client";
import React, { useRef, useEffect, useState } from "react";

interface EllipsisTooltipProps {
  content: string | number;
  className?: string;
}

const EllipsisTooltip: React.FC<EllipsisTooltipProps> = ({ content, className }) => {
  const spanRef = useRef<HTMLSpanElement>(null);
  const [showTooltip, setShowTooltip] = useState(false);

  useEffect(() => {
    const el = spanRef.current;
    if (!el) return;
    // Allow time for layout
    const checkTruncation = () => {
      if (el.scrollWidth > el.clientWidth) {
        setShowTooltip(true);
      } else {
        setShowTooltip(false);
      }
    };
    // Small timeout to allow layout
    const timeout = setTimeout(checkTruncation, 0);
    return () => clearTimeout(timeout);
  }, [content]);

  return (
<span
  ref={spanRef}
  className={`inline-block overflow-hidden text-ellipsis whitespace-nowrap ${className || ""}`}
  style={{
    // height: "100%",
    // lineHeight: "inherit",  // Inherit from parent cell
  }}
  title={showTooltip ? String(content) : undefined}
>
  {content}
</span>
  );
};

export default EllipsisTooltip;
