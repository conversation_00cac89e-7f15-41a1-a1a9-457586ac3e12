// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   output: "standalone",
//   reactStrictMode: false,
//   eslint: { ignoreDuringBuilds: true },
//   typescript: { ignoreBuildErrors: true },
//   images: {
//     remotePatterns: [
//       {
//         protocol: "https",
//         hostname: "infringementportalcontent.mfilterit.com",
//         pathname: "/**",
//       },
//     ],
//   },
//   async redirects() {
//     return [
//       {
//         source: '/webfraud',
//         destination: '/webfraud/',
//         permanent: true,
//       },
//     ]
//   },
// };
 
// export default nextConfig;
/** @type {import('next').NextConfig} */
// console.log('✅ NEXT_PUBLIC_ENV_NAME:', process.env.NEXT_PUBLIC_ENV_NAME);
// console.log('✅ NEXT_PUBLIC_BASE_PATH:', process.env.NEXT_PUBLIC_BASE_PATH);
// console.log('✅ NEXT_PUBLIC_REDIRECT_URL:', process.env.NEXT_PUBLIC_REDIRECT_URL);
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,  
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
 
  // 👇 Add basePath and assetPrefix for App Fraud
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || undefined,
  //basePath: '/web',
  assetPrefix: '/web',

 
  images: {
    remotePatterns: [
      {    
        protocol: "https",
        hostname: "infringementportalcontent.mfilterit.com",
        pathname: "/**",
      },
    ],
  },  
}
 
export default nextConfig;
 