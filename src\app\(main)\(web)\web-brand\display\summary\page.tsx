'use client';
import React, { useMemo } from 'react';
import { Card } from '@/components/ui/card';
import ChartBarStacked from '@/components/mf/charts/stackedBarChart';
import { onExpand, downloadURI, debounce, handleExportData } from "@/lib/utils";
import { useCallback,useRef ,useState} from 'react';
import domToImage from "dom-to-image";
import SummaryCardGroup from  '../common_module/page';
import DynamicBarChart from '@/components/mf/charts/DynamicBarChart';
import { Filter } from "@/components/mf/Filters";
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {
  useFilterChangeHandler,
  FilterState,
} from '../Filters/buildFilters';
import StackedBarWithLine from '@/components/mf/charts/StackedBarwithLine';
import HeaderRow from '@/components/mf/HeaderRow';
import { useWastageFilters } from '../Filters/useFilters';
import {usecreativewiseTraffic,usefraudCategoryWiseTraffic,useDeviceTypeWiseTraffic,usefraudDistributionByDeviceModel,usefraudDistributionByDeviceMake,usefraudDistributionByPublisher,useimpressionDistributionByCampaign,useimpressionDistributionByCreative} from './apicallPage';
import { useExportCsv } from '@/lib/Exportdata';
import Endpoint from '../../../common/endpoint';


const Summary = () => {
      const cardRefs = useRef<Record<string, HTMLElement | null>>({});
      const [expandedCard, setExpandedCard] = useState<string | null>(null);
        const { selectedPackage } = usePackage();
        const { startDate, endDate } = useDateRange();
        const [exportType, setExportType] = useState<string | null>(null);
        const [isExporting, setIsExporting] = useState(false);
    
  const params = useMemo(() => ({
  package_name: selectedPackage,
  start_date: startDate,
  end_date: endDate,
}),[selectedPackage,startDate,endDate]);


 const [query, setQuery] = useState({
      publisher: ["all"],
      campaign: ["all"],
      channel: ["all"],
      fraud_category:["all"],
      fraud_sub_category:["all"],
      creative_id:["all"],
      sub_publisher:["all"],
      campaign_id:["all"],
    });
const campaignpayloadfield = {
  ...params,
  ...query,
};

const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
const isReady =
  !!selectedPackage && !!startDate && !!endDate;
    const{ data: responseData, isLoading:creativeLoading, error:errorDevice} = usecreativewiseTraffic(campaignpayloadfield,isReady || exportType !== 'creative');
    const{ data: fraudData, isLoading:fraudLoading, error:fraudError} = usefraudCategoryWiseTraffic(campaignpayloadfield,isReady || exportType !== 'fraud');       
    const{ data: fraudDistributionByDeviceModel, isLoading:fraudDistributionByDeviceModelLoading, error:fraudDistributionByDeviceModelError} = usefraudDistributionByDeviceModel(campaignpayloadfield,isReady || exportType !== 'Model');
    const{ data: fraudDistributionByDeviceMake, isLoading:fraudDistributionByDeviceMakeLoading, error:fraudDistributionByDeviceMakeError} = usefraudDistributionByDeviceMake(campaignpayloadfield,isReady ||exportType !== 'Make');
    const { data: fraudDistributionByPublisher, isLoading:fraudDistributionByPublisherLoading, error:fraudDistributionByPublisherError} = usefraudDistributionByPublisher(campaignpayloadfield,isReady || exportType !== 'Publisher');
    const{ data: impressionDistributionByCampaign, isLoading:impressionDistributionByCampaignLoading, error:impressionDistributionByCampaignError} = useimpressionDistributionByCampaign(campaignpayloadfield,isReady || exportType !== 'Campaign');
    const{ data: creativewiseTraffic, isLoading:creativewiseTrafficLoading, error:creativewiseTrafficError} = usecreativewiseTraffic(campaignpayloadfield,isReady || exportType !== 'creativewiseTraffic');
    const{ data: deviceTypeWiseTraffic, isLoading:deviceTypeWiseTrafficLoading, error:deviceTypeWiseTrafficError} = useDeviceTypeWiseTraffic(campaignpayloadfield,isReady || exportType !== 'deviceTypeWiseTraffic');
    const{ data: impressionDistributionByCreative, isLoading:impressionDistributionByCreativeLoading, error:impressionDistributionByCreativeError} = useimpressionDistributionByCreative(campaignpayloadfield,isReady || exportType !== 'impressionDistributionByCreative');
    const onExport = useCallback(
        async (s: string, title: string, key: string) => {
          const ref = cardRefs.current[key];
          if (!ref) return;       
    
          switch (s) {
            case "png":
              const screenshot = await domToImage.toPng(ref);
              downloadURI(screenshot, title + ".png");
              break;
            default:
          }
        },
        []
      );
      const handleExpand = (key: string) => {
        onExpand(key, cardRefs, expandedCard, setExpandedCard);
      };


  const sourcechartData = Array.isArray(responseData?.data)
  ? responseData?.data.map((item:any) => ({
    label: item.label,
    "F-Cap Violation": item["F-Cap Violation"],
    IVT: item.IVT,
  }))
  : [];
const sourcechartConfig = responseData?.config ?? {};

  const devicechartData = Array.isArray(deviceTypeWiseTraffic?.data)
  ? deviceTypeWiseTraffic?.data.map((item:any) => ({
    label: item.label,
    tablet: item.tablet,
    smarttv: item.smarttv,
    mobile: item.mobile,
  }))
  : [];

const devicechartConfig = deviceTypeWiseTraffic?.config ?? {};

  const distributionPublisher= Array.isArray(fraudDistributionByPublisher?.data)
  ? fraudDistributionByPublisher?.data.map((item:any) => ({
    label: item.label,
    "Invalid Traffic": item["Invalid Traffic"],
    "Valid Traffic": item["Valid Traffic"],
  }))
  : [];
   
  const PublisherConfig=fraudDistributionByPublisher?.config ?? {};

   const distributionModel= Array.isArray(fraudDistributionByDeviceModel?.data)
  ? fraudDistributionByDeviceModel?.data.map((item:any) => ({
    label: item.label,
   "Invalid Traffic": item["Invalid Traffic"],
    "Valid Traffic": item["Valid Traffic"],
  }))
  : [];
   
  const ModelConfig=fraudDistributionByDeviceModel?.config ?? {};

  const distributionMake= Array.isArray(fraudDistributionByDeviceMake?.data)
  ? fraudDistributionByDeviceMake?.data.map((item:any) => ({
    label: item.label,
    "Invalid Traffic": item["Invalid Traffic"],
    "Valid Traffic": item["Valid Traffic"],
  }))
  : [];
  
  const MakeConfig=fraudDistributionByDeviceMake?.config ?? {};
  
const xAxisConfigS = {
    dataKey: "label",
  };

  const xAxisConfigD = {
    dataKey: "label",   
  };

  const xAxisConfigstack = {
    dataKey: "value",
  
  };

  const FraudchartData = Array.isArray(fraudData?.data)
  ? fraudData.data.map((item: any) => ({
      label: item.label,
      Percentage: item.Percentage,
      Total: item.Total,
    }))
  : [];

const FraudchartConfig = fraudData?.config ?? {};
 
const Topssp=[
  {
    label:"google.com",
    "Total Impressions": 50000340,
    "Invalid %": 10,
    
  },
  {
    label:"pubmatic.com",
    "Total Impressions": 40000340,
    "Invalid %": 20,
  },
{
  label:"rubiconProject.com",
  "Total Impressions": 30000340,
  "Invalid %": 30
},
{
  label:"empty",
  "Total Impressions": 20000340,
  "Invalid %": 40,

},
{
  label:"indexexchange",
  "Total Impressions": 1000340,
  "Invalid %": 50,
},
{
  label:"inmobi.com",
  "Total Impressions": 1000340,
  "Invalid %": 30,
},
{
  label:"appnexus",
  "Total Impressions": 14000340,
  "Invalid %": 150,
},
{
  label:"themediagrid.com",
  "Total Impressions": 100340,
  "Invalid %": 10,
},
{
  label:"onetag.com",
  "Total Impressions": 900340,
  "Invalid %": 90,
},
{
  label:"media.net",
  "Total Impressions": 170340,
  "Invalid %": 35,
},
{
  label:"pubnative.net",
  "Total Impressions": 150340,
  "Invalid %": 45,
},
{
  label:"unrulymedia.com",
  "Total Impressions": 4340,
  "Invalid %": 50,
},
{
  label:"yieldmo.com",
  "Total Impressions": 50340,
  "Invalid %": 70,
}
]
const TopsspConfig={
  "Total Impressions": {
    label: "Total Impressions",
    color: "#065084",
  },
  "Invalid %": {
    label: "Invalid %",
    color: "#b91c1c",
  },
}

const Topcampign= Array.isArray(impressionDistributionByCampaign?.data)
  ? impressionDistributionByCampaign?.data.map((item:any) => ({
    label: item.label,
    "Total Impressions": item["Total Impressions"],
    "Invalid Traffic": item["Invalid Traffic"],
  }))
  : [];
  const CampaignConfig=impressionDistributionByCampaign?.config ?? {};

const Topcreative= Array.isArray(impressionDistributionByCreative?.data)
  ? impressionDistributionByCreative?.data.map((item:any) => ({
    label: item.label,
    "Total Impressions": item["Total Impressions"],
    "Invalid Traffic": item["Invalid Traffic"],
  }))
  : [];
  const CreativeConfig=impressionDistributionByCreative?.config ?? {};

const filter = useWastageFilters(params, query);

const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
const handleExportClick = async (type: string) => {
  setExportType(type as any);
  setIsExporting(true);
};
 useExportCsv({
  exportParams: params,
  queryParams: query,
  exportType,
  setExportType,
  isExporting,
  setIsExporting,
  endpointMap: {
    fraud: Endpoint.WebBrand.DISPLAY_FRAUD_CATEGORY_WISE_TRAFFIC,
    creative: Endpoint.WebBrand.DISPLAY_CREATIVE_WISE_TRAFFIC,
    Publisher: Endpoint.WebBrand.DISPLAY_FRAUD_DISTRIBUTION_BY_PUBLISHER,
    Model: Endpoint.WebBrand.FRAUD_DISTRIBUTION_BY_DEVICE_MODEL,
    Make: Endpoint.WebBrand.FRAUD_DISTRIBUTION_BY_DEVICE_MAKE,
    Campaign: Endpoint.WebBrand.IMPRESSION_DISTRIBUTION_BY_CAMPAIGN,
    creativewiseTraffic: Endpoint.WebBrand.IMPRESSION_DISTRIBUTION_BY_CREATIVE,
    deviceTypeWiseTraffic: Endpoint.WebBrand.DISPLAY_DEVICE_TYPE_WISE_TRAFFIC,
  },
  baseUrlMap: {
    fraud: process.env.NEXT_PUBLIC_WEB_BRAND!,
    creative: process.env.NEXT_PUBLIC_WEB_BRAND!,
    Publisher: process.env.NEXT_PUBLIC_WEB_BRAND!,
    Model: process.env.NEXT_PUBLIC_WEB_BRAND!,
    Make: process.env.NEXT_PUBLIC_WEB_BRAND!,
    Campaign: process.env.NEXT_PUBLIC_WEB_BRAND!,
    creativewiseTraffic: process.env.NEXT_PUBLIC_WEB_BRAND!,
    deviceTypeWiseTraffic: process.env.NEXT_PUBLIC_WEB_BRAND!,
  },
});
    return (
       <div className="p-2 w-full grid grid-col  gap-2">
         <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 sm:overflow-x-auto  scrollbar w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
              <Filter filter={filter} onChange={handleFilterChange} />
              </div>
        <SummaryCardGroup params={params} query={query}/>
 
  <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-2 ">
    <Card  ref={(el) => (cardRefs.current["creative_camapign_id"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Creative ID / Campaign ID"
         onExport={() => onExport("png", "Creative ID / Campaign ID", "creative_camapign_id")}
         onExpand={() => handleExpand("creative_camapign_id")}
         handleExport={() => handleExportClick("creative")}
        chartConfig={sourcechartConfig}
        chartData={sourcechartData}
        isHorizontal={true}
        xAxis={xAxisConfigS}
        yAxis={xAxisConfigstack}
        height={330}
        isLoading={creativeLoading}
        marginBottom={10}
        marginLeft={10}
        graphheight={40}
        marginRight={20}
        marginTop={30}
        barsize={50}
        truncateLengthx={10}
        fullscreenbarsize={100}
          isLegend={false}
          isCustomLegendContent={true}  
        />
    </Card>
    <Card  ref={(el) => (cardRefs.current["device_split"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Device*  Type Wise Split"
         onExport={() => onExport("png", "Device Type Wise Split", "device_split")}
         onExpand={() => handleExpand("device_split")}
        chartConfig={devicechartConfig}
        chartData={devicechartData}
        isHorizontal={true}
        xAxis={xAxisConfigD}
        yAxis={xAxisConfigstack}
        height={330}
        truncateLengthx={10}
        isLoading={deviceTypeWiseTrafficLoading}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={30}
        barsize={50}
        fullscreenbarsize={100}
         isLegend={false}
        // ischangeLegend={true}
        isCustomLegendContent={true}
        graphheight={40}
        />
    </Card>
     <Card ref={(el) => (cardRefs.current["fraud_subfraud"] = el!)} className='p-2'> 
      <DynamicBarChart
      title="Fraud Category Contribution"
      onExport={() => onExport("png", "Fraud Category  Contribution", "fraud_subfraud")}
      onExpand={() => handleExpand("fraud_subfraud")}
      handleExport={() => handleExportClick("fraud")}
      data={FraudchartData}
      config={FraudchartConfig}
      isHorizontal={false}
      isLoading={fraudLoading}
      marginRight={20}
     // marginTop={40}
      barsize={30}
      isLegend={false}
      height="250px"
      yaxis1={
        {
          yAxisId: "right",
          orientation: "right",
          stroke: "hsl(var(--chart-1))",
        }
      }
      rightAxisKeys={["Percentage"]}
      yAxisPercentage={true}
      yaxisright={true}
      isCustomLegendContent={true}
      />
    </Card>
  </div>
  <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-2 ">
    <Card  ref={(el) => (cardRefs.current["top_ssps_with_ivt"] = el!)} className='p-2'>
        <HeaderRow 
        title="Top SSPs with IVT(%)"
         onExport={() => onExport("png", "Top SSPs with IVT(%)", "top_ssps_with_ivt")}
         onExpand={() => handleExpand("top_ssps_with_ivt")}/>
        <StackedBarWithLine
        chartConfig={TopsspConfig}
        chartData={Topssp}
        isHorizontal={true}
        keys="Invalid %"
        handleExport={() => handleExportClick("Publisher")}
        truncateLengthx={5}
         xAxisConfig={{
                  dataKey: "label",
                  tickLine: false,
                  tickMargin: 10,
                  axisLine: true,
                  tickFormatter: (value: string) => value,
                  textAnchor: "middle",
                  dy: 10,
                }}
          YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  stroke: "hsl(var(--chart-3))",
                }}
                    YAxis2={{
                  yAxisId: "right",
                  orientation: "right",
                   stroke: "hsl(var(--chart-3))",
                }} 
        graphheight={320}
       // isLoading={LoadingSource}
        
        />
    </Card>
    <Card  ref={(el) => (cardRefs.current["top_campaign_with_ivt"] = el!)} className='p-2'>
      <HeaderRow
        title="Top Campaign with IVT(%)"
        onExport={() => onExport("png", "Top Campaign with IVT(%)", "top_campaign_with_ivt")}
        onExpand={() => handleExpand("top_campaign_with_ivt")}
      />
        <StackedBarWithLine
        chartConfig={CampaignConfig}
        chartData={Topcampign}
        isHorizontal={true}
        handleExport={() => handleExportClick("Campaign")}
        truncateLengthx={5}
        keys="Invalid Traffic"
        xAxisConfig={{
                  dataKey: "label",
                  tickLine: false,
                  tickMargin: 10,
                  axisLine: true,
                  tickFormatter: (value: string) => value,
                  textAnchor: "middle",
                  dy: 10,
                }}
          YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  stroke: "hsl(var(--chart-3))",
                }}
                    YAxis2={{
                  yAxisId: "right",
                  orientation: "right",
                   stroke: "hsl(var(--chart-3))",
                }} 
        graphheight={320}
        isLoading={impressionDistributionByCampaignLoading}
        />
    </Card>
     <Card ref={(el) => (cardRefs.current["top_creatives_with_ivt"] = el!)} className='p-2'> 
      <HeaderRow
       title="Top Creatives with IVT(%)"
      onExport={() => onExport("png", "Top Creatives with IVT(%)", "top_creatives_with_ivt")}
      onExpand={() => handleExpand("top_creatives_with_ivt")}
      />
      <StackedBarWithLine
      chartData={Topcreative}
      chartConfig={CreativeConfig}
      isHorizontal={false}
      handleExport={() => handleExportClick("creativewiseTraffic")}
       truncateLengthx={5}
       keys="Invalid Traffic"
      xAxisConfig={{
                  dataKey: "label",
                  tickLine: false,
                  tickMargin: 10,
                  axisLine: true,
                  tickFormatter: (value: string) => value,
                  textAnchor: "middle",
                  dy: 10,
                }}
          YAxis1={{
                  yAxisId: "left",
                  orientation: "left",
                  stroke: "hsl(var(--chart-3))",
                }}
                graphheight={320}
     isLoading={impressionDistributionByCreativeLoading}
   />
    </Card>
  </div>
   <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-3 gap-2 ">
    <Card  ref={(el) => (cardRefs.current["distribution_by_publisher"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Distribution by Publisher"
         onExport={() => onExport("png", "Distribution by Publisher", "distribution_by_publisher")}
         onExpand={() => handleExpand("distribution_by_publisher")}
         handleExport={() => handleExportClick("Publisher")}
        chartConfig={PublisherConfig}
        chartData={distributionPublisher}
        isHorizontal={false}
        xAxis={xAxisConfigS}
        yAxis={xAxisConfigD}
        height={330}
        graphheight={40}
       isLoading={fraudDistributionByPublisherLoading}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={10}
        barsize={50}
        isLegend={false}
        fullscreenbarsize={100}
         yAxisXOffset={-40}
         yAxisXOffsetFullscreen={-70}
        isCustomLegendContent={true}
        
        />
    </Card>
    <Card  ref={(el) => (cardRefs.current["distribution_by_make"] = el!)} className='p-2'>
        <ChartBarStacked
         title="Distribution by Make"
         onExport={() => onExport("png", "Distribution by Make", "distribution_by_make")}
         onExpand={() => handleExpand("distribution_by_make")}
        handleExport={() => handleExportClick("Make")}
        chartConfig={MakeConfig}
        chartData={distributionMake}
        isHorizontal={false}
        xAxis={xAxisConfigD}
        yAxis={xAxisConfigD}
        height={330}
       isLoading={fraudDistributionByDeviceMakeLoading}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={10}
        barsize={50}
        graphheight={40}
        isLegend={false}
        fullscreenbarsize={100}
       yAxisXOffset={-40}
       yAxisXOffsetFullscreen={-70}
       isCustomLegendContent={true}
        />
    </Card>
     <Card ref={(el) => (cardRefs.current["distribution_by_model"] = el!)} className='p-2'> 
      <ChartBarStacked
      title="Distribution by Model"
      onExport={() => onExport("png", "Distribution by Model", "distribution_by_model")}
      onExpand={() => handleExpand("distribution_by_model")}
      handleExport={() => handleExportClick("Model")}      
      chartData={distributionModel}
      chartConfig={ModelConfig}
      isHorizontal={false}
      isLegend={false}
        xAxis={xAxisConfigD}
        yAxis={xAxisConfigD}
        height={330}
        graphheight={40}
       isLoading={fraudDistributionByDeviceModelLoading}
        marginBottom={10}
        marginLeft={10}
        marginRight={20}
        marginTop={10}
        barsize={50}
        fullscreenbarsize={100}
         yAxisXOffset={-40}
         yAxisXOffsetFullscreen={-70}
        isCustomLegendContent={true}
        />
    </Card>
  </div>
  <div className='grid grid-cols-1 '> 
    <Card className='p-2 shadow-md text-xs'>
        * Device Classifications may overlap  - certain CTV devices can function as mobile devices and vice versa Please interpret device level data with caution.
    </Card>
  </div>
</div>

    );
};

export default Summary;
