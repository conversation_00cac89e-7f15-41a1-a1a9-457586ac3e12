import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
  search_term:params.search_term,
  limit:params.limit,
  page:params.page,
});

export const useGeoDistribution = (params: any, enabled = true) =>
    useAppQuery(
        ['geoDistribution', JSON.stringify(params)],
        () =>
            apiCall({
                url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_GEO_DISTRIBUTION}`,
                method: 'POST',
                data:getDefaultPayload(params),
            }),
        { enabled }
    );
  
export const useDayWiseTrend = (params: any, enabled = true) =>
    useAppQuery(
        ['dayWiseTrend', JSON.stringify(params)],
        () =>
            apiCall({
                url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DISPLAY_DAY_WISE_TREND}`,
                method: 'POST',
                data:getDefaultPayload(params),
            }),  
        { enabled }
    );
