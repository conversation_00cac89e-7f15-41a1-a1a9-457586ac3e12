#!/bin/bash

# Script to remove filter-related code from overall-summary page
# This script will create a backup and then remove the specified sections

FILE="src/app/(main)/(web)/web-analytics/Dashboard/overall-summary/page.tsx"
BACKUP_FILE="${FILE}.backup.$(date +%Y%m%d_%H%M%S)"

echo "Creating backup: $BACKUP_FILE"
cp "$FILE" "$BACKUP_FILE"

echo "Removing filter-related code..."

# First, let's find the exact line numbers
echo "Finding line numbers..."

# Find the start of filter state variables
START_STATE=$(grep -n "const \[existingPublisherdata" "$FILE" | cut -d: -f1)
echo "Filter state variables start at line: $START_STATE"

# Find the end of filter state variables
END_STATE=$(grep -n "const \[existingChanneldata" "$FILE" | cut -d: -f1)
echo "Filter state variables end at line: $END_STATE"

# Find the start of filter API calls
START_API=$(grep -n "// Publishers Filter API" "$FILE" | cut -d: -f1)
echo "Filter API calls start at line: $START_API"

# Find the start of filter computation
START_COMP=$(grep -n "const filter = React.useMemo" "$FILE" | cut -d: -f1)
echo "Filter computation starts at line: $START_COMP"

# Find the end of filter computation (look for the closing bracket and dependencies)
END_COMP=$(grep -n "query.event_type," "$FILE" | tail -1 | cut -d: -f1)
END_COMP=$((END_COMP + 5))  # Add a few lines for the closing brackets
echo "Filter computation ends around line: $END_COMP"

echo "Removing filter state variables (lines $START_STATE-$END_STATE)..."
sed -i "${START_STATE},${END_STATE}d" "$FILE"

# Recalculate line numbers after first deletion
NEW_START_API=$(grep -n "// Publishers Filter API" "$FILE" | cut -d: -f1)
if [ ! -z "$NEW_START_API" ]; then
    echo "Removing filter API calls (starting at line $NEW_START_API)..."
    # Find the end of the last API call
    NEW_END_API=$(grep -n "// evenyt type Filter API" "$FILE" | cut -d: -f1)
    NEW_END_API=$((NEW_END_API + 20))  # Add lines for the API call
    sed -i "${NEW_START_API},${NEW_END_API}d" "$FILE"
fi

# Recalculate line numbers after second deletion
NEW_START_COMP=$(grep -n "const filter = React.useMemo" "$FILE" | cut -d: -f1)
if [ ! -z "$NEW_START_COMP" ]; then
    echo "Removing filter computation (starting at line $NEW_START_COMP)..."
    # Find the end by looking for the closing bracket pattern
    NEW_END_COMP=$(grep -n "query.event_type," "$FILE" | tail -1 | cut -d: -f1)
    if [ ! -z "$NEW_END_COMP" ]; then
        NEW_END_COMP=$((NEW_END_COMP + 5))
        sed -i "${NEW_START_COMP},${NEW_END_COMP}d" "$FILE"
    fi
fi

echo "Cleanup completed! Backup saved as: $BACKUP_FILE"
echo "Please review the changes and test the application." 