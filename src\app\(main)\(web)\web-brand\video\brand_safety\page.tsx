'use client'
import React from 'react'
import SummaryCardGroup from '../commonmodule/page';
import ResizableTable from '@/components/mf/ReportingToolTable';
import { useState } from 'react';
import { Filter } from "@/components/mf/Filters";
import {useChannelsFilter,useCampaignsFilter,usePublishersFilter} from '../../FilterApi';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from  "../../Filters/buildFilters";
import { useWastageFilters } from '../../Filters/useFilters'


function Brand_safety() {
     const[sercterm,setSearchTerm] = useState('');
    const { selectedPackage } = usePackage();
    const { startDate, endDate } = useDateRange();
             
               const params = {
           package_name: selectedPackage,
           start_date: startDate,
           end_date: endDate,
         };
         const [query, setQuery] = useState({
           publishers: ["all"],
           campaigns: ["all"],
           channels: ["all"],
           fraudcategory:["all"],
           subfraudcategory:["all"],
           creative_id:["all"],
           publisher_ids:["all"],
           
         });
         const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
     

   
         const UnsafeHeader =[     
         { title: "Video ID/ Placement ID", key: "Video ID/ Placement ID"},
        //  { title: "Content Name", key: "Content Name" },
         { title: "Unsafe Category", key: "Unsafe Category" },
         { title: "AD-Request Count-Blocked", key:"AD-Request Count-Blocked" },
         ]
           const SafeHeader =[     
         { title: "Video ID/ Placement ID", key:"Video ID/ Placement ID"},
        //  { title: "Content Name", key: "Content Name" },
         { title: "Category", key: "Category" },
         { title: "Impression Served", key:"Impression Served" },
         ]
           const BrandHeader =[     
         { title: "Video ID/ Placement ID", key: "Video ID/ Placement ID" },
        //  { title: "Content Name", key:"Content Name"},
         { title: "Category", key: "Category" },
         { title: "Impression Served", key:"Impression Served" },
         ]

          const UnsafereportData =[
    {
        "Video ID/ Placement ID": "Video1",
        // "Content Name": "Showname",
        "Unsafe Category": "Debatable",
        "AD-Request Count-Blocked": "2,323",
    },
     {
        "Video ID/ Placement ID": "Video2",
        // "Content Name": "Showname",
        "Unsafe Category": "Terroroism",
        "AD-Request Count-Blocked": "23",
    },
    {
        "Video ID/ Placement ID": "Video3",
        // "Content Name": "Showname",
        "Unsafe Category": "CategoryX",
        "AD-Request Count-Blocked": "423",
    },
      {
        "Video ID/ Placement ID": "Video4",
        // "Content Name": "Showname",
        "Unsafe Category": "Hate Speech",
        "AD-Request Count-Blocked": "3,423",
    },
     {
        "Video ID/ Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Unsafe Category": "CategoryABC",
        "AD-Request Count-Blocked": "35,423",
    },
]
      const safereportData =[
    {
        "Video ID/ Placement ID": "Video1",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impression Served": "2,323",
    },
     {
        "Video ID/ Placement ID": "Video2",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impression Served": "23",
    },
    {
        "Video ID/ Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impression Served": "423",
    },
      {
        "Video ID/ Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": " Safe",
        "Impression Served": "3,423",
    },
     {
        "Video ID/ Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe",
        "Impression Served": "35,423",
    },
]
    const BrandreportData =[
    {
        "Video ID/ Placement ID": "Video1",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "2,323",
    },
     {
        "Video ID/ Placement ID": "Video2",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "23",
    },
    {
        "Video ID/ Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "423",
    },
      {
        "Video ID/ Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "3,423",
    },
     {
        "Video ID/ Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "35,423",
    },
     {
        "Video ID/ Placement ID": "Video3",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "423",
    },
      {
        "Video ID/ Placement ID": "Video4",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "3,423",
    },
     {
        "Video ID/ Placement ID": "Video4X",
        // "Content Name": "Showname",
        "Category": "Safe but Unsuitable",
        "Impression Served": "35,423",
    },
]
const filter = useWastageFilters(params, query);

const handleFilterChange = useFilterChangeHandler(
  loadedFilter,
  setQuery,
  setLoadedFilter
);
  return (
    <div className=" w-full grid grid-col gap-2 p-2 ">
       <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
                    <Filter filter={filter} onChange={handleFilterChange} />
                    </div>
        <SummaryCardGroup/>
        <div className="gap-1 w-full"> 
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Unsafe Video ID / Placement ID</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={UnsafeHeader}
     data={UnsafereportData}
     isSearchable={true}
     isUserTable={false}
     height={410}
     row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
       <div className="gap-1 w-full"> 
     <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 safe Video ID / Placement ID</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={SafeHeader}
     data={safereportData}
     isSearchable={true}
     isUserTable={false}
     height={410}
      row_count={5}
     row_height={10}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
    <div className="gap-1 w-full"> 
     <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Top 50 Brand Unsuitability</div>
  <div className="grid grid-cols-1 w-full">
    <ResizableTable
     isPaginated={true}
     columns={BrandHeader}
     data={BrandreportData}
     isSearchable={true}
     isUserTable={false}
      row_count={5}
     row_height={10}
     height={410}
     setSearchTerm={setSearchTerm}
     SearchTerm={sercterm}
    />
    </div>
    </div>
    </div>
  )
}

export default Brand_safety;
