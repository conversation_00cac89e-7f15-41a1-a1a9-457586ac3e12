{"name": "gui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.62.8", "axios": "^1.7.9", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dom-to-image": "^2.6.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "formik": "^2.4.6", "input-otp": "^1.2.4", "jszip": "^3.10.1", "lucide-react": "^0.441.0", "next": "^14.2.24", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-hook-form": "^7.56.1", "react-icons": "^5.3.0", "react-qr-code": "^2.0.18", "react-query": "^3.39.3", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0", "zod": "^3.24.3"}, "devDependencies": {"@chromatic-com/storybook": "^1.9.0", "@storybook/addon-essentials": "^8.3.1", "@storybook/addon-interactions": "^8.3.1", "@storybook/addon-links": "^8.3.1", "@storybook/addon-onboarding": "^8.3.1", "@storybook/blocks": "^8.3.1", "@storybook/nextjs": "^8.3.1", "@storybook/react": "^8.3.1", "@storybook/test": "^8.3.1", "@types/dom-to-image": "^2.6.7", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-resizable": "^3.0.8", "ajv": "^8.17.1", "dotenv-cli": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.12", "eslint-plugin-storybook": "^0.8.0", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "storybook": "^8.3.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}