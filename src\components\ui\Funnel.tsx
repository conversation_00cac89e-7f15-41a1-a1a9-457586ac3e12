'use client';
import React from "react";
import { Card, CardContent } from "./card";
import { Loader2 } from "lucide-react";

interface FunnelStepsProps {
  title?: string;
 subTitleData?: { label: string; value: string }[];
 circleClassName?: string; 
  circleWidth?: number;
  circleHeight?: number;
  circleBgColor?: string;
  circleShadowColor?: string;
  textSize?: string;
  textColor?: string;
  arrowWidth?: number;
  arrowHeight?: number;
  arrowShadowColor?: string;
  cardquartilename?:string;
  cardquartilesubtitle?:string;
  isLoading?:boolean;
  leftClassName?:string;
}

const FunnelStep: React.FC<FunnelStepsProps> = ({
  title = "",
  subTitleData = [],
   circleClassName = "w-[120px] h-[120px]",
  // circleWidth = 120,
  // circleHeight = 120,
  circleBgColor = "#165a71",
  circleShadowColor = "rgba(22,90,113,0.18)",
  textSize = "16px",
  textColor = "#ffffff",
  arrowWidth = 300,
  arrowHeight = 110,
  arrowShadowColor = "5px 5px #fefefe",
  cardquartilename="",
cardquartilesubtitle="",
leftClassName="left-8",
isLoading=false,
}) => {
  return (
    <div className="flex flex-col items-center gap-2">
      {/* Circle + Arrow */}
      <div className="flex items-center relative w-full p-2">
        {/* Circle */}
        <div
          className={`flex items-center text-body lg:text-small-font justify-center rounded-full font-semibold z-10 ${circleClassName} ${leftClassName}`}
          style={{
            // width: `${circleWidth}px`,
            // height: `${circleHeight}px`,
            backgroundColor: circleBgColor,
            color: textColor,
            //fontSize: textSize,
            position: "relative",
           // left: "32px",
            boxShadow: `0 6px 24px 0 ${circleShadowColor}`,
            border: "4px solid #fff",
          }}
        >
          {isLoading?(<Loader2 className="h-8 w-8 animate-spin text-primary" />):(
            title
          )}
        </div>

        {/* Arrow */}
        <svg
          width={arrowWidth}
          height={arrowHeight}
          viewBox="0 0 320 110"
          className="ml-[-64px] z-0"
          style={{
            filter: `drop-shadow(${arrowShadowColor})`,
            marginLeft: "-40px",
          }}
        >
          <defs>
            <linearGradient id="arrowGradient" x1="0" y1="0" x2="1" y2="0">
              <stop offset="0%" stopColor="var(--gradient-start)" />
              <stop offset="100%" stopColor="var(--gradient-end)" />
            </linearGradient>
          </defs>
          <polygon
            points="0,0 250,0 320,55 250,110 0,110 40,55"
            fill="url(#arrowGradient)"
            stroke="#e3e7ea"
            strokeWidth="10"
          />
         <text
  x="100"
  y="55"
  textAnchor="start"
  fontSize="14px"
  fontWeight="500"
  fill="var(--svg-text-color)"
  alignmentBaseline="middle"
  style={{ fontFamily: "inherit" }}
  className="dark:text-white "
>
  {isLoading ? (
    <tspan x="170" dy="0">
      <foreignObject x="160" y="55" width="20" height="20">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </foreignObject>
    </tspan>
  ) : (
    subTitleData?.map((line, index) => (
      <tspan  className="font-semibold text-body"key={index} x="100" dy={index === 0 ? 0 : 24}>
        {`${line.label}: ${line.value}`}
      </tspan>
    ))
  )}
</text>

        </svg>
      </div>

      {/* Card - Centered */}
      {cardquartilename && cardquartilesubtitle && (
     <Card 
      className="
    h-24 
    w-full max-w-[140px] sm:max-w-[160px] md:max-w-[180px] lg:max-w-[200px] 
    border-2 shadow-md grid justify-center bg-white items-center
  "
>
  {isLoading ? (
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  ) : (
    <CardContent>
      <div className="text-center">
        <div className="text-sm sm:text-body md:text-body font-semibold text-gray-900 dark:text-white">
          {cardquartilename} : 
          <span className="text-body sm:text-body p-1">{cardquartilesubtitle}</span>
        </div>
      </div>
    </CardContent>
  )}
</Card>

      )}
    </div>
  );
};

export default FunnelStep;
