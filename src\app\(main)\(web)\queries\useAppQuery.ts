import { useQuery, useMutation, UseQueryOptions, UseMutationOptions, UseMutationResult } from '@tanstack/react-query';
import axios, { AxiosError, AxiosResponse } from 'axios';
import apiClient from '../ApiClient/page';
import { UnauthorizedError } from '@/common/errors';

const getToken = () =>
  typeof window !== 'undefined' ? localStorage.getItem('IDToken') || '' : '';

const handleUnauthorized = () => {
  const currentPath = window.location.pathname;
  if (currentPath !== '/') localStorage.setItem('redirectPath', currentPath);
  localStorage.clear();
  localStorage.clear();
  window.location.href = '/';
};

// ✅ API Call Wrapper
export const apiCall = async ({
  url,
  method,
  data,
  headers = {},
  params,
}: {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  headers?: Record<string, string>;
  params?: any;
}) => {
  const token = getToken();
  try {
    const response = await apiClient({
      url,
      method,
      data,
      params,
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
        ...headers,
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError && error.response?.status === 401) {
      handleUnauthorized();
      throw new UnauthorizedError('Session expired');
    }
    throw error;
  }
};

// ✅ Generic Query Hook
export const useAppQuery = <TData = any>(
  key: string[],
  fetchFn: () => Promise<TData>,
  options?: Partial<UseQueryOptions<TData>>
) => {
  return useQuery<TData>({
    queryKey: key,
    queryFn: fetchFn,
    retry: (failureCount, error: any) => {
      if (error instanceof AxiosError && error.response?.status === 401) return false;
      return failureCount < 3;
    },
    ...options,
  });
};

// ✅ Generic Mutation Hook

// ✅ Fixed useAppMutation
export function useAppMutation<
  TData = unknown,
  TError = unknown,
  TVariables = void,
  TContext = unknown
>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseMutationOptions<TData, TError, TVariables, TContext>
): UseMutationResult<TData, TError, TVariables, TContext> {
  return useMutation<TData, TError, TVariables, TContext>({
    mutationFn,
    ...options,
  });
}
