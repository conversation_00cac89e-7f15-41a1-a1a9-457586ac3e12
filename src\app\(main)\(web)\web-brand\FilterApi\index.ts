import Endpoint from '../../common/endpoint';
import { apiCall, useAppQuery } from '../../queries/useAppQuery';


// ---------- Utility Functions ----------

type FilterApiParams = {
  package_name: string;
  start_date: string;
  end_date: string;
};
type summaryApiParams = FilterApiParams & {
   package_name: string;
  start_date: string;
  end_date: string;
  publisher?: string[];
  campaign?: string[];
  channel?: string[];
  fraud_category?: string[];
  fraud_sub_category?: string[];
  creative_id?: string[];
  sub_publisher?: string[];
  campaign_id?: string[];
};


const getDefaultPayload = (params: FilterApiParams) => ({
   package_name: params.package_name,
  start_date: params.start_date,
  end_date: params.end_date,
  
});
const getSummaryPayload = (params: summaryApiParams) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
});
{/* ------------api query------- */}
export const usePublishersFilter = (params: Omit<FilterApiParams, 'publishers'>, enabled = true) =>
  useAppQuery(
    ['publishersFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.PUBLISHERS}`,
        method: 'POST',
        data: getDefaultPayload(params),
      }),
    { enabled }
  );

  export const usePublisherIdFilter = (params: Omit<FilterApiParams, 'sub_publishers'>, enabled = true) =>
  useAppQuery(
    ['publisherIdFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.PUBLISHER_ID}`,
        method: 'POST',
        data: getDefaultPayload(params),
      }),
    { enabled }
  );
  export const useCampaignsFilter = (params: Omit<FilterApiParams, 'campaigns'>, enabled = true) =>
  useAppQuery(
    ['campaignsFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.CAMPAIGNS}`,
        method: 'POST',
       data: getDefaultPayload(params),
      }),
    { enabled }
  );

export const useChannelsFilter = (params: Omit<FilterApiParams, 'channel'>, enabled = true) =>
  useAppQuery(
    ['channelsFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.CHANNELS}`,
        method: 'POST',
       data: getDefaultPayload(params),
      }),
    { enabled }
  );
  export const usefraudcategoryFilter = (params: Omit<FilterApiParams, 'fraudcategory'>, enabled = true) =>
  useAppQuery(
    ['fraudcategoryFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.WASTAGE_CATEGORY}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  export const useSubfraudcategoryFilter = (params: Omit<FilterApiParams, 'subfraudcategory'>, enabled = true) =>
  useAppQuery(
    ['subfraudcategoryFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.WASTAGE_SUB_CATEGORY}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
export const useCreativeIdFilter = (params: Omit<FilterApiParams, 'creative_id'>, enabled = true) =>
  useAppQuery(
    ['creativeIdFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.CREATIVE_ID}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );

export const useCampaignIdFilter = (params: Omit<FilterApiParams, 'campaign_id'>, enabled = true) =>
  useAppQuery(
    ['campaignIdFilter', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.CAMPAIGNS_ID}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
