import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';

const getDefaultPayload = (params: any) => ({
  ...params,
publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
});



  export const useFraudCategoryWiseTraffic = (params: any, enabled = true) =>
  useAppQuery(
    ['fraudCategoryWiseTraffic', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.FRAUD_CATEGORY_WISE_TRAFFIC}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );

  export const useDeviceTypeWiseTraffic = (params: any, enabled = true) =>
  useAppQuery(
    ['deviceTypeWiseTraffic', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.DEVICE_TYPE_WISE_TRAFFIC}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  export const useSourceWiseTraffic = (params: any, enabled = true) =>
  useAppQuery(
    ['sourceWiseTraffic', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.SOURCE_WISE_TRAFFIC}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  