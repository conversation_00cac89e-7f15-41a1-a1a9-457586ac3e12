type QueryType = Record<string, string[] | undefined>;
import { useCallback } from 'react';



export const buildFilter = ({
  data,
  queryKey,
  query,
  loading,
}: {
  data: string[] | undefined;
  queryKey: keyof QueryType;
  query: QueryType;
  loading: boolean;
}) => {
  return {
    filters:
      data?.map((item: string) => ({
        label: item,
        checked:
          query[queryKey]?.includes("all") ||
          query[queryKey]?.includes(item) ||
          !query[queryKey],
      })) || [],
    is_select_all:
      !query[queryKey] ||
      query[queryKey]?.includes("all") ||
      query[queryKey]?.length === data?.length,
    selected_count: query[queryKey]?.includes("all")
      ? data?.length ?? 0
      : query[queryKey]?.length ?? data?.length ?? 0,
    loading,
  };
};
export type FilterState = Record<
  string,
  {
    is_select_all: boolean;
    filters: { label: string; checked: boolean }[];
  }
>;

export const buildSelected = (state: FilterState, key: string) =>
  state[key]?.is_select_all
    ? ['all']
    : state[key]?.filters
        .filter((f) => f.checked)
        .map((f) => f.label);

export const handleFilterPayload = (state: FilterState) => ({
  publisher: buildSelected(state, 'Publishers'),
  sub_publisher: buildSelected(state, 'Sub Publishers'),
  campaign: buildSelected(state, 'Campaigns'),
  channel: buildSelected(state, 'Channels'),
  fraud_category: buildSelected(state, 'Wastage Category'),
  fraud_sub_category: buildSelected(state, 'Wastage Sub-Category'),
  creative_id: buildSelected(state, 'Creative ID'),
  publisher_ids: buildSelected(state, 'Publisher ID'),
  campaign_id: buildSelected(state, 'Campaigns ID'),
});
 
 
export const hasFiltersChanged = (
  newState: FilterState,
  prevState: FilterState
) =>
  ['Publishers', ' Publishers ID', 'Campaigns', 'Campaigns ID','Channels', 'Creative ID ','Wastage Category','Wastage Sub-Category'].some(
   
    (key) =>
      !deepEqual(newState[key]?.filters || [], prevState[key]?.filters || [])
  );

  export const useFilterChangeHandler = (
  loadedFilter: FilterState,
  setQuery: (payload: any) => void,
  setLoadedFilter: (state: FilterState) => void
) => {
  return useCallback(
    (newState: FilterState) => {
      const payload = handleFilterPayload(newState);
      //console.log(payload,"87eydhsddddddddddddddkj");
      setQuery(payload);

      if (hasFiltersChanged(newState, loadedFilter)) {
        setLoadedFilter(newState);
      }
    },
    [loadedFilter, setQuery, setLoadedFilter]
  );
};

function deepEqual(a: any, b: any) {
  return JSON.stringify(a) === JSON.stringify(b);
}
