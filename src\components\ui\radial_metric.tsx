"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import CircularProgress from "./Circular_progress";

interface Radialmetric{
    Radialname?:string;
    target?:number;
    value?:number;
    color?:string;
    showTime?:boolean;
    maxValue?:number;

}

const RadialMetricsDesign: React.FC<Radialmetric> = ({
Radialname="",
target=100,
value=0,
color="#ff00ff",
showTime=false,
maxValue=100,
})=>{  



  return (
   <div className="w-full rounded-xl shadow-md bg-gradient-to-br from-slate-50 to-blue-50 dark:bg-background">
  <Card
    className="w-full h-full border-0 p-0 rounded-xl dark:bg-card shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm flex flex-col"
  >
    <CardHeader className="p-2 h-7 text-center pb-1">
      <CardTitle className="text-body font-semibold ">{Radialname}</<PERSON><PERSON><PERSON>le>
    </CardHeader>
    <CardContent className="flex justify-center pb-2 flex-grow">
      <CircularProgress
        value={value}
        target={target}
        color={color}
        showTime={showTime}
        maxValue={maxValue}
      />
    </CardContent>
  </Card>
</div>
  )
}
export default RadialMetricsDesign;