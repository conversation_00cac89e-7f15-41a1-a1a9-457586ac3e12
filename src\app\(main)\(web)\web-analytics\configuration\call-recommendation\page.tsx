"use client";
 
import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ChevronDown, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import Endpoint from "../../../common/endpoint";
import { useApiCall } from "../../../queries/api_base";
import { usePackage } from "@/components/mf/PackageContext";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
 
interface RuleUpdatePayload {
  rules: {
    [key: string]: string;
  }[];
}
 
interface UpdateRulePayload {
  package_name: string;
  rule_name: string;
  new_call_recommendation: string;
}
 
const CallRecommendationUI = () => {
  const { selectedPackage } = usePackage();
  const { toast } = useToast();
  const [selectedItems, setSelectedItems] = useState<Record<string, Record<string, boolean>>>({
    Low: {},
    Medium: {},
    High: {},
  });
  const [allItems, setAllItems] = useState<{ label: string; id: string }[]>([]);
  const [pendingMoves, setPendingMoves] = useState<Array<{
    ruleId: string;
    fromLevel: string;
    toLevel: string;
  }>>([]);
  const [isPopoverOpen, setIsPopoverOpen] = useState<Record<string, boolean>>({
    Low: false,
    Medium: false,
    High: false,
  });
  const [tempSelections, setTempSelections] = useState<Record<string, Record<string, boolean>>>({
    Low: {},
    Medium: {},
    High: {},
  });
  const [isUpdating, setIsUpdating] = useState(false);
 
  // api call to get the recommendation rules
  const { result: apiCall, loading: isLoadingGet } = useApiCall<RuleUpdatePayload>({
    url: process.env.NEXT_PUBLIC_CONFIGURATION + Endpoint.CALL_RECOMMENDATION_GET,
    method: "POST",
    manual: true,
    onSuccess: (data) => {
      if (data?.rules && Array.isArray(data.rules)) {
        // Update allItems
        const items = data.rules.map((ruleItem) => {
          const ruleName = Object.keys(ruleItem)[0];
          return { label: ruleName, id: ruleName };
        });
        setAllItems(items);
 
        // Update selectedItems based on initial values
        const initial: Record<string, Record<string, boolean>> = {
          Low: {},
          Medium: {},
          High: {},
        };
 
        data.rules.forEach((ruleItem) => {
          const ruleName = Object.keys(ruleItem)[0];
          const level = ruleItem[ruleName];
          if (["Low", "Medium", "High"].includes(level)) {
            initial[level][ruleName] = true;
          }
        });
 
        setSelectedItems(initial);
      }
    },
    onError: (error) => {
      console.error("Error fetching recommendations:", error);
      toast({
        title: "Error",
        description: "Failed to fetch recommendation rules",
        variant: "destructive",
      });
    }
  });
 
  useEffect(() => {
    if (selectedPackage) {
      const payload = { package_name: selectedPackage };
 
      apiCall.mutate(payload);
 
    }
  }, [selectedPackage]);
 
  // api call to update the recommendation rules
  const updateApiCall = useApiCall<RuleUpdatePayload>({
    url: process.env.NEXT_PUBLIC_CONFIGURATION + Endpoint.CALL_RECOMMENDATION_UPDATE,
    method: "POST",
    manual: true,
    onSuccess: (data) => {
      console.log("API Response:", data);
      toast({
        title: "Success",
        description: "Recommendation rules updated successfully",
      });
      // Refresh recommendations after successful update
 
      apiCall.mutate({ package_name: selectedPackage });
 
    },
    onError: (error) => {
      console.error("Error updating recommendations:", error);
      toast({
        title: "Error",
        description: "Failed to update recommendation rules",
        variant: "destructive",
      });
    }
  });
 
  const groupedData = ["Low", "Medium", "High"].map((level) => ({
    level,
    items: allItems,
  }));
 
  const handleMoveConfirmation = async () => {
    if (!pendingMoves.length || !selectedPackage || updateApiCall.type !== "mutation") return;
 
    setIsUpdating(true);
 
    try {
      // Update local state for all pending moves
      setSelectedItems((prev) => {
        const newState = { ...prev };
        pendingMoves.forEach(({ ruleId, fromLevel, toLevel }) => {
          // Remove from old level
          newState[fromLevel] = {
            ...newState[fromLevel],
            [ruleId]: false,
          };
          // Add to new level
          newState[toLevel] = {
            ...newState[toLevel],
            [ruleId]: true,
          };
        });
        return newState;
      });
 
      // Call update API for each move
      const updatePromises = pendingMoves.map(({ ruleId, toLevel }) => {
        const payload: UpdateRulePayload = {
          package_name: selectedPackage,
          rule_name: ruleId,
          new_call_recommendation: toLevel
        };
        return updateApiCall.result.mutateAsync(payload);
      });
 
      await Promise.all(updatePromises);
 
      // Clear pending moves and close all popovers
      setPendingMoves([]);
      setTempSelections({
        Low: {},
        Medium: {},
        High: {},
      });
      setIsPopoverOpen({
        Low: false,
        Medium: false,
        High: false,
      });
    } catch (error) {
      console.error("Error updating recommendations:", error);
    } finally {
      setIsUpdating(false);
    }
  };
 
  const toggleSelection = (currentLevel: string, id: string) => {
    // Update temporary selections
    setTempSelections((prev) => ({
      ...prev,
      [currentLevel]: {
        ...prev[currentLevel],
        [id]: !prev[currentLevel]?.[id],
      },
    }));
  };
 
  const handlePopoverClose = (level: string) => {
    // Find all conflicts for the current level
    const conflicts: Array<{ ruleId: string; fromLevel: string; toLevel: string }> = [];
 
    Object.entries(tempSelections[level] || {}).forEach(([ruleId, isSelected]) => {
      if (isSelected) {
        Object.entries(selectedItems).forEach(([otherLevel, items]) => {
          if (otherLevel !== level && items[ruleId]) {
            conflicts.push({
              ruleId,
              fromLevel: otherLevel,
              toLevel: level
            });
          }
        });
      }
    });
 
    if (conflicts.length > 0) {
      setPendingMoves(conflicts);
    } else {
      // If no conflicts, just update the selections
      setSelectedItems((prev) => ({
        ...prev,
        [level]: {
          ...prev[level],
          ...tempSelections[level]
        }
      }));
    }
 
    // Close the popover
    setIsPopoverOpen((prev) => ({
      ...prev,
      [level]: false,
    }));
  };
 
  const handlePopoverOpen = (level: string) => {
    // Initialize temp selections with current selections
    setTempSelections((prev) => ({
      ...prev,
      [level]: { ...selectedItems[level] }
    }));
    setIsPopoverOpen((prev) => ({
      ...prev,
      [level]: true,
    }));
  };
 
 
 
  return (
 
    <div className="w-full max-w-4xl mx-auto py-10">
     
          <Card className="border p-4">
            <CardTitle className="mb-4 text-xl font-semibold">
              Call Recommendation Rules
            </CardTitle>
            <CardContent className="space-y-6">
              {groupedData.map((section, index) => (
                <div key={section.level}>
                  <div className="flex items-center gap-4 mb-4">
                    <h3 className="text-lg font-medium text-gray-700 min-w-[100px]">
                      {section.level}
                    </h3>
                    <Popover
                      open={isPopoverOpen[section.level]}
                      onOpenChange={(open) => {
                        if (open) {
                          handlePopoverOpen(section.level);
                        } else {
                          handlePopoverClose(section.level);
                        }
                      }}
                    >
                      <PopoverTrigger asChild>
                        <Button variant="outline">
                          Select Rules <ChevronDown className="ml-2 h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full max-w-xl max-h-96 overflow-auto">
                        {isLoadingGet ? (
                          <div className="flex justify-center items-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-primary" />
                          </div>
                        ) : section.items.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">
                            No rules available
                          </div>
                        ) : (
                          <>
                            <div className="grid grid-cols-2 gap-2 p-2">
                              {section.items.map((item) => (
                                <div key={item.id} className="flex items-center gap-2">
                                  <Checkbox
                                    id={`${section.level}-${item.id}`}
                                    checked={!!tempSelections[section.level]?.[item.id]}
                                    onCheckedChange={() =>
                                      toggleSelection(section.level, item.id)
                                    }
                                  />
                                  <label
                                    htmlFor={`${section.level}-${item.id}`}
                                    className="text-sm"
                                  >
                                    {item.label}
                                  </label>
                                </div>
                              ))}
                            </div>
                            <div className="flex justify-end mt-4">
                              <Button
                             
                                onClick={() => handlePopoverClose(section.level)}
                                className="text-white bg-primary hover:bg-primary"
                              >
                                Save
                              </Button>
                            </div>
                          </>
                        )}
                      </PopoverContent>
                    </Popover>
                  </div>
 
                  <div className="flex flex-wrap gap-2 mb-4">
                    {section.items
                      .filter((item) => selectedItems[section.level]?.[item.id])
                      .map((item) => (
                        <span
                          key={item.id}
                          className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded"
                        >
                          {item.label}
                        </span>
                      ))}
                  </div>
 
                  {index < groupedData.length - 1 && (
                    <hr className="border-t border-gray-300 my-6" />
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
 
 
 
 
          {/* Confirmation Dialog */}
          <Dialog open={pendingMoves.length > 0} onOpenChange={(open) => !open && setPendingMoves([])}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Move Rules to Different Level</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                {pendingMoves.length > 0 && (
                  <>
                    Are you sure you want to move the following rules?
                    <ul className="mt-2 space-y-2">
                      {pendingMoves.map((move, index) => (
                        <li key={index} className="text-sm">
                          • "{move.ruleId}" from{" "}
                          <span className="font-semibold">{move.fromLevel}</span> to{" "}
                          <span className="font-semibold">{move.toLevel}</span>
                        </li>
                      ))}
                    </ul>
                  </>
                )}
              </div>
              <DialogFooter>
                <Button
 
                  onClick={() => setPendingMoves([])}
                  className="text-white bg-primary hover:bg-primary"
                  disabled={isUpdating}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleMoveConfirmation}
                  className="text-white bg-primary hover:bg-primary"
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Confirm Move"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
 
          {/* Toast Renderer */}
          <Toaster />
     
 
    </div>
  );
};
 
export default CallRecommendationUI;