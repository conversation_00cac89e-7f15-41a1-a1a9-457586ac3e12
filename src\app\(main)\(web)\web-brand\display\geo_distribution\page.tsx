'use client'
import React from 'react';
import SummaryCardGroup from '../common_module/page';
import DoubleLine<PERSON>hart from '@/components/mf/charts/DoubleLineChart';
import { onExpand, downloadURI, debounce, handleExportData } from "@/lib/utils";
import domToImage from "dom-to-image";
import { useCallback, useRef, useState,useEffect } from 'react';
import { Card } from '@/components/ui/card';
import ResizableTable from '@/components/mf/ReportingToolTable';
import { Filter } from "@/components/mf/Filters";
import Endpoint from '../../../common/endpoint';
import { useExportCsv } from '@/lib/Exportdata';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import {
  buildFilter,
  useFilterChangeHandler,
  FilterState,
} from '../Filters/buildFilters';
import { useWastageFilters } from '../../Filters/useFilters'
import { useDayWiseTrend,useGeoDistribution } from './apicallPage';
import { useDebounce } from '@/hooks/useDebounce';


const Geo_distribution = () => {
  const cardRefs = useRef<Record<string, HTMLElement | null>>({});
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const [selectedFrequency, setSelectedFrequency] = useState("date");
  const { selectedPackage } = usePackage();
  const [pageNo, setPageNo] = useState(1);
  const { startDate, endDate } = useDateRange();
  const [serchterm, setSearchTerm] = useState('');
  const [limit, setLimit] = useState(10);
  const [isExporting, setIsExporting] = useState(false);
    const [exportType, setExportType] = useState<string | null>(null);
    const [loadedFilter, setLoadedFilter] = useState<FilterState>({});
  const debouncedSearchTerm = useDebounce(serchterm, 500); // 300ms delay
    const params = {
      package_name: selectedPackage,
      start_date: startDate,
      end_date: endDate,
    };
  
    const [query, setQuery] = useState({
      publisher: ["all"],
      campaign: ["all"],
      channel: ["all"],
      fraud_category:["all"],
      fraud_sub_category:["all"],
      creative_id:["all"],
      sub_publisher:["all"],
      campaign_id:["all"],
    });
      const daywiseparams={
      ...params,
      ...query,
      frequency:selectedFrequency
    }
    const geodistributionparams={
      ...params,
      ...query,
      search_term:debouncedSearchTerm,
      // limit:limit,
      // page:pageNo,
    }

    const isReady =
  !!selectedPackage && !!startDate && !!endDate;
    const { data: daywise, isLoading: daywiseLoading, error: daywiseError } = useDayWiseTrend(daywiseparams,isReady||exportType !== 'daywise');
    const { data: geodistribution, isLoading: geodistributionLoading, error: geodistributionError } = useGeoDistribution(geodistributionparams,isReady||exportType !== 'geodistribution');
  
  const onExport = useCallback(
    async (s: string, title: string, key: string) => {
      const ref = cardRefs.current[key];
      if (!ref) return;

      switch (s) {
        case "png":
          const screenshot = await domToImage.toPng(ref);
          downloadURI(screenshot, title + ".png");
          break;
        default:
      }
    },
    []
  );
  const handleExpand = (key: string) => {
    onExpand(key, cardRefs, expandedCard, setExpandedCard);
  };

  const geochartData = daywise?.map((item: any) => ({
   label: item.label,
   total_count: item.total_count,
  invalid_traffic_percentage: parseFloat(item.invalid_traffic_percentage.replace('%', '')),
  })) ?? [];
  
  const handleFrequencyChange = (value: string) => {
    const frequencyMap: { [key: string]: string } = {
      Daily: "date",
      Weekly: "week",
      Monthly: "month",
      Yearly: "year",
    };
    const mappedFrequency = frequencyMap[value];
    setSelectedFrequency(mappedFrequency);
  };
  const geochartConfig = {
    total_count: {
      label: "Impression Counts",
      color: "#093FB4",
    },
    invalid_traffic_percentage: {
      label: "Invalid %",
      color: "#b91c1c",
    },
  }
  const selectOptions = ["Daily", "Weekly", "Monthly", "Yearly"];

  const GeoHeader = [
    { title: "State", key: "Country" },
    { title: "Impression Counts", key: "Impression Counts" },
    { title: "% Distribution", key: "% Distribution" },
    { title: "IVT %", key: "IVT %" },
   // { title: "Brand Unsafe %", key: "Brand Unsafe %" },
  ]
  const GeoreportData = geodistribution?.map((item: any) => ({
    "Country": item.imp_country,
    "Impression Counts": item.impression_counts,
    "% Distribution": item.distribution_pct,
    "IVT %": item.ivt_pct,
  })) ?? [];
 
 const filter = useWastageFilters(params, query);
 
  const handleFilterChange = useFilterChangeHandler(
    loadedFilter,
    setQuery,
    setLoadedFilter
  );
   const handleExportClick = async (type: string) => {
    setExportType(type as any);
    setIsExporting(true);
  };
    useExportCsv({
      exportParams: params,
      queryParams: query,
      exportType,
      setExportType,
      isExporting,
      setIsExporting,
      endpointMap: {
        impression: Endpoint.WebBrand.DISPLAY_DAY_WISE_TREND,
        location: Endpoint.WebBrand.LOCATION_WISE_TRAFFIC,
        
      },
      baseUrlMap: {
        impression: process.env.NEXT_PUBLIC_WEB_BRAND!,
        location: process.env.NEXT_PUBLIC_WEB_BRAND!,
       
      },
    });
  return (
    <div className=" w-full grid grid-col p-2 gap-2">
      <div className=" sticky top-0 z-50 sm:w-full flex flex-cols-3 w-full flex-wrap items-center justify-start gap-4 rounded-md bg-background px-5">
        <Filter filter={filter} onChange={handleFilterChange} />
      </div>
      <SummaryCardGroup params={params} query={query}/>
      <div className="gap-1 w-full">
        <div className='w-full bg-gray-200 text-sub-header font-semibold grid justify-items-center sm:text-body p-2'>Trend and Geo Distribution</div>
        <div className="grid grid-cols-1 w-full gap-2 ">
          <Card ref={(el) => (cardRefs.current["geo_distribution"] = el!)} className='p-2'>
            <DoubleLineChart
              title="Traffic Trends"
              onExport={() => onExport("png", "Trend and Geo Distribution", "geo_distribution")}
              onExpand={() => handleExpand("geo_distribution")}
              handleExport={() => {
                handleExportClick("impression");
              }}
              isexportcsv={true}
              chartConfig={geochartConfig}
              isLoading={daywiseLoading}
              chartData={geochartData}
              isSelect
              selectoptions={selectOptions}
              placeholder='Daily'
              YAxis1={{
                yAxisId: "right",
                orientation: "right",
                stroke: "hsl(var(--chart-3))",
                title: "Invalid %",

              }}
              YLeftAxis={{
                title: " Impression counts"
              }}
              rightAxisKeys={["invalid_traffic_percentage"]}
              selectedFrequency={selectedFrequency}
              handleFrequencyChange={handleFrequencyChange}
              titley1_color="#b91c1c"
              titley_color="#093FB4"
              yAxisXOffset={-20}
              yAxisXOffsetFullscreen={-35}
              datalength={10}
              leftmargin={30}
              rightmargin={20}
              yAxisPercentage={true}
              PercentageLabel="invalid_traffic_percentage"
              isLegend={false}
              CustomLegend={true}
            />
          </Card>
        </div>
        <div className="grid grid-cols-1 w-full gap-2 pt-2">
          <ResizableTable
            isPaginated={true}
            columns={GeoHeader}
            data={GeoreportData}
            isSearchable={true}
            setSearchTerm={setSearchTerm}
            SearchTerm={serchterm}
            isUserTable={false}
            isLoading={geodistributionLoading}
            isTableDownload={true}
            onDownload={() => handleExportClick("location")}
            height={410}
            row_count={5}
            row_height={10}
          />
        </div>
         <div className="grid grid-cols-1 w-full gap-2 pt-2">
          <Card className='p-2 shadow-md text-xs'>
            ^Geo Report is for Directional purpose only ,not for reconciliation
              </Card>
         </div>
      </div>
    </div>
  )
}

export default Geo_distribution