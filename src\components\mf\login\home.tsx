"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";

interface HomeProps {
  logoUrl: string;
  logoSize: string;
  InfoText: string;
  children: React.ReactNode;
}

const Home: React.FC<HomeProps> = ({
  children,
  logoSize,
  logoUrl,
  InfoText,
}) => {
  return (
    <HomeBackground
      logo={
        <img src={logoUrl} alt="Logo" className={`${logoSize} p-2 md:p-0`} />
      }
    >
      <div className="flex flex-col   justify-center items-center md:items-start py-10  md:w-7/12">
        <div className="rounded-lg bg-primary p-4 text-center">
          <p className="text-md  hover:text-white text-white px-4 text-justify">{InfoText}</p>
          <a
            href="https://www.mfilterit.com/"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button
              variant="secondary"
              className="mt-2 p-2 font-bold text-white rounded-full  hover:text-white"
            >
              Our Products
            </Button>
          </a>
        </div>
      </div>
      {children}
    </HomeBackground>
  );
};

export default Home;

export const HomeBackground: React.FC<{
  children: React.ReactNode;
  logo: React.ReactNode;
}> = ({ children, logo }) => {
  return (
    <>
      <div className="relative flex min-h-screen w-screen flex-col items-center  bg-secondary">
        <div className="absolute -bottom-16 -left-10 z-0 h-48 w-48 rounded-full bg-primary opacity-30"></div>
        <div className="absolute -right-10 -top-16 z-0 h-48 w-48 rounded-full bg-primary opacity-30"></div>
        <div className="absolute -bottom-16 right-5 z-0 h-64 w-64 rounded-lg bg-primary opacity-50"></div>
       <div className="z-10 flex w-full justify-start px-4 pt-4 md:px-5 md:pt-5">
          {logo}
        </div>

        <div className="z-10 flex w-full flex-1 flex-col items-center justify-center gap-10 px-10 md:flex-row md:justify-center md:items-center md:px-10">
          {children}
        </div>
      </div>
    </>
  );
};