"use client";
import { useFullscreen } from "@/hooks/use-fullscreen";
import React, { useEffect, useRef, useState, useMemo } from "react";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import {
  MdEdit,
  MdDelete,
  MdVisibility,
  MdFileDownload,
  MdArrowDropDown,
  MdSearch,
  MdRefresh,
  MdArrowDownward,
  MdArrowUpward,
  MdPause,
  MdPlayArrow,
  MdDownload,
  MdUnfoldMore,
} from "react-icons/md";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { FiRefreshCw } from "react-icons/fi";
import { FaClone } from "react-icons/fa";
import { IoIosSend } from "react-icons/io";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import Pagination from "@/components/ui/pagination";
import {
  Select,
  SelectItem,
  SelectContent,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Import ShadCN Select components
import { Loader2,CircleX ,X} from "lucide-react"; // Add this import
// import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { CalendarIcon, RefreshCw } from "lucide-react";
import JSZip from "jszip";
import { Button } from "@/components/ui/button";
import EllipsisTooltip from "./EllipsisTooltip";
import { Card, CardContent } from '@/components/ui/card';

export type Column<T = void> =
  | { title: string; key: string }
  | { title: string; key: string; render: (data: T) => React.ReactNode };

// Constants for fixed height calculation
const ROW_HEIGHT = 40; // Increased row height for better readability in non-dashboard mode
const DASHBOARD_ROW_HEIGHT = 20; // Smaller row height for dashboard
const HEADER_HEIGHT = 56; // Height of table header
const PAGINATION_HEIGHT = 36; // Height of pagination controls
const TABLE_CONTROLS_HEIGHT = 64; // Height of search/filter controls
const DEFAULT_ROWS = 15; // Default number of rows to show
const FIXED_ROW_COUNT = 10; // Number of rows to show before scrollbar

interface ResizableTableProps<T> {
  buttonTextName?: string;
  columns: Column<T>[];
  isbuttonText?: boolean;
  data: T[];
  headerColor?: string;
  isEdit?: boolean;
  isDelete?: boolean;
  isClone?: boolean;
  isRefetch?: boolean;
  isSend?: boolean;
  isView?: boolean;
  isDownload?: boolean;
  onRefetch?: (params?: { startDate?: Date; endDate?: Date }) => void;
  isPaginated?: boolean;
  SearchTerm?: string;
  setSearchTerm: (term: string) => void;
  isSearchable?: boolean;
  isSelectable?: boolean;
  isCount?: boolean;
  isLoading?: boolean;
  isFile?: boolean;
  actionButton?: React.ReactNode | React.ReactNode[];
  onEdit?: (item: T) => void;
  onDownloadAll?: (item: T[]) => void;
  onDelete?: (item: T) => void;
  onView?: (item: T) => void;
  onDownload?: (item: T) => void;
  onRefresh?: () => void;
  onSelect?: (selectedItems: T[]) => void;
  itemCount?: (count: number) => void;
  isPause?: boolean;
  isPlay?: boolean;
  onPause?: (item: T) => void;
  onPlay?: (item: T) => void;
  onClone?: (item: T) => void;
  onSend?: (item: T) => void;
  onGenerateReport?: () => void;
  height?: number;
  onPageChangeP?: (page: number) => void;
  onLimitChange?: (page: number) => void;
  pageNo?: any;
  totalPages?: number;
  isColumn?: boolean;
  isMultiLineHeader?: boolean;
  ischeckbx?: boolean;
  isPaginationBordered?: boolean;
  isPaginationStyled?: boolean;
  isDashboard?: boolean; // New prop to control row height
}

const DropdownMenu: React.FC<{
  columns: Column<Record<string, string | number>>[];
  onToggle: (key: string) => void;
  visibleColumns: Column<Record<string, string | number>>[];
}> = ({ columns, onToggle, visibleColumns }) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="flex w-28 items-center  text-small-font justify-between rounded border bg-card p-2 text-card-foreground">
          <span>Columns</span>
          <div className="ml-auto flex items-center space-x-2">
            {/* <div className="h-6 border-l-2 border-black" />  */}|
            <span className="ml-2 mt-1 text-sm text-primary text-small-font">
              {columns.length === visibleColumns.length
                ? "All"
                : visibleColumns.length}
            </span>
            <MdArrowDropDown className="ml-2" />
          </div>
        </button>
      </PopoverTrigger>
      <PopoverContent className="w-40">
        {columns.map((column) => (
          <div
            key={column.key}
            className="flex items-center px-4 py-2 text-small-font"
          >
            <Checkbox
              checked={visibleColumns.some((col) => col.key === column.key)}
              onCheckedChange={() => onToggle(column.key)}
            />
            <span className="ml-2">{column.title}</span>
          </div>
        ))}
      </PopoverContent>
    </Popover>
  );
};

const ResizableTable: React.FC<
  ResizableTableProps<Record<string, string | number>>
> = ({
  buttonTextName = "New Report",
  columns,
  data = [],
  headerColor = "#ccc",
  isEdit = false,
  isDelete = false,
  isClone = false,
  isRefetch = false,
  onRefetch,
  isSend = false,
  isView = false,
  isPaginated = true,
  isDownload = false,
  isSearchable = false,
  SearchTerm = "",
  setSearchTerm,
  isSelectable = false,
  isCount = false,
  isLoading = false,
  actionButton,
  onEdit,
  onDelete,
  isbuttonText = false,
  onView,
  onDownload,
  onSelect,
  onDownloadAll,
  onRefresh,
  itemCount,
  isPause = false,
  isPlay = false,
  onPause,
  onPlay,
  onClone,
  onSend,
  onGenerateReport,
  onPageChangeP,
  onLimitChange,
  isColumn = true,
  pageNo, // Total number of records
  totalPages = 1,
  height, // Accept the height prop
  isMultiLineHeader = false,
  ischeckbx = false,
  isPaginationBordered = false,
  isPaginationStyled = false,
  isDashboard = false
}) => {
    const [selectedItems, setSelectedItems] = useState<
      Record<string, string | number>[]
    >([]);
    const [isMounted, setIsMounted] = useState(false);
    const [visibleColumns, setVisibleColumns] =
      useState<Column<Record<string, string | number>>[]>(columns);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [columnWidths, setColumnWidths] = useState<{ [key: string]: number | string }>({});
    const tableRef = useRef<HTMLTableElement>(null);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const [sortConfig, setSortConfig] = useState<{
      key: string;
      direction: "asc" | "desc";
    } | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isRefetchModalOpen, setIsRefetchModalOpen] = useState(false);
    const [startDate, setStartDate] = useState<Date>();
    const [endDate, setEndDate] = useState<Date>();
    const displayData = isLoading ? [] : data; // Clear data when loading

    // Fullscreen state management
        const isFullscreen = useFullscreen();

    // Calculate table height based on props or default to 15 rows
    const calculateTableHeight = () => {
      if (isFullscreen) {
        // Calculate height for exactly 10 rows in fullscreen
        const fullscreenRowHeight = 45; // Height per row in fullscreen (decreased from 60)
        const fullscreenHeaderHeight = 80; // Header height in fullscreen
        const fullscreenPaginationHeight = 50; // Pagination height in fullscreen
        const fullscreenControlsHeight = 80; // Search/filter controls height in fullscreen
        const tableBodyHeight = fullscreenRowHeight * 10; // Height for exactly 10 rows
        
        return fullscreenHeaderHeight + tableBodyHeight + fullscreenPaginationHeight + fullscreenControlsHeight + 60; // Reduced buffer to 60px
      }
      if (isDashboard) {
        // Use dynamic height for dashboard mode
        if (height) {
          return height;
        }
        // Fixed height for dashboard mode regardless of data
        return HEADER_HEIGHT + (DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS);
      }
      // Fixed height for non-dashboard mode (like ReportingToolTable)
      return HEADER_HEIGHT + 700; // Fixed table body height of 700px
    };

    const tableHeight = calculateTableHeight();

    const calculateInitialColumnWidth = (
      columns: Column[],
      containerWidth: number
    ) => {
      // Remove fixed width calculations and let content determine width
      const initialWidths: { [key: string]: number | string } = {};
      columns.forEach((col) => {
        initialWidths[col.key] = 'auto'; // Set to auto to fit content
      });

      return initialWidths;
    };

    useEffect(() => {
      setIsMounted(true);
      const tableContainer = tableRef.current?.parentElement;
      const containerWidth = tableContainer?.clientWidth || 800; // Default width if container not found
      const initialWidths = calculateInitialColumnWidth(columns, containerWidth);
      setColumnWidths(initialWidths);
    }, [
      columns,
      tableRef,
      isSelectable,
      isEdit,
      isDelete,
      isView,
      isDownload,
      isPause,
      isPlay,
      isRefetch,
      isSend,
      isClone,
    ]);

    useEffect(() => {
      if (onPageChangeP) {
        onPageChangeP(currentPage); // page change handler
      }
    }, [currentPage]);

    useEffect(() => {
      if (onLimitChange) {
        onLimitChange(itemsPerPage); // limit change handler
      }
    }, [itemsPerPage]);

    const handleColumnToggle = (key: string) => {
      const newVisibleColumns = visibleColumns.some((col) => col.key === key)
        ? visibleColumns.filter((col) => col.key !== key)
        : [...visibleColumns, columns.find((col) => col.key === key)!];
      setVisibleColumns(newVisibleColumns);
    };

    const handleSort = (key: string) => {
      let direction: "asc" | "desc" = "asc";
      if (
        sortConfig &&
        sortConfig.key === key &&
        sortConfig.direction === "asc"
      ) {
        direction = "desc";
      }
      setSortConfig({ key, direction });
    };

    const sortedData = React.useMemo(() => {
      if (!Array.isArray(data)) {
        console.error("Data is not an array:", data);
        return []; // Return an empty array to prevent the error
      }
      const sortableData = Array.isArray(data) ? [...data] : [];
      if (sortConfig !== null) {
        sortableData.sort((a, b) => {
          if (a[sortConfig.key] < b[sortConfig.key]) {
            return sortConfig.direction === "asc" ? -1 : 1;
          }
          if (a[sortConfig.key] > b[sortConfig.key]) {
            return sortConfig.direction === "asc" ? 1 : -1;
          }
          return 0;
        });
      }
      return sortableData;
    }, [data, sortConfig]);

    const filteredData = React.useMemo(() => {
      if (!SearchTerm.trim()) return sortedData;

      return sortedData.filter((item) => {
        return visibleColumns.some((column) => {
          const cellValue = String(item[column.key] || "").toLowerCase();
          return cellValue.includes(SearchTerm.toLowerCase());
        });
      });
    }, [sortedData, visibleColumns, SearchTerm]);

    // const paginatedData = React.useMemo(() => {
    //    const startIndex = (currentPage - 1) * itemsPerPage;
    //    return filteredData.slice(startIndex, startIndex + itemsPerPage);
    //  }, [filteredData, currentPage, itemsPerPage]);
    // console.log(sortedData,filteredData,itemsPerPage,"1stchild")

    const handleCheckboxChange = (item: Record<string, string | number>) => {
      if (selectedItems.includes(item)) {
        const items = selectedItems.filter((i) => i !== item);
        setSelectedItems(items);
        if (onSelect) onSelect(items);
      } else {
        const items = [...selectedItems, item];
        setSelectedItems(items);
        if (onSelect) onSelect(items);
      }
    };

    // Column Resize Handlers
    const handleMouseDown = (e: React.MouseEvent, key: string) => {
      e.preventDefault();
      const startX = e.clientX;
      const startWidth = typeof columnWidths[key] === 'number' ? columnWidths[key] as number : 100;
      const minWidth = 100; // Minimum column width

      const handleMouseMove = (moveEvent: MouseEvent) => {
        const newWidth = Math.max(
          minWidth,
          startWidth + moveEvent.clientX - startX
        );
        setColumnWidths((prevWidths) => ({
          ...prevWidths,
          [key]: newWidth,
        }));
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    };

    useEffect(() => {
      if (typeof itemCount === "function") itemCount(selectedItems.length);
    }, [selectedItems.length]);

    const handlePageChange = (newPage: number) => {
      //  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
      const validPage = Math.max(1, Math.min(newPage, totalPages));
      setCurrentPage(validPage);
      if (onPageChangeP) {
        onPageChangeP(validPage); // API call only on user change
      }
    };

    const handleRefetch = () => {
      if (startDate && endDate) {
        onRefetch?.({ startDate, endDate });
        setIsRefetchModalOpen(false);
      }
    };

    const downloadTableAsCSV = async () => {
      try {
        // Create a new instance of JSZip
        const zip = new JSZip();

        // Fetch the dummy.csv file
        const response = await fetch("/dummy.csv");
        const csvData = await response.blob();

        // Add the CSV to the zip file
        zip.file("dummy.csv", csvData);

        // Generate the zip file
        const zipContent = await zip.generateAsync({ type: "blob" });

        // Create a download link
        const link = document.createElement("a");
        const currentDate = format(new Date(), "yyyyMMdd");
        const fileName = `web.mfilterit.cpv_${currentDate}.zip`;

        link.href = URL.createObjectURL(zipContent);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error("Error downloading file:", error);
      }
    };

    if (!isMounted) return null;

    return (
      <div className="w-full " ref={tableContainerRef}>
        <div className="flex flex-wrap w-full items-center text-body space-x-2 rounded-lg border bg-card ">
          {isSearchable && (
            // <div className="flex flex-grow items-center space-x-2 p-2 sm:w-full md:w-1/2 lg:w-1/2">
            <div className="flex flex-grow min-w-0 items-center p-2">
              <MdSearch className="text-xl text-card-foreground transition-colors duration-200 hover:text-primary" />
              <input
                type="text"
                placeholder="Search"
                value={SearchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-card text-card-foreground outline-none text-body"
              />
              {/* <TooltipProvider>
                    <Tooltip>
                <TooltipTrigger> */}
              <Button  title="Clear"
                className="cursor-pointer  text-xs px-0 py-0 w-5 h-5 text-white "
                
              >
                {/* Clear */}
                {/* <CircleX size={30} /> */}
                <X  size={15}/>
              </Button>
              {/* </TooltipTrigger>
                     <TooltipContent> */}
              {/* Refresh */}
              {/* </TooltipContent>
</Tooltip>
</TooltipProvider> */}
            </div>
          )}
          {/* Separator */}
          <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />
          {isColumn && (
            <DropdownMenu
              columns={columns}
              onToggle={handleColumnToggle}
              visibleColumns={visibleColumns}
            />
          )}

          {isbuttonText && (
            <div className="flex items-center space-x-2">
              <Button
                variant="default"
                className="bg-primary text-white hover:bg-secondary"
                onClick={onGenerateReport}
              >
                {buttonTextName}
              </Button>
            </div>
          )}
        </div>
        {/* {actionButton && (
          <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />
        )} */}
        {actionButton}
        {isCount && (
          <div>
            <div className="mx-2 h-6 border-l sm:hidden md:block lg:block" />

            <span
              title="Total Selected Rows"
              onClick={() =>
                typeof onDownloadAll === "function" ? onDownloadAll(data) : null
              }
              className="rounded-lg bg-purple-100 p-2 text-primary"
            >
              <span>{selectedItems.length}</span>
            </span>
          </div>
        )}
        {/* Separator */}

        {/* */}
        {/* {isDownload &&(
          <MdFileDownload
            title="Table Data Download"
            className="cursor-pointer text-xl text-primary transition-colors duration-200 hover:text-gray-400"
            title="Download Table Data as CSV"
          >
            <MdFileDownload />
          </button>
        )} */}
        {/* Separator */}

        {/*  <div className="mx-2 h-6 border-l" /> */}
        {/* <Button variant="ghost" size="icon-xs" onClick={() => onRefresh()}>
          <MdRefresh
            title="Table Data lauds"
            className="cursor-pointer text-xl text-primary transition-colors duration-200 hover:text-gray-400"
          />
        </Button> */}
        {/* {isDownload && (
        <div>
          <div className="mx-2 h-6 sm:hidden md:block lg:block" />
          <button
            onClick={downloadTableAsCSV}
            className="cursor-pointer text-xl text-primary transition-colors duration-200 hover:text-gray-400"
            title="Download Table Data as CSV"
          >
            <MdFileDownload />
          </button>
        </div>
      )} */}

        <div
          className={`relative border border-gray-200 rounded-lg overflow-hidden shadow-sm bg-card ${
            isFullscreen && filteredData.length > 10 ? 'scrollbar' : ''
          }`}
          style={{
            height: isDashboard ? `${tableHeight}px` : '720px', // 56 header + 700 body + ~24 border/margin
            overflowY: isDashboard
              ? 'hidden'
              : 'auto',
            overflowX: isFullscreen ? 'hidden' : 'auto',
          }}
        >
          {/* Single scrollable container for table body */}
          <div
            className="overflow-auto bg-card"
                      style={isDashboard ? {
            height: isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`,
            maxHeight: isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`,
            overflowY: isLoading ? 'hidden' : 'auto',
            overflowX: isFullscreen ? 'hidden' : 'auto',
            width: '100%',
          } : {
            height: '700px',
            maxHeight: '700px',
            overflowY: filteredData.length > 10 ? 'auto' : 'hidden',
            overflowX: isFullscreen ? 'hidden' : 'auto',
            width: '100%',
          }}
          >
            <Table className="w-full" style={isDashboard ? { tableLayout: 'fixed' } : { tableLayout: 'auto' }}>
            <TableHeader className="sticky top-0 z-10 p-0 bg-gray-50">
                <TableRow className="border-b-2 border-gray-300" style={isDashboard ? { height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px` } : { height: `${HEADER_HEIGHT}px` }}>
                {ischeckbx && isSelectable && (
                      <TableHead
                    className="border-r border-gray-200"
                      style={isDashboard ? {
                        width: '50px', minWidth: '50px', maxWidth: '50px', backgroundColor: headerColor, fontSize: '12px', padding: '8px',
                      } : {
                        width: '50px', minWidth: '50px', maxWidth: '50px', backgroundColor: headerColor, fontSize: '12px', padding: '8px',
                        }}
                      >
                        <Checkbox
                          onCheckedChange={(checked) => {
                            const allItems = checked ? filteredData : [];
                            setSelectedItems(allItems);
                            if (onSelect) {
                              onSelect(allItems);
                            }
                          }}
                        />
                      </TableHead>
                )}
                {visibleColumns.map((column) => (
                  <TableHead
                    key={column.key}
                    className="relative border-r border-gray-200"
                      style={isDashboard ? {
                      backgroundColor: headerColor,
                        color: 'black',
                        fontSize: isFullscreen ? '16px' : '12px',
                        padding: '8px',
                        whiteSpace: isMultiLineHeader ? 'pre-line' : 'nowrap',
                        wordWrap: isMultiLineHeader ? 'break-word' : 'normal',
                        lineHeight: isMultiLineHeader ? '1.2' : 'normal',
                        height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`,
                      } : {
                        backgroundColor: headerColor,
                        color: 'black',
                        width: '150px', minWidth: '150px', maxWidth: '150px',
                        padding: '12px ',
                        whiteSpace: isMultiLineHeader ? 'pre-line' : 'nowrap',
                        wordWrap: isMultiLineHeader ? 'break-word' : 'normal',
                        lineHeight: isMultiLineHeader ? '1.2' : 'normal',
                        height: `${HEADER_HEIGHT}px`,
                      }}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span
                          className={isDashboard ? "block font-semibold" : "block text-sm font-semibold"}
                          style={isDashboard && isMultiLineHeader ? {
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'normal',
                            maxHeight: '2.6em',
                          } : {
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                          }}
                          title={column.title}
                        >
                          {column.title}
                        </span>
                        <span
                          onClick={() => handleSort(column.key)}
                          className="cursor-pointer text-xs ml-2"
                        >
                          {sortConfig?.key === column.key ? (
                            sortConfig.direction === 'asc' ? (
                              <MdArrowUpward className="text-primary" />
                            ) : (
                              <MdArrowDownward className="text-primary" />
                            )
                          ) : (
                            <MdUnfoldMore className="text-gray-400" />
                          )}
                        </span>
                    </div>
                  </TableHead>
                ))}
                  {(isEdit || isDelete || isView || isDownload || isPause || isPlay || isRefetch || isSend || isClone) && (
                    <TableHead
                      className={isDashboard ? "border-r border-gray-200 text-left font-semibold" : "border-r border-gray-200 text-left text-sm font-semibold"}
                      style={isDashboard ? {
                        backgroundColor: headerColor, color: 'black', fontSize: '12px', padding: '8px', whiteSpace: 'nowrap',
                      } : {
                        backgroundColor: headerColor, color: 'black', width: '100px', minWidth: '100px', maxWidth: '100px', padding: '12px 8px', whiteSpace: 'nowrap', height: `${HEADER_HEIGHT}px`,
                      }}
                    >
                      Action
                    </TableHead>
                  )}
              </TableRow>
            </TableHeader>
              <TableBody>
              {isLoading ? (
                  <TableRow style={isDashboard ? { height: height ? `${height}px` : (isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`) } : { height: '700px' }}>
                    <TableCell colSpan={
                      visibleColumns.length +
                      (isSelectable ? 1 : 0) +
                      (isEdit || isDelete || isView || isDownload || isPause || isClone || isPlay ? 1 : 0)
                    } className="text-center p-0" style={isDashboard ? { height: height ? `${height}px` : (isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`), verticalAlign: 'middle' } : { height: '700px', verticalAlign: 'middle' }}>
                      <div className="flex justify-center items-center w-full h-full" style={{ height: '100%' }}>
                      <Loader2 className={`${isFullscreen ? 'h-12 w-12' : 'h-8 w-8'} animate-spin text-primary`} />
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredData.length === 0 ? (
                  <TableRow style={isDashboard ? { height: height ? `${height}px` : (isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`) } : { height: '700px' }}>
                    <TableCell colSpan={
                      visibleColumns.length +
                      (isSelectable ? 1 : 0) +
                      (isEdit || isDelete || isView || isDownload || isPause || isClone || isPlay ? 1 : 0)
                    } className="text-center p-0" style={isDashboard ? { height: height ? `${height}px` : (isFullscreen ? `${45 * DEFAULT_ROWS}px` : `${DASHBOARD_ROW_HEIGHT * DEFAULT_ROWS}px`), verticalAlign: 'middle' } : { height: '700px', verticalAlign: 'middle' }}>
                      <div className={isDashboard ? 'flex justify-center items-center w-full h-full' : 'flex justify-center items-center w-full h-full'}>
                        <span className={`${isFullscreen ? 'text-sm' : 'text-small-font'} dark:text-white`}>{SearchTerm.trim() ? 'No matching results found' : 'No Data Found!'}</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredData.map((item, index) => (
                  <TableRow
                    key={index}
                      className="hover:bg-background transition-colors duration-150"
                      style={isDashboard ? { height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px` } : { height: `${ROW_HEIGHT}px` }}
                  >
                    {ischeckbx && isSelectable && (
                          <TableCell
                        className="border-r border-gray-200 p-4"
                          style={isDashboard ? {
                            width: '50px', minWidth: '50px', maxWidth: '50px', height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, lineHeight: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, fontSize: '12px',
                          } : {
                            width: '50px', minWidth: '50px', maxWidth: '50px', height: `${ROW_HEIGHT}px`, lineHeight: `${ROW_HEIGHT}px`, fontSize: '12px',
                            }}
                          >
                            <Checkbox
                              checked={selectedItems.includes(item)}
                              onCheckedChange={() => handleCheckboxChange(item)}
                            />
                          </TableCell>
                    )}
                    {visibleColumns.map((column) => (
                      <TableCell
                        key={column.key}
                          className="border-r text-left dark:text-white p-2 text-xs"
                          style={isDashboard ? {
                            fontSize: isFullscreen ? '16px' : '12px', padding: '8px', height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, lineHeight: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: '150px', minWidth: '150px', width: '150px',
                          } : {
                            maxWidth: '150px', minWidth: '150px', width: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', height: `${ROW_HEIGHT}px`, lineHeight: `${ROW_HEIGHT}px`,
                          }}
                        >
                          <div
                            className="overflow-hidden text-ellipsis whitespace-nowrap text-left"
                            style={{ width: '100%', maxWidth: '100%' }}
                            title={typeof item[column.key] === 'number' ? item[column.key].toLocaleString() : String(item[column.key] || '')}
                          >
                            <span className="block truncate">
                              {typeof item[column.key] === 'number' ? item[column.key].toLocaleString() : String(item[column.key] || '')}
                            </span>
                          </div>
                      </TableCell>
                    ))}
                      {(isEdit || isDelete || isView || isDownload || isClone || isPause || isPlay) && (
                        <TableCell
                          className="border-r dark:text-white p-2 text-left text-xs"
                          style={isDashboard ? {
                            fontSize: '12px', padding: '8px', height: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, lineHeight: isFullscreen ? '45px' : `${DASHBOARD_ROW_HEIGHT}px`, whiteSpace: 'nowrap',
                          } : {
                            width: '100px', minWidth: '100px', maxWidth: '100px', height: `${ROW_HEIGHT}px`, lineHeight: `${ROW_HEIGHT}px`,
                          }}
                        >
                          <div className="flex space-x-2 justify-center">
                            {isEdit && (
                              <button onClick={() => onEdit?.(item)} className="text-primary hover:text-gray-500">
                                <MdEdit size={18} />
                              </button>
                            )}
                            {isClone && (
                              <button onClick={() => onClone?.(item)} className="text-primary hover:text-gray-500">
                                <FaClone size={18} />
                              </button>
                            )}
                            {isDelete && (
                              <button onClick={() => onDelete?.(item)} className="text-primary hover:text-gray-500">
                                <MdDelete size={18} />
                              </button>
                            )}
                            {isView && (
                              <button onClick={() => onView?.(item)} className="text-primary hover:text-gray-500">
                                <MdVisibility size={18} />
                              </button>
                            )}
                          </div>
                        </TableCell>
                      )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          </div>
        </div>

        {isPaginated && data.length > 0 && (
          <div className={`mt-1 flex items-center justify-between ${isPaginationStyled ? 'bg-card p-4 rounded-lg shadow-md' : isDashboard ? 'bg-card' : 'bg-muted'}`}>
            <div className="flex items-center">
              <Select
                value={String(itemsPerPage)}
                onValueChange={(value) => {
                  setItemsPerPage(Number(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-[70px] h-[30px] outline-none focus:ring-0 border-primary shadow-md text-small-font dark:text-white">
                  <SelectValue className="w-16" placeholder="Rows" />
                </SelectTrigger>
                <SelectContent 
                  className="border-none outline-none focus:ring-0 z-[9999]"
                >
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              showFirstLast={true}
            />
          </div>
        )}

        <Dialog open={isRefetchModalOpen} onOpenChange={setIsRefetchModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Select Date Range</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid gap-2">
                <Label>End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleRefetch} disabled={!startDate || !endDate}>
                Refetch Data
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  };

export default ResizableTable;
