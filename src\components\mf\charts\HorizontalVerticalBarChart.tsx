"use client"
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>hart, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>ist, ResponsiveContainer } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartLegend,
  ChartLegendContent,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  CustomTick,
} from "@/components/ui/chart"
import HeaderRow from "@/components/mf/HeaderRow";
import { Loader2 } from "lucide-react"
import { InformationCard } from "../InformationCard";
import { useTheme } from "@/components/mf/theme-context";
import { formatValue } from "@/lib/utils";
import { useFullscreen } from "@/hooks/use-fullscreen";

interface chartData {
  label: string;
  [key: string]: string | number;
}

interface chartconfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface HorizontalBarChartProps {
  chartData: chartData[];
  chartConfig: chartconfig;
  xAxisTitle?: string;
  yAxisTitle?: string;
  isHorizontal?: boolean;
  InformCard?: { title: string; desc: string }[];
  title?: string;
  position?: "top" | "bottom" | "left" | "right" | "center" | "inside" | "insideTop" | "insideBottom" | "insideLeft" | "insideRight" | "insideTopLeft" | "insideBottomLeft" | "insideTopRight" | "insideBottomRight";
  handleExport?: () => void;
  onExpand?: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  isRadioButton?: boolean;
  isSelect?: boolean;
  BarchartTitle?: string;
  formatterType?: "number" | "percentage";
  dataKey?: string;
  namekeys?:string;
  isLoading?:boolean;
  barsize?:number;
  setheight?:string;
  isPercentage?:boolean;
  handleFrequencyChange?: (value: string) => void;
  selectoptions?:string[];
  selecteedFrequency?:string;
  placeholder?:string;
  truncateLength?: number;
  yAxisXOffset?: number;
  yAxisXOffsetFullscreen?: number;
}

const HorizontalVerticalBarChart: React.FC<HorizontalBarChartProps> = ({
  chartData,
  chartConfig,
  xAxisTitle,
  yAxisTitle,
  isHorizontal = false,
  handleExport,
  onExport,
  onExpand,
  isLoading,
  position,
  isSelect,
  isRadioButton,
  title,
  BarchartTitle,
  formatterType = "number",
  dataKey,
  namekeys,
  InformCard=[],
  barsize,
  setheight,
  isPercentage,
  handleFrequencyChange,
  selectoptions,
  selecteedFrequency,
  placeholder="",
  truncateLength = 10,
  yAxisXOffset,
  yAxisXOffsetFullscreen,
}) => {
   const { isDarkMode } = useTheme();
   const isFullscreen = useFullscreen();

   // Calculate responsive chart height based on fullscreen state
   const getChartHeight = () => {
     if (isFullscreen) {
       return 550; // Fixed height for fullscreen to prevent overflow
     }
     return Math.min(chartData?.length * 10, 400); // Normal height
   };

   const chartHeight = getChartHeight();

  const formatLabel = (value: number) => {
    if (formatterType === "percentage") {
      return `${(value * 1).toFixed(2)}%`;   // Format as percentage
    }
    return `${value}`;  // Format as number
  };
  const chartWidth = Math.min(chartData.length * 50, 1000);
  const computedChartSize = isHorizontal
  ? { height: Math.min(chartData.length * 30, 400), width: undefined }
  : { height: undefined, width: Math.min(chartData.length * 50, 1000) };
  
  return (
    <Card className="border-none h-full p-0 w-full">
      <HeaderRow
        title={title}
        onExpand={onExpand}
        handleExport={handleExport}
        isRadioButton={isRadioButton}
        isSelect={isSelect}
        onExport={onExport}
        selectoptions={selectoptions}
        handleFrequencyChange={handleFrequencyChange}
        selectedFrequency={selecteedFrequency}
        placeholder={placeholder||""}
      />
      <CardHeader className="items-center p-0 pb-0">
        <CardTitle className="text-body font-semibold">{BarchartTitle}</CardTitle>
      </CardHeader>
      <CardContent className={`w-full chart-card-content ${isFullscreen ? 'h-[600px]' : 'h-[310px]'} ${isFullscreen ? '' : 'overflow-y-auto scrollbar'}`}>
        <ResponsiveContainer width={"100%"} height={isFullscreen ? 550 : '100%'}>
      <div className="flex flex-row h-full w-full min-w-0">
          {/* Information Cards */}
          <div className={`flex-0 flex flex-col justify-center ${isFullscreen ? 'gap-4' : ''} min-w-0`}>
            {InformCard?.map((item, index) => (

              <InformationCard
                key={index}
                InformTitle={item.title}
                informDescription={item.desc}
                isFullscreen={isFullscreen}
              />
            ))}
          </div>
          <div className="flex justify-center flex-1 min-w-0">
      {isLoading ?(
         <div className="flex items-center justify-center w-full h-[250px]">
                    <Loader2 className=" h-8 w-8 animate-spin text-primary" />
               </div>
      ):(
        <ChartContainer config={chartConfig} style={{ height: isFullscreen ? "550px" : "100%", width: "100%", minWidth: 0 }}>
          <ResponsiveContainer height={isFullscreen ? 550 : chartHeight}>
          {chartData.length>0 ?(
          <BarChart  
          //height={chartHeight} 
            accessibilityLayer
            data={chartData}
            layout={isHorizontal ? "vertical" : "horizontal"} // Toggle between vertical and horizontal chart
            margin={{
              left: isFullscreen ? 80 : 4,
              top:20,
              right:20,
            }}
            barSize={isFullscreen ? (barsize || 20) * 1.5 : barsize}
            barGap={isFullscreen ? 3 : 1} // Reduce gap between bars in the same group
            barCategoryGap={isFullscreen ? "4%" : "2%"} // 
            height={chartHeight}
          >
            {/* Conditionally set axis based on orientation */}
            {isHorizontal ? (
              <>
               <YAxis
  dataKey="label"
  type="category"
  tickLine={false}
  tickMargin={25}
  axisLine={false}
  interval={0}
 textAnchor="start"
  className="text-body"
  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" textAnchor="start" truncateLength={truncateLength} yAxisXOffset={yAxisXOffset} yAxisXOffsetFullscreen={yAxisXOffsetFullscreen} />}
  style={{ fontSize: isFullscreen ? "18px" : "12px" }}
/>
                <XAxis 
                  dataKey={dataKey}
                  axisLine={true}
                  interval={0}
                  style={{ fontSize: isFullscreen ? "16px" : "10px" }}
                  type="number"  // Use a numerical axis for horizontal layout
                  textAnchor="middle"
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="x" xOffset={-1} truncateLength={truncateLength} />}
                />
              </>
            ) : (
              <>
                <XAxis
                  dataKey="label"
                  type="category"
                  tickLine={false}
                  tickMargin={3}
                  axisLine={false}
                  interval={0}
                  style={{ fontSize: isFullscreen ? "16px" : "10px" }}
                  textAnchor="middle"
                  tickFormatter={(value) =>
                    chartConfig[value as keyof typeof chartConfig]?.label
                  }
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="x" xOffset={-1} truncateLength={truncateLength} />}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  textAnchor="middle"
                  style={{ fontSize: isFullscreen ? "16px" : "10px" }}
                  tick={(props) => <CustomTick {...props} chartConfig={chartConfig} isFullscreen={isFullscreen} axisType="y" truncateLength={truncateLength} />}
                />
              </>
            )}
            
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent  nameKey={namekeys} hideLabel isPercentage={isPercentage}/>}
            />
            <ChartLegend
              content={<ChartLegendContent verticalAlign="bottom"  nameKey={namekeys} isFullscreen={isFullscreen}/>}
              className="-translate-y-2 flex-wrap gap-2 [&>*]:basis-1/4 [&>*]:justify-center"
            />
            <Bar 
              dataKey={dataKey}
              layout={isHorizontal ? "vertical" : "horizontal"} // Bar layout based on the orientation
              radius={4}
            >
             
                           <LabelList 
                                  dataKey={dataKey}
                                  position={position || "top"}
                                  style={{ 
                                    fontSize: isFullscreen ? "12px" : "8px",  
                                    fill: isDarkMode ? "#fff" : "#000",  
                                    backgroundColor:"#fff"
                                  }}
                                  formatter={(formatLabel)} 
                                />
                        
                                
              </Bar>
          </BarChart>
          ):(
            <div className="flex items-center justify-center h-[250px]">
            <span className="text-small-font">No Data Found.!</span>
          </div>)}
          </ResponsiveContainer>
        </ChartContainer>
      )}
      </div>
      </div>
      </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
export default HorizontalVerticalBarChart;
