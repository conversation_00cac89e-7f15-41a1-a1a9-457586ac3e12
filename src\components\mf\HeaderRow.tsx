"use client";
import React, { useEffect, useState} from 'react';
import {
  Card,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
} from "@/components/ui/dropdown-menu";
import { Ellipsis, Maximize } from 'lucide-react';
import { RadioButtons } from './RadioButton';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MultiSelect } from '@/components/ui/multi-select';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@radix-ui/react-tooltip';
import { createPortal } from 'react-dom';
import { useFullscreen } from '@/hooks/use-fullscreen';

interface HeaderRowProps {
  heading?: string;
  sub_heading?: string;
  handleExport?: () => void;
  onExport?: (format: string, title: string, key: string) => void;
  onExpand?: (key: string) => void;
  visitEventOptions?: { value: string; label: string }[];
  exportKey?: string;
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  handleFrequencyChange?: (value: string) => void; 
  handleMultiSelectChange?: (values: string[]) => void;
  selectedFrequency?: string;
  selectedMultiValues?: string[];
  selectoptions?: string[];
  multiSelectOptions?: { label: string; value: string }[];
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  isMultiSelect?: boolean;
  placeholder?: string;
  multiSelectPlaceholder?: string;
  width?: string;
  textcolors?:string;
  showEllipsisInExpanded?: boolean;
  showSelectInExpanded?: boolean;
  isexportcsv?:boolean;
  isellipsis?:boolean;
}

const HeaderRow: React.FC<HeaderRowProps> = ({
  handleTypeChange,
  isellipsis=true,
  visitEventOptions,
  selectoptions = [],
  multiSelectOptions = [],
  selectedType,
  exportKey="",
  selectedFrequency,
  selectedMultiValues = [],
  handleFrequencyChange,
  handleMultiSelectChange,
  handleExport,
  onExport,
  onExpand,
  title,
  width = "120px",
  isSelect = false,
  isRadioButton = false,
  isMultiSelect = false,
  placeholder = "",
  multiSelectPlaceholder = "",
  textcolors,
  showEllipsisInExpanded = false,
  showSelectInExpanded=false,
  isexportcsv=false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isSelectDropdownOpen, setIsSelectDropdownOpen] = useState(false);
  
  // Use the custom hook for fullscreen detection
  const isFullscreen = useFullscreen();

  const handleExpandClick = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    onExpand?.(exportKey); // Notify the parent for expansion change with optional chaining
    setIsDropdownOpen(false); // Close dropdown after expand
  };

  const handleDropdownClick = () => {
    if (isFullscreen) {
      // For fullscreen mode, use custom dropdown
      const trigger = document.querySelector('[data-dropdown-trigger]');
      if (trigger) {
        // Just toggle the dropdown state - position will be calculated in render
        const newState = !isDropdownOpen;
        setIsDropdownOpen(newState);
      } else {
      }
    } else {
      // For non-fullscreen mode, just toggle the state
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  const handleExportClick = () => {
    handleExport?.();
    setIsDropdownOpen(false);
  };

  const handlePNGExportClick = () => {
    onExport?.("png", title ?? "Untitled", exportKey);
    setIsDropdownOpen(false);
  };

  
  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      // Force re-render when fullscreen state changes
      const newFullscreenState = !!document.fullscreenElement || 
                                !!(document as any).webkitFullscreenElement || 
                                !!(document as any).mozFullScreenElement || 
                                !!(document as any).msFullscreenElement;
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isDropdownOpen && isFullscreen) {
        const target = event.target as Element;
        
        if (!target.closest('[data-dropdown-trigger]') && !target.closest('[data-custom-dropdown]')) {
          setIsDropdownOpen(false);
        } else {
        }
      }
    };

    if (isDropdownOpen && isFullscreen) {
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 200);
      
      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isDropdownOpen, isFullscreen]);

  // Close select dropdown when clicking outside
  useEffect(() => {
    const handleSelectClickOutside = (event: MouseEvent) => {
      if (isSelectDropdownOpen && isFullscreen) {
        const target = event.target as Element;
        if (!target.closest('[data-select-trigger]') && !target.closest('[data-select-dropdown]')) {
          setIsSelectDropdownOpen(false);
        }
      }
    };

    if (isSelectDropdownOpen && isFullscreen) {
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleSelectClickOutside);
      }, 200);
      
      return () => {
        clearTimeout(timeoutId);
        document.removeEventListener('mousedown', handleSelectClickOutside);
      };
    }
  }, [isSelectDropdownOpen, isFullscreen]);

  // Debug dropdown state
  useEffect(() => {
     // console.log("Dropdown is open");
      
      setTimeout(() => {
        const dropdownElement = document.querySelector('[data-custom-dropdown]');
        if (dropdownElement) {
          const rect = dropdownElement.getBoundingClientRect();
        } else {
        }
      }, 100);
    }, [isDropdownOpen, isFullscreen]);

  // Handle ESC key to hide ellipsis in expanded mode
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsDropdownOpen(false);
        setIsSelectDropdownOpen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isFullscreen]);

  // Determine if ellipsis should be shown
  const shouldShowEllipsis = isFullscreen ? showEllipsisInExpanded : true;

  const shouldShowSelect = isFullscreen ? showSelectInExpanded : true;

  return (
    <Card className='border-none w-full'>
      <div className="flex flex-wrap justify-between">

          <>
            {/* Title and expandable menu when collapsed */}
            <CardTitle className="flex justify-center items-center text-sub-header font-semibold sm:text-body p-1" style={{ color: textcolors }}>
              {title}
            </CardTitle>
            <CardTitle className="flex flex-wrap space-x-4 sm:space-x-2 justify-between w-full sm:w-auto p-1">
              {isRadioButton && (
                <div className="flex justify-center items-center">
                  <RadioButtons
                    options={visitEventOptions || []}
                    defaultValue={selectedType}
                    onValueChange={handleTypeChange || (() => {})}
                  />
                </div>
              )}

              {isSelect && selectoptions.length>0 && shouldShowSelect && (
                <div className="flex justify-start items-center relative">
                  {isFullscreen ? (
                    // Custom select for fullscreen mode to avoid aria-hidden issues
                    <div className="relative">
                      <button
                        className={`w-${width} h-[30px] flex items-center justify-between rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
                        onClick={() => setIsSelectDropdownOpen(!isSelectDropdownOpen)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            setIsSelectDropdownOpen(!isSelectDropdownOpen);
                          } else if (e.key === 'Escape') {
                            setIsSelectDropdownOpen(false);
                          }
                        }}
                        aria-expanded={isSelectDropdownOpen}
                        aria-haspopup="listbox"
                        data-select-trigger
                      >
                        <span className="truncate">
                          {selectedFrequency || placeholder}
                        </span>
                        <svg className="h-4 w-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      
                      {isSelectDropdownOpen && createPortal(
                        <div 
                          className="fixed z-[999999] bg-white border border-input rounded-md shadow-lg min-w-[200px]"
                          style={{
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            maxHeight: '200px',
                            overflowY: 'auto'
                          }}
                          data-select-dropdown
                          onKeyDown={(e) => {
                            if (e.key === 'Escape') {
                              setIsSelectDropdownOpen(false);
                            }
                          }}
                          role="listbox"
                        >
                          {selectoptions.map((option, index) => (
                            <button
                              key={index}
                              className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                              onClick={() => {
                                handleFrequencyChange?.(option);
                                setIsSelectDropdownOpen(false);
                              }}
                              role="option"
                              aria-selected={selectedFrequency === option}
                            >
                              {option}
                            </button>
                          ))}
                        </div>,
                        document.body
                      )}
                    </div>
                  ) : (
                    // Normal Select component for non-fullscreen mode
                 <Select onValueChange={handleFrequencyChange}>
                                    <SelectTrigger className={`w-${width} h-[30px]`}>
                                        <SelectValue placeholder={placeholder} />
                                    </SelectTrigger>
                                    <SelectContent>
                        <SelectGroup className="relative">
                                            {selectoptions.map((option, index) => (
                                                <SelectItem key={index} value={option} style={{ textAlign: 'left' }}>
                                                    {option}
                                                </SelectItem>
                                            ))}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                  )}
                </div>
              )}

              {isMultiSelect && (
                <div className="flex justify-start items-center">
                  <MultiSelect
                    options={multiSelectOptions}
                    value={selectedMultiValues}
                    onValueChange={handleMultiSelectChange || (() => {})}
                    placeholder={multiSelectPlaceholder}
                    className={`w-${width} ${isFullscreen ? 'z-[999999]' : ''}`}
                    style={isFullscreen ? { zIndex: 999999 } : {}}
                  />
                </div>
              )}

{isellipsis && (

  <div className="p-2 flex justify-center items-center relative" title='Select option'>
    {(() => {
      //console.log("Rendering dropdown section, isFullscreen:", isFullscreen, "isDropdownOpen:", isDropdownOpen);
      return isFullscreen ? (
        // Custom dropdown for fullscreen mode to avoid aria-hidden issues
        // COMMENTED OUT: Current expanded screen functionality is working properly
        <>
          <div 
            className="group cursor-pointer" 
            onClick={handleDropdownClick}
            data-dropdown-trigger
          >
            {shouldShowEllipsis ? <Ellipsis className="group-hover:text-blue-500" /> : null}
          </div>
         
          
         
        </>
      ) : (
        // Normal Radix UI dropdown for non-fullscreen mode
        <TooltipProvider>
          <Tooltip>
            <DropdownMenu 
              open={isDropdownOpen} 
              onOpenChange={setIsDropdownOpen}
            >
              <DropdownMenuTrigger asChild>
                <TooltipTrigger>
                  <div 
                    className="group" 
                    onClick={handleDropdownClick}
                    data-dropdown-trigger
                  >
                    {shouldShowEllipsis ? <Ellipsis className="group-hover:text-blue-500" /> : null}
                  </div>
                </TooltipTrigger>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="dark:bg-background z-[9999] relative">
                <DropdownMenuGroup>
                   {isexportcsv && exportKey ? (
                               <DropdownMenuItem onClick={() => handleExportClick(exportKey)}>
                                 Export Filtered CSV
                               </DropdownMenuItem>
                             ) : (
                               <DropdownMenuItem onClick={handleExportClick}>
                                 Export to CSV
                               </DropdownMenuItem>
                             )}
                  <DropdownMenuItem onClick={handlePNGExportClick}>
                    Export to PNG
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleExpandClick}>
                    <span>Expand</span>
                    <Maximize size={20} className="ml-3" />
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </Tooltip>
        </TooltipProvider>
      );
    })()}
  </div>
  )}

            </CardTitle>
          </>
      </div>
    </Card>
  );
};

export default HeaderRow;