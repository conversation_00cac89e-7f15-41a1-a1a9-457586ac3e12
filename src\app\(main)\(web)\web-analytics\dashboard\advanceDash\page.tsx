"use client";
import { useSearchParams } from "next/navigation";
import { useApiCall } from "../../../queries/api_base";
import Endpoint from "@/app/(main)/(web)/common/endpoint";

const AdvanceDashPage = () => {
  const searchParams = useSearchParams();
  const url = searchParams.get("url");

  // Extract the id from the url param (assuming it's the dashboard id)
  // If url is a full URL, extract the id part (after last slash or as needed)
  // For now, assume url is the id or contains the id
  const dashboardId = url ? url.split("/").pop() : null;

  // Use a unique queryKey to force the API to be called every render
  const queryKey = [
    "get_embedded_menus",
    dashboardId ? String(dashboardId) : "",
  ];
  const {
    result: apiResult,
    loading: apiLoading,
  } = useApiCall<any>({
    url: dashboardId
      ?   process.env.NEXT_PUBLIC_AUTH_DOMAIN + `api/v1/access_control/get_embedded_menus/${dashboardId}`
      : "",
    method: "GET",
    manual: false,
    headers: {
      Authorization:
        "eyJraWQiOiJWdUdrQXgyaHZpcTRDXC9BWUs0SmcxRkcxenpWQmphWHZzc3dKSGtxdmFlZz0iLCJhbGciOiJSUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vo48argKM-_NIU1BDeKNwQK7sd_v_SHsA24acBgcznp-rVRgLmqXsTJtbTaBtnpBIVBoBovlOg1FlQCFAfO5YOs-OsBVviBj-2qboguqxYRAxnnfw3VvqN3hyIAaeeQ0D-2k-T7tzLNtFXYYlOuS5nQLi4ZnTBs_OrXBtK4p4TqawRLpJQ08Hs4ahnsDgXrZj3TWgrZAHg77M2Q2U5EcokYMdPfoQsMex--qDybsllOuEIf_UPMXFkQaTOb7AiutPmXjhpKU7wblg4BPKIoh4f4sqUmrCjdB79D2TEMTScqrsr42VwXy3orF17tE4rmmqhwfvhxjeM4j8Act1nBtYA",
    },
    // No params for GET
    onError: (err) => {},
    onSuccess: (data) => {},
    queryKey: dashboardId ? queryKey : undefined,
  });

  return (
    <div className="w-full h-screen">
      {url ? (
        <>
          {/* Show API loading, error, or data */}
          {apiLoading && <div className="p-2 text-gray-500">Loading Dashboard...</div>}
          {apiResult?.isError && (
            <div className="p-2 text-red-500">Error loading embedded menu.</div>
          )}
          {/* Only render iframe if API response is present and is a string (the embed URL) */}
          {apiResult?.data && typeof apiResult.data === "string" ? (
            <iframe
              title="Advance Dashboard"
              src={apiResult.data}
              width="100%"
              height="100%"
              style={{ border: "none" }}
              allowFullScreen
            />
          ) : null}
        </>
      ) : (
        <div className="flex items-center justify-center h-full text-lg text-gray-500">
          No dashboard URL provided.
        </div>
      )}
    </div>
  );
};

export default AdvanceDashPage;
