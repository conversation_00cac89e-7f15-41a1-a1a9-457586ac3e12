# Fullscreen Utility Migration Guide

## Overview
This guide helps you update all components to use the new SSR-safe fullscreen utilities instead of direct `document.fullscreenElement` access.

## New Fullscreen Utilities

### 1. Import the utilities
```typescript
import { 
  useFullscreen, 
  setFullscreenStateSafely, 
  useFullscreenEffect 
} from '@/lib/fullscreen-utils';
```

### 2. Available Functions

#### `useFullscreen()` - React Hook
```typescript
const isFullscreen = useFullscreen();
```
- ✅ SSR-safe
- ✅ Reactive (updates automatically)
- ✅ Recommended for most components

#### `setFullscreenStateSafely(setStateFunction)` - Utility Function
```typescript
setFullscreenStateSafely(setIsFullscreen);
```
- ✅ SSR-safe
- ✅ Use in useEffect for manual state management

#### `useFullscreenEffect(setStateFunction)` - Effect Hook
```typescript
useFullscreenEffect(setIsFullscreen);
```
- ✅ SSR-safe
- ✅ Handles both initial state and event listeners

## Components to Update

### 1. HeaderRow.tsx ✅ (Already Updated)
- Uses `useFullscreen()` hook
- Has `showEllipsisInExpanded` and `showSelectInExpanded` props

### 2. DynamicBarChart.tsx ✅ (Just Updated)
- Replaced `!!document.fullscreenElement` with `setFullscreenStateSafely(setIsFullscreen)`

### 3. Components Still Need Updates:

#### HorizontalVerticalBarChart.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### RadialChart.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### TableComponent.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### DoubleLineChart.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### DonutChart.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### StackedBarwithLine.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

#### stackedBarChart.tsx
**Current:**
```typescript
setIsFullscreen(!!document.fullscreenElement);
```

**Replace with:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';

// In useEffect:
setFullscreenStateSafely(setIsFullscreen);
```

## Quick Update Pattern

For each component:

1. **Add import:**
```typescript
import { setFullscreenStateSafely } from '@/lib/fullscreen-utils';
```

2. **Replace the line:**
```typescript
// OLD:
setIsFullscreen(!!document.fullscreenElement);

// NEW:
setFullscreenStateSafely(setIsFullscreen);
```

3. **Remove SSR checks** (if any):
```typescript
// OLD:
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  setIsFullscreen(!!document.fullscreenElement);
}

// NEW:
setFullscreenStateSafely(setIsFullscreen);
```

## Benefits After Migration

- ✅ **No more SSR errors**
- ✅ **Consistent fullscreen behavior**
- ✅ **Better performance**
- ✅ **Easier maintenance**
- ✅ **Type safety**

## Testing

After updating all components:
1. Run `npm run build` - should complete without SSR errors
2. Test fullscreen functionality in browser
3. Verify ellipsis/select hiding works in fullscreen mode 