'use client'
import React from 'react';
import ResizableTable from '@/components/mf/TableComponent1';

const Teams = () => {
  const columns = [
    { title: "Name", key: "name" },
    { title: "Role", key: "role" },
    { title: "Email", key: "email" },
    { title: "Phone No", key: "phone" },
    { title: "Status", key: "status" },
    { title: "Active/Inactive", key: "active" },
  ];

  const data = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Admin",
      email: "<EMAIL>",
      phone: "************",
      status: "Online",
      active: "Active",
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "User",
      email: "<EMAIL>",
      phone: "************",
      status: "Offline",
      active: "Inactive",
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Editor",
      email: "<EMAIL>",
      phone: "************",
      status: "Busy",
      active: "Active",
    },
  ];

  return (
    <div className="overflow-x-auto"> {/* Ensure the table is scrollable horizontally on small screens */}
      <ResizableTable
        columns={columns}
        data={data}
        isEdit={true}
        isDelete={true}
        isView={true}
        isDownload={true}
        isSelectable={true}
        actionButton={<button>Action Button</button>}
        onEdit={(item) => console.log("Edit", item)}
        onDelete={(item) => console.log("Delete", item)}
        onView={(item) => console.log("View", item)}
        onDownload={(item) => console.log("Download", item)}
        onDownloadAll={() => console.log("Download All")}
        onSelect={(selectedItems) => console.log("Selected Items", selectedItems)}
      />
    </div>
  );
};

export default Teams;
