"use client";
import React, { useState, useEffect, useRef } from 'react';
import ReusableDialog from '@/components/ui/dialogUserMangement';
import { useProductListQuery, useRoleListQuery, usePackageListQuery } from '../../app/(main)/(web)/web-analytics/user-management/queries/product-mapping';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Select from 'react-select';

interface UserFormProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (data: Record<string, any>) => void;
  dialogMode: "create" | "edit" | "view";
  defaultValues?: Record<string, string>;
  isSubmitting?: boolean;
}

interface FormValues {
  name: string;
  email: string;
  phone: string;
  gender: string;
  products: string;
  roles: string;
  Package_Name: string;
}

const validationSchema = Yup.object().shape({
  name: Yup.string().required('User Name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string()
    .matches(/^[0-9]{10}$/, 'Phone number must be exactly 10 digits')
    .required('Phone number is required'),
  gender: Yup.string().required('Gender is required'),
  products: Yup.string().when('dialogMode', {
    is: 'create',
    then: () => Yup.string().required('Product is required')
  }),
  roles: Yup.string().when(['dialogMode', 'products'], {
    is: (dialogMode: string, products: string) => dialogMode === 'create' && products,
    then: () => Yup.string().required('Role is required')
  })
});

const GENDER_OPTIONS = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" }
];

const UserForm: React.FC<UserFormProps> = ({
  open,
  setOpen,
  onSubmit,
  dialogMode,
  defaultValues,
  isSubmitting
}) => {
  const [productList, setProductList] = useState<Array<{ value: string; label: string }>>([]);
  const [roleList, setRoleList] = useState<Array<{ value: string; label: string }>>([]);
  const [packageList, setPackageList] = useState<Array<{ value: string; label: string }>>([]);
  const [selectedProduct, setSelectedProduct] = useState("");

  const nameInputRef = useRef<HTMLInputElement>(null);
  const emailInputRef = useRef<HTMLInputElement>(null);
  const phoneInputRef = useRef<HTMLInputElement>(null);

  const productListQuery = useProductListQuery();
  const roleListQuery = useRoleListQuery(selectedProduct);
  const packageListQuery = usePackageListQuery(selectedProduct);

  const handleProductChange = async (productName: string) => {
    if (!productName) return;
    setSelectedProduct(productName);
  };

  useEffect(() => {
    if (productListQuery.data && Array.isArray(productListQuery.data)) {
      const transformedProducts = productListQuery.data.map(product => {
        const value = typeof product === 'string' ? product : (product.value || '');
        const label = typeof product === 'string' ? product : (product.label || '');
        return { 
          value: String(value), 
          label: String(label) 
        };
      });
      setProductList(transformedProducts);
    }
  }, [productListQuery.data]);

  useEffect(() => {
    if (roleListQuery.data) {
      const transformedRoles = roleListQuery.data.map(role => ({
        value: role._id || '',
        label: role.Alias || role._id || ''
      }));
      setRoleList(transformedRoles);
    } else {
      setRoleList([]);
    }
  }, [roleListQuery.data]);

  useEffect(() => {
    if (packageListQuery.data) {
      const transformedPackages = packageListQuery.data.map((pkg: any) => ({
        value: typeof pkg === "string" ? pkg : pkg.Package_Name,
        label: typeof pkg === "string" ? pkg : pkg.Package_Name,
      }));
      setPackageList(transformedPackages);
    } else {
      setPackageList([]);
    }
  }, [packageListQuery.data]);

  const initialValues: FormValues = {
    name: defaultValues?.name || '',
    email: defaultValues?.email || '',
    phone: defaultValues?.phone || '',
    gender: defaultValues?.gender || '',
    products: defaultValues?.products || '',
    roles: defaultValues?.roles || '',
    Package_Name: defaultValues?.Package_Name || ''
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={onSubmit}
      enableReinitialize
    >
      {({ values, handleSubmit, setFieldValue, errors, touched }) => (
        <ReusableDialog
          open={open}
          setOpen={setOpen}
          onSubmit={handleSubmit}
          DialogTitles={
            dialogMode === "create" ? "Create User" : 
            dialogMode === "edit" ? "Edit User" : 
            "User Details"
          }
          fields={[
            {
              name: "name",
              label: "User Name",
              type: "input",
              disabled: dialogMode === "view",
              ref: nameInputRef,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue("name", e.target.value);
              }
            },
            {
              name: "email",
              label: "Email",
              type: "input",
              disabled: dialogMode === "edit" || dialogMode === "view",
              ref: emailInputRef,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue("email", e.target.value);
              }
            },
            {
              name: "phone",
              label: "Phone",
              type: "input",
              disabled: dialogMode === "view",
              ref: phoneInputRef,
              onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue("phone", e.target.value);
              }
            },
            {
              name: "gender",
              label: "Gender",
              type: "select",
              options: GENDER_OPTIONS,
              disabled: dialogMode === "edit" || dialogMode === "view",
              onChange: (option: any) => {
                setFieldValue("gender", option.value);
              }
            },
            ...(dialogMode === "create" ? [
              {
                name: "products",
                label: "Products",
                type: "select",
                options: productList,
                onDependencyChange: handleProductChange,
                onChange: (option: any) => {
                  setFieldValue("products", option.value);
                }
              },
              {
                name: "roles",
                label: "Roles",
                type: "select",
                options: roleList,
                dependsOn: "products",
                onChange: (option: any) => {
                  setFieldValue("roles", option.value);
                }
              },
              {
                name: "Package_Name",
                label: "Package Name",
                type: "select",
                options: packageList,
                dependsOn: "products",
                onChange: (option: any) => {
                  setFieldValue("Package_Name", option.value);
                }
              }
            ] : [])
          ]}
          defaultValues={values}
          dialogMode={dialogMode}
          isViewMode={dialogMode === "view"}
          isSubmitting={isSubmitting}
          buttonClass="bg-primary hover:bg-primary text-white"
        />
      )}
    </Formik>
  );
};

export default UserForm;