'use client';
import React from 'react';
import { Card } from '@/components/ui/card';
import AttractiveCard from '@/components/ui/attractive-card';
import { usePackage } from "@/components/mf/PackageContext";
import { useDateRange } from "@/components/mf/DateRangeContext";
import { useState } from 'react';
import {
  buildFilter,
  useFilter<PERSON>hangeHandler,
  FilterState,
} from  '../Filters/buildFilters';
import {useSummaryApi} from './apicallPage';

  
 type SummaryCardGroupProps = {
   params?: {
     package_name: string;
     start_date: string;
     end_date: string;
   };
   query?: {
     publisher: string[];
     campaign: string[];
     channel: string[];
     fraud_category: string[];
     fraud_sub_category: string[];
     creative_id: string[];
     sub_publisher: string[];
     campaign_id: string[];
   };
 };
 
 const SummaryCardGroup  :React.FC<SummaryCardGroupProps> = ({ params, query }) => {
      
  const { data, isLoading, error } = useSummaryApi({...params, ...query});
  const CardNamevalue = [
    { title: "Impressions", value: data?.impressions , borderColor: "5px 5px #065084" },
  ];

  const CardNamevalue2 = [
    { title: "Invalid Traffic", value: data?.invalid_impressions ||"0", percentage: data?.invalid_impressions_percentage||"0.0%", borderColor: "5px 5px #DC2525" },
    { title: "Brand Unsafe", value: data?.brandunsafe||"0", percentage: data?.brandunsafe_percentage||"0.0%", borderColor: "5px 5px #8A0000" },
    { title: "Clicks", value: data?.clicks||"0",percentage: data?.clicks_percentage||"0.0%", borderColor: "5px 5px #9B177E" },
    { title: "Viewability", value: data?.viewability||"0", percentage: data?.viewability_percentage||"0.0%", borderColor: "5px 5px #E8988A" },
    { title: "F-Cap Violation", value: data?.f_cap_violation||"0",percentage: data?.f_cap_violation_percentage||"0.0%", borderColor: "5px 5px #0ABAB5" },
  ];

 

  return (
    <div className="grid grid-cols-1 lg:grid-cols-6 gap-2 dark:bg-background ">

      {/* 🟦 Wrapper 1: Overall Summary + Card 1 + Card 2 */}
      <div className="col-span-1 lg:col-span-6 border border-gray-300 rounded-lg p-2 bg-white shadow dark:bg-card">
        <div className="text-md flex justify-center items-center font-semibold text-gray-800 bg-gray-100 px-3 py-2 rounded mb-2 dark:text-white  dark:bg-background">
          Overall Summary
        </div>

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="w-full lg:w-1/4">
            <Card className="shadow-md p-2 bg-gray-100 dark:text-white dark:bg-background">
              <div className="grid grid-rows-1 gap-4">
                {CardNamevalue.map((card, idx) => (
                  <AttractiveCard
                    key={idx}
                    title={card.title}
                    value={card.value}
                    width="w-full"
                    height="h-20"
                    borderColor={card.borderColor}
                    isLoading={isLoading}
                  />
                ))}
              </div>
            </Card>
          </div>

          <div className="w-full lg:w-3/4">
            <Card className="shadow-md p-2 bg-gray-100 dark:text-white dark:bg-background">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                {CardNamevalue2.map((card, idx) => (
                  <AttractiveCard
                    key={idx}
                    title={card.title}
                    value={card.value}
                    percentage={card.percentage}
                    width="w-full"
                    height="h-20"
                    borderColor={card.borderColor}
                    isLoading={isLoading}
                  />
                ))}
              </div>
            </Card>
          </div>
        </div>
      </div>

     
    </div>
  );
};

export default SummaryCardGroup;
