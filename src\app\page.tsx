"use client";
import Home from "../components/mf/login/home";
import FormCard from "@/components/mf/login/card";

export default function HomePage() {
  return (
    <>
      <Home
        InfoText="mFValid8, provides comprehensive, end-to-end protection against the dynamic threats of ad fraud by monitoring the entire funnel from impressions and clicks to landing page visits and lead or sale conversions. Our solution accurately identifies genuine users, helps in significantly reducing invalid traffic and blocking junk leads from entering your CRM. By ensuring your ads reach only genuine customers, we help in enhancing your marketing efficiency, improve ROAS, and lower CAC, resulting in more effective campaign performance and higher-quality conversions."
        logoSize="w-52"
        logoUrl="https://infringementportalcontent.mfilterit.com/images/media/logos/mfilterit-white-logo.png"
      >
        <FormCard />
      </Home>
    </>
  );
}
