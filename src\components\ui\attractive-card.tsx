"use client";
import React from "react";
import { Card } from "@/components/ui/card";
import {formatNumber} from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import Circular_progress from "./Circular_progress";
import { Loader2 } from "lucide-react";

interface AttractiveCardProps {
  title?: string;
  value?: string;
  width?: string;
  height?: string;
  borderColor?: string; // Can be Tailwind class or hex like "#D6D85D"
  titleColor?: string;
  titleFontSize?: string;
  valueColor?: string;
  valueFontSize?: string;
  className?: string;
  percentage?:string;
  circularPercentage?:number
  sizeRadial?:number;
  radialcolor?:string;
  isLoading?:boolean;
}

const AttractiveCard: React.FC<AttractiveCardProps> = ({
  title,
  value,
  width = "w-96",
  height = "h-48",
  borderColor = " 5px 5px #FACC15", // fallback to yellow-400
  titleColor = "text-black",
  titleFontSize = "text-body",
  valueColor = "text-black",
  valueFontSize = "text-lg",
  className = "",
  percentage,
  circularPercentage,
  sizeRadial=120,
  radialcolor="#FACC15",
  isLoading=false,
}) => {
  return (
    <Card
      className={`relative ${width} ${height} rounded-xl bg-white  overflow-hidden  dark:bg-card   ${className}`}style={{ boxShadow: borderColor }}
    >
      <div
        className="absolute top-0 right-0 h-full w-3 rounded-tr-xl rounded-br-xl"
        
      ></div>

      <div className="p-2 space-y-2">
        {title && (
          <div className={`${titleFontSize} font-semibold ${titleColor} flex justify-center items-center dark:text-white`}>
            {title}
          </div>
        )}
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <span className="text-small-font">
               <Loader2 className=" h-5 w-5 animate-spin text-primary" />
            </span>
          </div>
        ) :(
<div
  className={`flex items-center  font-semibold dark:text-white ${
    value && percentage ? "justify-between" : "justify-center"
  }`}
>
 {value && (
  <TooltipProvider>
    <Tooltip >
      <TooltipTrigger asChild>
        <div className={`${valueFontSize} font-semibold ${valueColor} dark:text-white`}>
          {formatNumber(Number(value))}
        </div>
      </TooltipTrigger>
      <TooltipContent>
        {Number(value).toLocaleString("en-US")}
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
)}
  {percentage && (
    <div className={`${valueFontSize} font-semibold ${valueColor} dark:text-white`}>
      {percentage}
      {/* <span className="text-sm p-1 font-semibold">%</span> */}
    </div>
  )}
  {circularPercentage && (
    <div className="flex items-center">
      <Circular_progress value={circularPercentage} size={sizeRadial} color={radialcolor} />
    </div>
  )}
</div>
        )}
        {/* {!value && !percentage && !circularPercentage && !isLoading && (
          <div className="flex items-center justify-center h-full">
            <span className="text-small-font">No Data Found!</span>
          </div>
        )} */}
      </div>
    </Card> 
  );
};

export default AttractiveCard;
