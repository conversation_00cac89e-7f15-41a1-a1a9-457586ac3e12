import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
  search_term:params.search_term,
  limit:params.limit,
  page:params.page,
});

export const useImpressionDistributionByTime = (params: any, enabled = true) =>
  useAppQuery(
    ['impressionDistributionByTime', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.IMPRESSION_DISTRIBUTION_BY_TIME}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );

  export const useLocationWiseTraffic = (params: any, enabled = true) =>
  useAppQuery(
    ['locationWiseTraffic', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.LOCATION_WISE_TRAFFIC}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );

