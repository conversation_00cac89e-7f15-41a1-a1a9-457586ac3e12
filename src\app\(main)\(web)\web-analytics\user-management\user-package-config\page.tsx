"use client";

import ResizableTable, { Column } from "@/components/mf/ReportingToolTable";
import React, { useState } from "react";
// import { useAPI } from "@/queries/useAPI";
// import Endpoint from "@/common/endpoint";
// import { PlayCircle } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import { Switch } from "@/components/ui/switch";
// import { MFDateRangePicker } from "@/components/mf";
//import AddUserForm from "../generate/page";
import { Form } from "@/components/ui/form";
//import { form } from "@/components/ui/form";
// import {
//   useQuery,
//   QueryClient,
//   QueryClientProvider,
// } from "@tanstack/react-query";
// import axios from "axios";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import GenerateReportModal from "../../ReportingTool/generate/page";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Checkbox } from "@/components/ui/checkbox";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
import { useRouter } from "next/navigation";
import JSZip from "jszip";
import { format, subDays } from "date-fns";
import { DateRange } from "react-day-picker";
//import { string } from "zod";

// Create a client
//const queryClient = new QueryClient();

interface CampaignRowData {
  platform: any;
  campaign_name: any;
  inserted_datetime: any;
  budget: number;
  update_status: any;
}

interface AdGrpRowData {
    id: number;
    package_name: string;
    package_title: string;
    role: string;
    action: boolean;
  }

interface KeywordData {
  keyword: any;
  platform: any;
  bid: any;
  match_type: any;
  ad_group_name: any;
  campaign_name: any;
  inserted_datetime: any;
  update_status: any;
}

interface productData {
  product_code: any;
  platform: any;
  ad_group_name: any;
  campaign_name: any;
  inserted_datetime: any;
  update_status: any;
}

interface RuleEngineData {
  cname: string;
  ad_grp_name: string;
  keyword: string;
  date_time: string;
  number_action: string;
  status: string;
  details: string;
}

interface RuleEngineDetails {
  budget_decrease?: string;
  campaign_status?: boolean;
  defaultOutputData?: boolean;
  rowError?: string | null;
  rowId?: number;
  ruleId?: string;
}

interface UserFormValues {
  name: string;
  email: string;
  role: string;
  phone: string;
  publisher?: string;
  fraudType?: string;
}

const CampaignOverviewPage: React.FC = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>("Campaign Overview Logs");
  const [RowCount, setRowCount] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<number | null>(null);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [emailTo, setEmailTo] = useState("");
  const [formType, setFormType] = useState<string>("AddUserForm");
  const [selectedReport, setSelectedReport] = useState<Record<
    string,
    string | number
  > | null>(null);
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>(
    {
      from: subDays(new Date(), 7),
      to: new Date(),
    }
  );
  const [tableData, setTableData] = useState<AdGrpRowData[]>([
    {
      id: 1,
      package_name: "com.amity_edu",
      package_title: "com.amity_edu",
      role: "customer",
      action: true,
    },
  ]);


   const routerAddPackage = useRouter() 
    const handleAddPackage = () => {
      router.push({
        pathname: "./generate",
        query: { form: "AddPackage"},
      } as any);
    };

  const handleStatusChange = (
    id: number,
    field: "Report_Status" | "Email_Status",
    checked: boolean
  ) => {
    setTableData((prevData) =>
      prevData.map((item) =>
        item.id === id ? { ...item, [field]: checked ? "True" : "False" } : item
      )
    );
  };

  const handleDelete = (item: Record<string, string | number>) => {
    setRowToDelete(item.id as number);
    setDeleteDialogOpen(true);
  };

  const handleClone = (item: Record<string, string | number>) => {
    const newId = Math.max(...tableData.map((item) => item.id)) + 1;
    const clonedItem = {
      ...tableData.find((row) => row.id === (item.id as number))!,
      id: newId,
      Report_Name: `${item.Report_Name}_copy`,
    };
    setTableData((prevData) => [clonedItem, ...prevData]);
  };

  const handleEdit = (item: Record<string, string | number>) => {
    router.push(`/webfraud/ReportingTool/generate?id=${item.id}`);
  };

  const confirmDelete = () => {
    if (rowToDelete) {
      setTableData((prevData) =>
        prevData.filter((item) => item.id !== rowToDelete)
      );
      setDeleteDialogOpen(false);
      setRowToDelete(null);
    }
  };

  const handleSend = (item: Record<string, string | number>) => {
    setSelectedReport(item);
    setEmailModalOpen(true);
  };

  const handleSendEmail = () => {
    // Here you would implement the actual email sending logic
    console.log("Sending email to:", emailTo, "for report:", selectedReport);
    // Reset the form
    setEmailTo("");
    setSelectedReport(null);
    setEmailModalOpen(false);
  };

//   const handleUserAdded = (newUser: UserFormValues) => {
//     console.log('New user:', newUser);
//     setTableData(prev => [
//       {
//         id: Math.max(...prev.map(u => u.id)) + 1,
//         Name: newUser.name,
//         Email: newUser.email,
//         Role: newUser.role,
//         Phone: newUser.phone,
//         Status: "active", // or whatever default status
//         Publisher: newUser.publisher || "",
//         Fraud_Type: newUser.fraudType || "-",
//         Vendor: ""
//       },
//       ...prev
//     ]);
//   };

  const handleDownload = async (items: Record<string, string | number>[]) => {
    try {
      // Create a new instance of JSZip
      const zip = new JSZip();

      // Fetch the dummy.csv file
      const response = await fetch("/dummy.csv");
      const csvData = await response.blob();

      // Add the CSV to the zip file
      zip.file("dummy.csv", csvData);

      // Generate the zip file
      const zipContent = await zip.generateAsync({ type: "blob" });

      // Create a download link
      const link = document.createElement("a");
      const currentDate = format(new Date(), "yyyyMMdd");
      const fileName = `web.mfilterit.cpv_${currentDate}.zip`;

      link.href = URL.createObjectURL(zipContent);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  const adgrpcolumns: Column<AdGrpRowData>[] = [
    {
      title: "Package Name",
      key: "package_name",
      render: (data: AdGrpRowData) => (
        <div className="text-left font-">{data.package_name}</div>
      ),
    },
    {
      title: "Package Title",
      key: "package_title",
      render: (data: AdGrpRowData) => (
        <div className="text-left">{data.package_title}</div>
      ),
    },
    {
      title: "Role",
      key: "role",
      render: (data: AdGrpRowData) => (
        <div className="text-left">{data.role}</div>
      ),
    },
   
  ];


  
  // const campaignQuery = {
  //   platform: ["all"],
  //   campaign_type: ["all"],
  //   campaign_name: ["all"],
  //   status: ["all"],
  // };

  // const campaignOverviewLogs = useAPI<any[], { message: string }>({
  //   tag: "campaignOverviewLogs",
  //   options: {
  //     url: process.env.NEXT_PUBLIC_UAM_DOMAIN + Endpoint.UAM.CAMPAIGN_LOGS,
  //     method: "POST",
  //     data: campaignQuery,
  //   },
  // });

  const handleRefetch = (params?: { startDate?: Date; endDate?: Date }) => {
    if (params?.startDate && params?.endDate) {
      console.log(
        "Refetching data for range:",
        params.startDate,
        params.endDate
      );
      // Here you can add your API call or data fetching logic
      // Example:
      // const formattedStartDate = params.startDate.toISOString();
      // const formattedEndDate = params.endDate.toISOString();
      // fetchData({ startDate: formattedStartDate, endDate: formattedEndDate });
    }
  };

  return (
    <div className="space-y-6">
    {/* <Form {...form}> */}
      {/* <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        ...
      </form> */}
    {/* </Form> */}
    
  </div>
    // <QueryClientProvider client={queryClient}>
    //   <div className="relative bg-card">
    //     <div className="p-4 [&_td]:text-[14px] [&_th]:text-[14px]">
    //       <ResizableTable
    //         columns={adgrpcolumns ?? []}
    //         isgenerateTable={false}
    //         isUserTable={false}
    //         isUserPackage={false}
    //         data={tableData}
    //         isTableDownload={false}
    //         isLoading={false}
    //         isDownload={false}
    //         headerColor="#DCDCDC"
    //         itemCount={setRowCount}
    //         isSearchable={false}
    //         SearchTerm=""
    //         setSearchTerm={() => {}}
    //         isRefetch={false}
    //         onRefetch={handleRefetch}
    //         // handleAddUser={false}
    //         // handleAddPackage={handleAddPackage}
    //         isEdit={true}
    //         onEdit={handleEdit}
    //         isSend={false}
    //         isView={false}
    //         onView={handleEdit}
    //         onSend={handleSend}
    //         isClone={false}
    //         onClone={handleClone}
    //         isSelectable={false}
    //         isDelete={true}
    //         onDelete={handleDelete}
    //         onDownloadAll={handleDownload}
    //       />
    //     </div>
    //     <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
    //       <DialogContent>
    //         <DialogHeader>
    //           <DialogTitle>Confirm Delete</DialogTitle>
    //         </DialogHeader>
    //         <div className="py-4">
    //           Are you sure you want to delete this report?
    //         </div>
    //         <DialogFooter>
    //           <Button
    //             variant="secondary"
    //             onClick={() => setDeleteDialogOpen(false)}
    //             className="text-white bg-primary hover:bg-primary/90"
    //           >
    //             Cancel
    //           </Button>
    //           <Button
    //             variant="secondary"
    //             onClick={confirmDelete}
    //             className="text-white bg-primary hover:bg-primary/90"
    //           >
    //             Delete
    //           </Button>
    //         </DialogFooter>
    //       </DialogContent>
    //     </Dialog>

    //     <Dialog open={emailModalOpen} onOpenChange={setEmailModalOpen}>
    //       <DialogContent>
    //         <DialogHeader>
    //           <DialogTitle>Share Report via Email</DialogTitle>
    //         </DialogHeader>
    //         <div className="py-4 space-y-4">
    //           <div className="space-y-2">
    //             <Label htmlFor="email">Email Address</Label>
    //             <Input
    //               id="email"
    //               type="email"
    //               placeholder="Enter recipient's email"
    //               value={emailTo}
    //               onChange={(e) => setEmailTo(e.target.value)}
    //             />
    //           </div>
    //           {selectedReport && (
    //             <div className="text-sm text-muted-foreground">
    //               Sharing report: {selectedReport.Report_Name}
    //             </div>
    //           )}
    //         </div>
    //         <DialogFooter>
    //           <Button
    //             variant="secondary"
    //             onClick={() => setEmailModalOpen(false)}
    //             className="text-white bg-primary hover:bg-primary/90"
    //           >
    //             Cancel
    //           </Button>
    //           <Button
    //             variant="secondary"
    //             onClick={handleSendEmail}
    //             disabled={!emailTo || !selectedReport}
    //             className="text-white bg-primary hover:bg-primary/90 disabled:bg-primary/50"
    //           >
    //             Send
    //           </Button>
    //         </DialogFooter>
    //       </DialogContent>
    //     </Dialog>
    //   </div>
    // </QueryClientProvider>
  );
};

export default CampaignOverviewPage;