"use client"
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ar<PERSON>hart, RadialBar, PolarAngleAxis, ResponsiveContainer, Label, PolarRadiusAxis } from "recharts";
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
  CardHeader,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Loader2 } from "lucide-react";
import HeaderRow from "../HeaderRow";
import { useFullscreen } from "@/hooks/use-fullscreen";

interface RadialChartProps {
    chartData?: { label?: string; [key: string]: string | number | undefined }[];
    chartConfig?: {
      [key: string]: {
        label: string;
        color: string;
      };
  };
  Device?:string;
  Vname?:string;
  Vvalue?:number;
  Oname?:string;
  Ovalue?:number;
  backgroundcolors?:string;
  textcolors?:string;
  value1?:string;
  value2?:string;
  isLoading?:boolean;
  isPercentage?:boolean;
  selectedFrequency?:string;
  placeholder?:string;
  handleExport?: () => void;
  onExpand: (key: string) => void;
  onExport?: (format: string, title: string, key: string) => void;
  title?: string;
  isSelect?: boolean;
  isRadioButton?: boolean;
  handleFrequencyChange?: (value: string) => void; 
  selectoptions?:string[];
  visitEventOptions?: { value: string; label: string }[];
  handleTypeChange?: (value: string) => void;
  selectedType?: string;
  heading?:string;
  isHeader?:boolean;
  isCardTitle?:boolean;
};

const RadialBars: React.FC<RadialChartProps> = ({
    heading ="heading",
    //sub_heading,
      handleTypeChange,
      visitEventOptions,
      selectedType,
      handleExport,
      onExport,
      selectoptions =[],
      onExpand,
      handleFrequencyChange,
      title ,
      isSelect= false,
      isRadioButton =false,
      isHeader=true,
      isCardTitle=false,
    chartConfig = {},
    chartData,
    Device,
    Vname,
    Vvalue,
    Oname,
    value1,
    value2,
    Ovalue,
    backgroundcolors,
    textcolors,
    isLoading,
    isPercentage,
    selectedFrequency,
    placeholder,
}) => {
    const [data, setData] = useState<{ label?: string; [key: string]: string | number | undefined }[]>([]);
            const isFullscreen = useFullscreen();

    // Calculate responsive chart dimensions based on fullscreen state
    const getChartDimensions = () => {
        if (isFullscreen) {
            return {
                maxWidth: "400px",
                innerRadius: 120,
                outerRadius: 180
            };
        }
        return {
            maxWidth: "200px",
            innerRadius: 80,
            outerRadius: 120
        };
    };

    const chartDimensions = getChartDimensions();

    useEffect(() => {
        if (chartData) {
            setData(chartData);
        } else {
            setData([]);
        }
    }, [chartData]);
    
    // Check if there's meaningful data (non-zero values)
    const hasMeaningfulData = React.useMemo(() => {
        if (!data || data.length === 0) return false;
        
        return data.some(item => {
            const visitValue = parseFloat(String(item[value1 || "Visit %"] || "0").replace('%', ''));
            const eventValue = parseFloat(String(item[value2 || "Event %"] || "0").replace('%', ''));
            return visitValue > 0 || eventValue > 0;
        });
    }, [data, value1, value2]);

    return (
        <Card className={`flex flex-col rounded-none ${isFullscreen ? 'max-h-[600px]' : 'max-h-[280px]'}`}>
            
                {isHeader && (
                    <div className="p-2">
                <HeaderRow
      visitEventOptions={visitEventOptions}
      handleTypeChange={handleTypeChange}
      selectoptions={selectoptions}
      selectedType={selectedType}
      title={title}
      handleFrequencyChange={handleFrequencyChange}
      selectedFrequency={selectedFrequency}
      onExpand={onExpand}
      handleExport={handleExport}
      isRadioButton={isRadioButton}
      isSelect={isSelect}
      onExport={onExport}
      textcolors={textcolors}
      heading={heading}
      placeholder={placeholder}
              

/>
</div>
 ) }
            
            {isCardTitle && (
               <CardHeader className="items-center p- pb-0">
                <CardTitle className={`font-semibold p-2 ${isFullscreen ? 'text-xl' : 'text-body'}`} style={{ color: textcolors }}>
                    {Device}
                </CardTitle>
            </CardHeader>
        )}
            <CardContent className={`relative flex-1 ${isFullscreen ? 'min-h-[400px]' : 'min-h-[150px]'}`}>
                {isLoading ? (
                    <div className="absolute inset-0 flex items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                ) : (
                    <ChartContainer
                        config={chartConfig}
                        className={`mx-auto aspect-square w-full ${isFullscreen ? 'max-w-[400px]' : 'max-w-[200px]'}`}
                    >
                        {hasMeaningfulData ? (
                            <RadialBarChart
                                data={data}
                                endAngle={180}
                                innerRadius={chartDimensions.innerRadius}
                                outerRadius={chartDimensions.outerRadius}
                            >
                                <ChartTooltip
                                    cursor={false}
                                    content={<ChartTooltipContent hideLabel isPercentage={isPercentage}/>}
                                />
                                <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                                    <Label content={({ viewBox }) => {
                                        // Return null for now - this can be customized later
                                        return null;
                                    }}/>
                                </PolarRadiusAxis>
                                <RadialBar
                                    dataKey={value1 || "Visit %"}
                                    stackId="a"
                                    cornerRadius={isFullscreen ? 8 : 5}
                                    fill={chartConfig[value1 || "Visit %"]?.color}
                                    className="stroke-transparent stroke-2"
                                />
                                <RadialBar
                                    dataKey={value2 || "Event %"}
                                    fill={chartConfig[value2 || "Event %"]?.color}
                                    stackId="a"
                                    cornerRadius={isFullscreen ? 8 : 5}
                                    className="stroke-transparent stroke-2"
                                />
                            </RadialBarChart>
                        ) : (
                            <div className="flex items-center justify-center h-full">
                                <span className={`${isFullscreen ? 'text-lg' : 'text-small-font'}`}>No Data Found!</span>
                            </div>
                        )}
                    </ChartContainer>
                )}
            </CardContent>
            <CardFooter className={`${isFullscreen ? 'pb-8' : 'pb-5'}`}>
                <div className={`${backgroundcolors} w-full`} style={{ color: textcolors }}>
                    <div className={`flex items-center justify-center gap-4 font-semibold ${isFullscreen ? 'text-lg gap-8' : 'text-body'}`}>
                        <div className="flex items-center gap-2">
                            <div 
                                className={`rounded-full ${isFullscreen ? 'w-4 h-4' : 'w-3 h-3'}`}
                                style={{ backgroundColor: chartConfig[value1 || "Visit %"]?.color || "#000" }}
                            />
                            <span>{Vname}: {isLoading ? "0" : (Vvalue ?? 0)}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div 
                                className={`rounded-full ${isFullscreen ? 'w-4 h-4' : 'w-3 h-3'}`}
                                style={{ backgroundColor: chartConfig[value2 || "Event %"]?.color || "#000" }}
                            />
                            <span>{Oname}: {isLoading ? "0" : (Ovalue ?? 0)}%</span>
                        </div>
                    </div>
                </div>
            </CardFooter>
        </Card>
    );
};
export default RadialBars;
