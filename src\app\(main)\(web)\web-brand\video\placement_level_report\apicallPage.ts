
import Endpoint from '../../../common/endpoint';
import { apiCall, useAppQuery } from '../../../queries/useAppQuery';


const getDefaultPayload = (params: any) => ({
  ...params,
  publisher: params.publisher,
  campaign: params.campaign,
  channel: params.channel,
  fraud_category:params.fraud_category,
  fraud_sub_category:params.fraud_sub_category,
  creative_id:params.creative_id,
  sub_publisher:params.sub_publisher,
  campaign_id:params.campaign_id,
  search_term:params.search_term,
  limit:params.limit,
  page:params.page,
});

export const usePlacementReport = (params: any, enabled = true) =>
  useAppQuery(
    ['placementReport', JSON.stringify(params)],
    () =>
      apiCall({
        url: `${process.env.NEXT_PUBLIC_WEB_BRAND}${Endpoint.WebBrand.SHOW_WISE_IMPRESSION}`,
        method: 'POST',
        data:getDefaultPayload(params),
      }),
    { enabled }
  );
  